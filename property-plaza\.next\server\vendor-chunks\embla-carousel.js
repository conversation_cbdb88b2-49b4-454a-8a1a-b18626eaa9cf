"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/embla-carousel";
exports.ids = ["vendor-chunks/embla-carousel"];
exports.modules = {

/***/ "(ssr)/./node_modules/embla-carousel/esm/embla-carousel.esm.js":
/*!***************************************************************!*\
  !*** ./node_modules/embla-carousel/esm/embla-carousel.esm.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EmblaCarousel)\n/* harmony export */ });\nfunction isNumber(subject) {\n  return typeof subject === 'number';\n}\nfunction isString(subject) {\n  return typeof subject === 'string';\n}\nfunction isBoolean(subject) {\n  return typeof subject === 'boolean';\n}\nfunction isObject(subject) {\n  return Object.prototype.toString.call(subject) === '[object Object]';\n}\nfunction mathAbs(n) {\n  return Math.abs(n);\n}\nfunction mathSign(n) {\n  return Math.sign(n);\n}\nfunction deltaAbs(valueB, valueA) {\n  return mathAbs(valueB - valueA);\n}\nfunction factorAbs(valueB, valueA) {\n  if (valueB === 0 || valueA === 0) return 0;\n  if (mathAbs(valueB) <= mathAbs(valueA)) return 0;\n  const diff = deltaAbs(mathAbs(valueB), mathAbs(valueA));\n  return mathAbs(diff / valueB);\n}\nfunction roundToTwoDecimals(num) {\n  return Math.round(num * 100) / 100;\n}\nfunction arrayKeys(array) {\n  return objectKeys(array).map(Number);\n}\nfunction arrayLast(array) {\n  return array[arrayLastIndex(array)];\n}\nfunction arrayLastIndex(array) {\n  return Math.max(0, array.length - 1);\n}\nfunction arrayIsLastIndex(array, index) {\n  return index === arrayLastIndex(array);\n}\nfunction arrayFromNumber(n, startAt = 0) {\n  return Array.from(Array(n), (_, i) => startAt + i);\n}\nfunction objectKeys(object) {\n  return Object.keys(object);\n}\nfunction objectsMergeDeep(objectA, objectB) {\n  return [objectA, objectB].reduce((mergedObjects, currentObject) => {\n    objectKeys(currentObject).forEach(key => {\n      const valueA = mergedObjects[key];\n      const valueB = currentObject[key];\n      const areObjects = isObject(valueA) && isObject(valueB);\n      mergedObjects[key] = areObjects ? objectsMergeDeep(valueA, valueB) : valueB;\n    });\n    return mergedObjects;\n  }, {});\n}\nfunction isMouseEvent(evt, ownerWindow) {\n  return typeof ownerWindow.MouseEvent !== 'undefined' && evt instanceof ownerWindow.MouseEvent;\n}\n\nfunction Alignment(align, viewSize) {\n  const predefined = {\n    start,\n    center,\n    end\n  };\n  function start() {\n    return 0;\n  }\n  function center(n) {\n    return end(n) / 2;\n  }\n  function end(n) {\n    return viewSize - n;\n  }\n  function measure(n, index) {\n    if (isString(align)) return predefined[align](n);\n    return align(viewSize, n, index);\n  }\n  const self = {\n    measure\n  };\n  return self;\n}\n\nfunction EventStore() {\n  let listeners = [];\n  function add(node, type, handler, options = {\n    passive: true\n  }) {\n    let removeListener;\n    if ('addEventListener' in node) {\n      node.addEventListener(type, handler, options);\n      removeListener = () => node.removeEventListener(type, handler, options);\n    } else {\n      const legacyMediaQueryList = node;\n      legacyMediaQueryList.addListener(handler);\n      removeListener = () => legacyMediaQueryList.removeListener(handler);\n    }\n    listeners.push(removeListener);\n    return self;\n  }\n  function clear() {\n    listeners = listeners.filter(remove => remove());\n  }\n  const self = {\n    add,\n    clear\n  };\n  return self;\n}\n\nfunction Animations(ownerDocument, ownerWindow, update, render) {\n  const documentVisibleHandler = EventStore();\n  const fixedTimeStep = 1000 / 60;\n  let lastTimeStamp = null;\n  let accumulatedTime = 0;\n  let animationId = 0;\n  function init() {\n    documentVisibleHandler.add(ownerDocument, 'visibilitychange', () => {\n      if (ownerDocument.hidden) reset();\n    });\n  }\n  function destroy() {\n    stop();\n    documentVisibleHandler.clear();\n  }\n  function animate(timeStamp) {\n    if (!animationId) return;\n    if (!lastTimeStamp) {\n      lastTimeStamp = timeStamp;\n      update();\n      update();\n    }\n    const timeElapsed = timeStamp - lastTimeStamp;\n    lastTimeStamp = timeStamp;\n    accumulatedTime += timeElapsed;\n    while (accumulatedTime >= fixedTimeStep) {\n      update();\n      accumulatedTime -= fixedTimeStep;\n    }\n    const alpha = accumulatedTime / fixedTimeStep;\n    render(alpha);\n    if (animationId) {\n      animationId = ownerWindow.requestAnimationFrame(animate);\n    }\n  }\n  function start() {\n    if (animationId) return;\n    animationId = ownerWindow.requestAnimationFrame(animate);\n  }\n  function stop() {\n    ownerWindow.cancelAnimationFrame(animationId);\n    lastTimeStamp = null;\n    accumulatedTime = 0;\n    animationId = 0;\n  }\n  function reset() {\n    lastTimeStamp = null;\n    accumulatedTime = 0;\n  }\n  const self = {\n    init,\n    destroy,\n    start,\n    stop,\n    update,\n    render\n  };\n  return self;\n}\n\nfunction Axis(axis, contentDirection) {\n  const isRightToLeft = contentDirection === 'rtl';\n  const isVertical = axis === 'y';\n  const scroll = isVertical ? 'y' : 'x';\n  const cross = isVertical ? 'x' : 'y';\n  const sign = !isVertical && isRightToLeft ? -1 : 1;\n  const startEdge = getStartEdge();\n  const endEdge = getEndEdge();\n  function measureSize(nodeRect) {\n    const {\n      height,\n      width\n    } = nodeRect;\n    return isVertical ? height : width;\n  }\n  function getStartEdge() {\n    if (isVertical) return 'top';\n    return isRightToLeft ? 'right' : 'left';\n  }\n  function getEndEdge() {\n    if (isVertical) return 'bottom';\n    return isRightToLeft ? 'left' : 'right';\n  }\n  function direction(n) {\n    return n * sign;\n  }\n  const self = {\n    scroll,\n    cross,\n    startEdge,\n    endEdge,\n    measureSize,\n    direction\n  };\n  return self;\n}\n\nfunction Limit(min = 0, max = 0) {\n  const length = mathAbs(min - max);\n  function reachedMin(n) {\n    return n < min;\n  }\n  function reachedMax(n) {\n    return n > max;\n  }\n  function reachedAny(n) {\n    return reachedMin(n) || reachedMax(n);\n  }\n  function constrain(n) {\n    if (!reachedAny(n)) return n;\n    return reachedMin(n) ? min : max;\n  }\n  function removeOffset(n) {\n    if (!length) return n;\n    return n - length * Math.ceil((n - max) / length);\n  }\n  const self = {\n    length,\n    max,\n    min,\n    constrain,\n    reachedAny,\n    reachedMax,\n    reachedMin,\n    removeOffset\n  };\n  return self;\n}\n\nfunction Counter(max, start, loop) {\n  const {\n    constrain\n  } = Limit(0, max);\n  const loopEnd = max + 1;\n  let counter = withinLimit(start);\n  function withinLimit(n) {\n    return !loop ? constrain(n) : mathAbs((loopEnd + n) % loopEnd);\n  }\n  function get() {\n    return counter;\n  }\n  function set(n) {\n    counter = withinLimit(n);\n    return self;\n  }\n  function add(n) {\n    return clone().set(get() + n);\n  }\n  function clone() {\n    return Counter(max, get(), loop);\n  }\n  const self = {\n    get,\n    set,\n    add,\n    clone\n  };\n  return self;\n}\n\nfunction DragHandler(axis, rootNode, ownerDocument, ownerWindow, target, dragTracker, location, animation, scrollTo, scrollBody, scrollTarget, index, eventHandler, percentOfView, dragFree, dragThreshold, skipSnaps, baseFriction, watchDrag) {\n  const {\n    cross: crossAxis,\n    direction\n  } = axis;\n  const focusNodes = ['INPUT', 'SELECT', 'TEXTAREA'];\n  const nonPassiveEvent = {\n    passive: false\n  };\n  const initEvents = EventStore();\n  const dragEvents = EventStore();\n  const goToNextThreshold = Limit(50, 225).constrain(percentOfView.measure(20));\n  const snapForceBoost = {\n    mouse: 300,\n    touch: 400\n  };\n  const freeForceBoost = {\n    mouse: 500,\n    touch: 600\n  };\n  const baseSpeed = dragFree ? 43 : 25;\n  let isMoving = false;\n  let startScroll = 0;\n  let startCross = 0;\n  let pointerIsDown = false;\n  let preventScroll = false;\n  let preventClick = false;\n  let isMouse = false;\n  function init(emblaApi) {\n    if (!watchDrag) return;\n    function downIfAllowed(evt) {\n      if (isBoolean(watchDrag) || watchDrag(emblaApi, evt)) down(evt);\n    }\n    const node = rootNode;\n    initEvents.add(node, 'dragstart', evt => evt.preventDefault(), nonPassiveEvent).add(node, 'touchmove', () => undefined, nonPassiveEvent).add(node, 'touchend', () => undefined).add(node, 'touchstart', downIfAllowed).add(node, 'mousedown', downIfAllowed).add(node, 'touchcancel', up).add(node, 'contextmenu', up).add(node, 'click', click, true);\n  }\n  function destroy() {\n    initEvents.clear();\n    dragEvents.clear();\n  }\n  function addDragEvents() {\n    const node = isMouse ? ownerDocument : rootNode;\n    dragEvents.add(node, 'touchmove', move, nonPassiveEvent).add(node, 'touchend', up).add(node, 'mousemove', move, nonPassiveEvent).add(node, 'mouseup', up);\n  }\n  function isFocusNode(node) {\n    const nodeName = node.nodeName || '';\n    return focusNodes.includes(nodeName);\n  }\n  function forceBoost() {\n    const boost = dragFree ? freeForceBoost : snapForceBoost;\n    const type = isMouse ? 'mouse' : 'touch';\n    return boost[type];\n  }\n  function allowedForce(force, targetChanged) {\n    const next = index.add(mathSign(force) * -1);\n    const baseForce = scrollTarget.byDistance(force, !dragFree).distance;\n    if (dragFree || mathAbs(force) < goToNextThreshold) return baseForce;\n    if (skipSnaps && targetChanged) return baseForce * 0.5;\n    return scrollTarget.byIndex(next.get(), 0).distance;\n  }\n  function down(evt) {\n    const isMouseEvt = isMouseEvent(evt, ownerWindow);\n    isMouse = isMouseEvt;\n    preventClick = dragFree && isMouseEvt && !evt.buttons && isMoving;\n    isMoving = deltaAbs(target.get(), location.get()) >= 2;\n    if (isMouseEvt && evt.button !== 0) return;\n    if (isFocusNode(evt.target)) return;\n    pointerIsDown = true;\n    dragTracker.pointerDown(evt);\n    scrollBody.useFriction(0).useDuration(0);\n    target.set(location);\n    addDragEvents();\n    startScroll = dragTracker.readPoint(evt);\n    startCross = dragTracker.readPoint(evt, crossAxis);\n    eventHandler.emit('pointerDown');\n  }\n  function move(evt) {\n    const isTouchEvt = !isMouseEvent(evt, ownerWindow);\n    if (isTouchEvt && evt.touches.length >= 2) return up(evt);\n    const lastScroll = dragTracker.readPoint(evt);\n    const lastCross = dragTracker.readPoint(evt, crossAxis);\n    const diffScroll = deltaAbs(lastScroll, startScroll);\n    const diffCross = deltaAbs(lastCross, startCross);\n    if (!preventScroll && !isMouse) {\n      if (!evt.cancelable) return up(evt);\n      preventScroll = diffScroll > diffCross;\n      if (!preventScroll) return up(evt);\n    }\n    const diff = dragTracker.pointerMove(evt);\n    if (diffScroll > dragThreshold) preventClick = true;\n    scrollBody.useFriction(0.3).useDuration(0.75);\n    animation.start();\n    target.add(direction(diff));\n    evt.preventDefault();\n  }\n  function up(evt) {\n    const currentLocation = scrollTarget.byDistance(0, false);\n    const targetChanged = currentLocation.index !== index.get();\n    const rawForce = dragTracker.pointerUp(evt) * forceBoost();\n    const force = allowedForce(direction(rawForce), targetChanged);\n    const forceFactor = factorAbs(rawForce, force);\n    const speed = baseSpeed - 10 * forceFactor;\n    const friction = baseFriction + forceFactor / 50;\n    preventScroll = false;\n    pointerIsDown = false;\n    dragEvents.clear();\n    scrollBody.useDuration(speed).useFriction(friction);\n    scrollTo.distance(force, !dragFree);\n    isMouse = false;\n    eventHandler.emit('pointerUp');\n  }\n  function click(evt) {\n    if (preventClick) {\n      evt.stopPropagation();\n      evt.preventDefault();\n      preventClick = false;\n    }\n  }\n  function pointerDown() {\n    return pointerIsDown;\n  }\n  const self = {\n    init,\n    destroy,\n    pointerDown\n  };\n  return self;\n}\n\nfunction DragTracker(axis, ownerWindow) {\n  const logInterval = 170;\n  let startEvent;\n  let lastEvent;\n  function readTime(evt) {\n    return evt.timeStamp;\n  }\n  function readPoint(evt, evtAxis) {\n    const property = evtAxis || axis.scroll;\n    const coord = `client${property === 'x' ? 'X' : 'Y'}`;\n    return (isMouseEvent(evt, ownerWindow) ? evt : evt.touches[0])[coord];\n  }\n  function pointerDown(evt) {\n    startEvent = evt;\n    lastEvent = evt;\n    return readPoint(evt);\n  }\n  function pointerMove(evt) {\n    const diff = readPoint(evt) - readPoint(lastEvent);\n    const expired = readTime(evt) - readTime(startEvent) > logInterval;\n    lastEvent = evt;\n    if (expired) startEvent = evt;\n    return diff;\n  }\n  function pointerUp(evt) {\n    if (!startEvent || !lastEvent) return 0;\n    const diffDrag = readPoint(lastEvent) - readPoint(startEvent);\n    const diffTime = readTime(evt) - readTime(startEvent);\n    const expired = readTime(evt) - readTime(lastEvent) > logInterval;\n    const force = diffDrag / diffTime;\n    const isFlick = diffTime && !expired && mathAbs(force) > 0.1;\n    return isFlick ? force : 0;\n  }\n  const self = {\n    pointerDown,\n    pointerMove,\n    pointerUp,\n    readPoint\n  };\n  return self;\n}\n\nfunction NodeRects() {\n  function measure(node) {\n    const {\n      offsetTop,\n      offsetLeft,\n      offsetWidth,\n      offsetHeight\n    } = node;\n    const offset = {\n      top: offsetTop,\n      right: offsetLeft + offsetWidth,\n      bottom: offsetTop + offsetHeight,\n      left: offsetLeft,\n      width: offsetWidth,\n      height: offsetHeight\n    };\n    return offset;\n  }\n  const self = {\n    measure\n  };\n  return self;\n}\n\nfunction PercentOfView(viewSize) {\n  function measure(n) {\n    return viewSize * (n / 100);\n  }\n  const self = {\n    measure\n  };\n  return self;\n}\n\nfunction ResizeHandler(container, eventHandler, ownerWindow, slides, axis, watchResize, nodeRects) {\n  const observeNodes = [container].concat(slides);\n  let resizeObserver;\n  let containerSize;\n  let slideSizes = [];\n  let destroyed = false;\n  function readSize(node) {\n    return axis.measureSize(nodeRects.measure(node));\n  }\n  function init(emblaApi) {\n    if (!watchResize) return;\n    containerSize = readSize(container);\n    slideSizes = slides.map(readSize);\n    function defaultCallback(entries) {\n      for (const entry of entries) {\n        if (destroyed) return;\n        const isContainer = entry.target === container;\n        const slideIndex = slides.indexOf(entry.target);\n        const lastSize = isContainer ? containerSize : slideSizes[slideIndex];\n        const newSize = readSize(isContainer ? container : slides[slideIndex]);\n        const diffSize = mathAbs(newSize - lastSize);\n        if (diffSize >= 0.5) {\n          emblaApi.reInit();\n          eventHandler.emit('resize');\n          break;\n        }\n      }\n    }\n    resizeObserver = new ResizeObserver(entries => {\n      if (isBoolean(watchResize) || watchResize(emblaApi, entries)) {\n        defaultCallback(entries);\n      }\n    });\n    ownerWindow.requestAnimationFrame(() => {\n      observeNodes.forEach(node => resizeObserver.observe(node));\n    });\n  }\n  function destroy() {\n    destroyed = true;\n    if (resizeObserver) resizeObserver.disconnect();\n  }\n  const self = {\n    init,\n    destroy\n  };\n  return self;\n}\n\nfunction ScrollBody(location, offsetLocation, previousLocation, target, baseDuration, baseFriction) {\n  let scrollVelocity = 0;\n  let scrollDirection = 0;\n  let scrollDuration = baseDuration;\n  let scrollFriction = baseFriction;\n  let rawLocation = location.get();\n  let rawLocationPrevious = 0;\n  function seek() {\n    const displacement = target.get() - location.get();\n    const isInstant = !scrollDuration;\n    let scrollDistance = 0;\n    if (isInstant) {\n      scrollVelocity = 0;\n      previousLocation.set(target);\n      location.set(target);\n      scrollDistance = displacement;\n    } else {\n      previousLocation.set(location);\n      scrollVelocity += displacement / scrollDuration;\n      scrollVelocity *= scrollFriction;\n      rawLocation += scrollVelocity;\n      location.add(scrollVelocity);\n      scrollDistance = rawLocation - rawLocationPrevious;\n    }\n    scrollDirection = mathSign(scrollDistance);\n    rawLocationPrevious = rawLocation;\n    return self;\n  }\n  function settled() {\n    const diff = target.get() - offsetLocation.get();\n    return mathAbs(diff) < 0.001;\n  }\n  function duration() {\n    return scrollDuration;\n  }\n  function direction() {\n    return scrollDirection;\n  }\n  function velocity() {\n    return scrollVelocity;\n  }\n  function useBaseDuration() {\n    return useDuration(baseDuration);\n  }\n  function useBaseFriction() {\n    return useFriction(baseFriction);\n  }\n  function useDuration(n) {\n    scrollDuration = n;\n    return self;\n  }\n  function useFriction(n) {\n    scrollFriction = n;\n    return self;\n  }\n  const self = {\n    direction,\n    duration,\n    velocity,\n    seek,\n    settled,\n    useBaseFriction,\n    useBaseDuration,\n    useFriction,\n    useDuration\n  };\n  return self;\n}\n\nfunction ScrollBounds(limit, location, target, scrollBody, percentOfView) {\n  const pullBackThreshold = percentOfView.measure(10);\n  const edgeOffsetTolerance = percentOfView.measure(50);\n  const frictionLimit = Limit(0.1, 0.99);\n  let disabled = false;\n  function shouldConstrain() {\n    if (disabled) return false;\n    if (!limit.reachedAny(target.get())) return false;\n    if (!limit.reachedAny(location.get())) return false;\n    return true;\n  }\n  function constrain(pointerDown) {\n    if (!shouldConstrain()) return;\n    const edge = limit.reachedMin(location.get()) ? 'min' : 'max';\n    const diffToEdge = mathAbs(limit[edge] - location.get());\n    const diffToTarget = target.get() - location.get();\n    const friction = frictionLimit.constrain(diffToEdge / edgeOffsetTolerance);\n    target.subtract(diffToTarget * friction);\n    if (!pointerDown && mathAbs(diffToTarget) < pullBackThreshold) {\n      target.set(limit.constrain(target.get()));\n      scrollBody.useDuration(25).useBaseFriction();\n    }\n  }\n  function toggleActive(active) {\n    disabled = !active;\n  }\n  const self = {\n    shouldConstrain,\n    constrain,\n    toggleActive\n  };\n  return self;\n}\n\nfunction ScrollContain(viewSize, contentSize, snapsAligned, containScroll, pixelTolerance) {\n  const scrollBounds = Limit(-contentSize + viewSize, 0);\n  const snapsBounded = measureBounded();\n  const scrollContainLimit = findScrollContainLimit();\n  const snapsContained = measureContained();\n  function usePixelTolerance(bound, snap) {\n    return deltaAbs(bound, snap) <= 1;\n  }\n  function findScrollContainLimit() {\n    const startSnap = snapsBounded[0];\n    const endSnap = arrayLast(snapsBounded);\n    const min = snapsBounded.lastIndexOf(startSnap);\n    const max = snapsBounded.indexOf(endSnap) + 1;\n    return Limit(min, max);\n  }\n  function measureBounded() {\n    return snapsAligned.map((snapAligned, index) => {\n      const {\n        min,\n        max\n      } = scrollBounds;\n      const snap = scrollBounds.constrain(snapAligned);\n      const isFirst = !index;\n      const isLast = arrayIsLastIndex(snapsAligned, index);\n      if (isFirst) return max;\n      if (isLast) return min;\n      if (usePixelTolerance(min, snap)) return min;\n      if (usePixelTolerance(max, snap)) return max;\n      return snap;\n    }).map(scrollBound => parseFloat(scrollBound.toFixed(3)));\n  }\n  function measureContained() {\n    if (contentSize <= viewSize + pixelTolerance) return [scrollBounds.max];\n    if (containScroll === 'keepSnaps') return snapsBounded;\n    const {\n      min,\n      max\n    } = scrollContainLimit;\n    return snapsBounded.slice(min, max);\n  }\n  const self = {\n    snapsContained,\n    scrollContainLimit\n  };\n  return self;\n}\n\nfunction ScrollLimit(contentSize, scrollSnaps, loop) {\n  const max = scrollSnaps[0];\n  const min = loop ? max - contentSize : arrayLast(scrollSnaps);\n  const limit = Limit(min, max);\n  const self = {\n    limit\n  };\n  return self;\n}\n\nfunction ScrollLooper(contentSize, limit, location, vectors) {\n  const jointSafety = 0.1;\n  const min = limit.min + jointSafety;\n  const max = limit.max + jointSafety;\n  const {\n    reachedMin,\n    reachedMax\n  } = Limit(min, max);\n  function shouldLoop(direction) {\n    if (direction === 1) return reachedMax(location.get());\n    if (direction === -1) return reachedMin(location.get());\n    return false;\n  }\n  function loop(direction) {\n    if (!shouldLoop(direction)) return;\n    const loopDistance = contentSize * (direction * -1);\n    vectors.forEach(v => v.add(loopDistance));\n  }\n  const self = {\n    loop\n  };\n  return self;\n}\n\nfunction ScrollProgress(limit) {\n  const {\n    max,\n    length\n  } = limit;\n  function get(n) {\n    const currentLocation = n - max;\n    return length ? currentLocation / -length : 0;\n  }\n  const self = {\n    get\n  };\n  return self;\n}\n\nfunction ScrollSnaps(axis, alignment, containerRect, slideRects, slidesToScroll) {\n  const {\n    startEdge,\n    endEdge\n  } = axis;\n  const {\n    groupSlides\n  } = slidesToScroll;\n  const alignments = measureSizes().map(alignment.measure);\n  const snaps = measureUnaligned();\n  const snapsAligned = measureAligned();\n  function measureSizes() {\n    return groupSlides(slideRects).map(rects => arrayLast(rects)[endEdge] - rects[0][startEdge]).map(mathAbs);\n  }\n  function measureUnaligned() {\n    return slideRects.map(rect => containerRect[startEdge] - rect[startEdge]).map(snap => -mathAbs(snap));\n  }\n  function measureAligned() {\n    return groupSlides(snaps).map(g => g[0]).map((snap, index) => snap + alignments[index]);\n  }\n  const self = {\n    snaps,\n    snapsAligned\n  };\n  return self;\n}\n\nfunction SlideRegistry(containSnaps, containScroll, scrollSnaps, scrollContainLimit, slidesToScroll, slideIndexes) {\n  const {\n    groupSlides\n  } = slidesToScroll;\n  const {\n    min,\n    max\n  } = scrollContainLimit;\n  const slideRegistry = createSlideRegistry();\n  function createSlideRegistry() {\n    const groupedSlideIndexes = groupSlides(slideIndexes);\n    const doNotContain = !containSnaps || containScroll === 'keepSnaps';\n    if (scrollSnaps.length === 1) return [slideIndexes];\n    if (doNotContain) return groupedSlideIndexes;\n    return groupedSlideIndexes.slice(min, max).map((group, index, groups) => {\n      const isFirst = !index;\n      const isLast = arrayIsLastIndex(groups, index);\n      if (isFirst) {\n        const range = arrayLast(groups[0]) + 1;\n        return arrayFromNumber(range);\n      }\n      if (isLast) {\n        const range = arrayLastIndex(slideIndexes) - arrayLast(groups)[0] + 1;\n        return arrayFromNumber(range, arrayLast(groups)[0]);\n      }\n      return group;\n    });\n  }\n  const self = {\n    slideRegistry\n  };\n  return self;\n}\n\nfunction ScrollTarget(loop, scrollSnaps, contentSize, limit, targetVector) {\n  const {\n    reachedAny,\n    removeOffset,\n    constrain\n  } = limit;\n  function minDistance(distances) {\n    return distances.concat().sort((a, b) => mathAbs(a) - mathAbs(b))[0];\n  }\n  function findTargetSnap(target) {\n    const distance = loop ? removeOffset(target) : constrain(target);\n    const ascDiffsToSnaps = scrollSnaps.map((snap, index) => ({\n      diff: shortcut(snap - distance, 0),\n      index\n    })).sort((d1, d2) => mathAbs(d1.diff) - mathAbs(d2.diff));\n    const {\n      index\n    } = ascDiffsToSnaps[0];\n    return {\n      index,\n      distance\n    };\n  }\n  function shortcut(target, direction) {\n    const targets = [target, target + contentSize, target - contentSize];\n    if (!loop) return target;\n    if (!direction) return minDistance(targets);\n    const matchingTargets = targets.filter(t => mathSign(t) === direction);\n    if (matchingTargets.length) return minDistance(matchingTargets);\n    return arrayLast(targets) - contentSize;\n  }\n  function byIndex(index, direction) {\n    const diffToSnap = scrollSnaps[index] - targetVector.get();\n    const distance = shortcut(diffToSnap, direction);\n    return {\n      index,\n      distance\n    };\n  }\n  function byDistance(distance, snap) {\n    const target = targetVector.get() + distance;\n    const {\n      index,\n      distance: targetSnapDistance\n    } = findTargetSnap(target);\n    const reachedBound = !loop && reachedAny(target);\n    if (!snap || reachedBound) return {\n      index,\n      distance\n    };\n    const diffToSnap = scrollSnaps[index] - targetSnapDistance;\n    const snapDistance = distance + shortcut(diffToSnap, 0);\n    return {\n      index,\n      distance: snapDistance\n    };\n  }\n  const self = {\n    byDistance,\n    byIndex,\n    shortcut\n  };\n  return self;\n}\n\nfunction ScrollTo(animation, indexCurrent, indexPrevious, scrollBody, scrollTarget, targetVector, eventHandler) {\n  function scrollTo(target) {\n    const distanceDiff = target.distance;\n    const indexDiff = target.index !== indexCurrent.get();\n    targetVector.add(distanceDiff);\n    if (distanceDiff) {\n      if (scrollBody.duration()) {\n        animation.start();\n      } else {\n        animation.update();\n        animation.render(1);\n        animation.update();\n      }\n    }\n    if (indexDiff) {\n      indexPrevious.set(indexCurrent.get());\n      indexCurrent.set(target.index);\n      eventHandler.emit('select');\n    }\n  }\n  function distance(n, snap) {\n    const target = scrollTarget.byDistance(n, snap);\n    scrollTo(target);\n  }\n  function index(n, direction) {\n    const targetIndex = indexCurrent.clone().set(n);\n    const target = scrollTarget.byIndex(targetIndex.get(), direction);\n    scrollTo(target);\n  }\n  const self = {\n    distance,\n    index\n  };\n  return self;\n}\n\nfunction SlideFocus(root, slides, slideRegistry, scrollTo, scrollBody, eventStore, eventHandler, watchFocus) {\n  const focusListenerOptions = {\n    passive: true,\n    capture: true\n  };\n  let lastTabPressTime = 0;\n  function init(emblaApi) {\n    if (!watchFocus) return;\n    function defaultCallback(index) {\n      const nowTime = new Date().getTime();\n      const diffTime = nowTime - lastTabPressTime;\n      if (diffTime > 10) return;\n      eventHandler.emit('slideFocusStart');\n      root.scrollLeft = 0;\n      const group = slideRegistry.findIndex(group => group.includes(index));\n      if (!isNumber(group)) return;\n      scrollBody.useDuration(0);\n      scrollTo.index(group, 0);\n      eventHandler.emit('slideFocus');\n    }\n    eventStore.add(document, 'keydown', registerTabPress, false);\n    slides.forEach((slide, slideIndex) => {\n      eventStore.add(slide, 'focus', evt => {\n        if (isBoolean(watchFocus) || watchFocus(emblaApi, evt)) {\n          defaultCallback(slideIndex);\n        }\n      }, focusListenerOptions);\n    });\n  }\n  function registerTabPress(event) {\n    if (event.code === 'Tab') lastTabPressTime = new Date().getTime();\n  }\n  const self = {\n    init\n  };\n  return self;\n}\n\nfunction Vector1D(initialValue) {\n  let value = initialValue;\n  function get() {\n    return value;\n  }\n  function set(n) {\n    value = normalizeInput(n);\n  }\n  function add(n) {\n    value += normalizeInput(n);\n  }\n  function subtract(n) {\n    value -= normalizeInput(n);\n  }\n  function normalizeInput(n) {\n    return isNumber(n) ? n : n.get();\n  }\n  const self = {\n    get,\n    set,\n    add,\n    subtract\n  };\n  return self;\n}\n\nfunction Translate(axis, container) {\n  const translate = axis.scroll === 'x' ? x : y;\n  const containerStyle = container.style;\n  let previousTarget = null;\n  let disabled = false;\n  function x(n) {\n    return `translate3d(${n}px,0px,0px)`;\n  }\n  function y(n) {\n    return `translate3d(0px,${n}px,0px)`;\n  }\n  function to(target) {\n    if (disabled) return;\n    const newTarget = roundToTwoDecimals(axis.direction(target));\n    if (newTarget === previousTarget) return;\n    containerStyle.transform = translate(newTarget);\n    previousTarget = newTarget;\n  }\n  function toggleActive(active) {\n    disabled = !active;\n  }\n  function clear() {\n    if (disabled) return;\n    containerStyle.transform = '';\n    if (!container.getAttribute('style')) container.removeAttribute('style');\n  }\n  const self = {\n    clear,\n    to,\n    toggleActive\n  };\n  return self;\n}\n\nfunction SlideLooper(axis, viewSize, contentSize, slideSizes, slideSizesWithGaps, snaps, scrollSnaps, location, slides) {\n  const roundingSafety = 0.5;\n  const ascItems = arrayKeys(slideSizesWithGaps);\n  const descItems = arrayKeys(slideSizesWithGaps).reverse();\n  const loopPoints = startPoints().concat(endPoints());\n  function removeSlideSizes(indexes, from) {\n    return indexes.reduce((a, i) => {\n      return a - slideSizesWithGaps[i];\n    }, from);\n  }\n  function slidesInGap(indexes, gap) {\n    return indexes.reduce((a, i) => {\n      const remainingGap = removeSlideSizes(a, gap);\n      return remainingGap > 0 ? a.concat([i]) : a;\n    }, []);\n  }\n  function findSlideBounds(offset) {\n    return snaps.map((snap, index) => ({\n      start: snap - slideSizes[index] + roundingSafety + offset,\n      end: snap + viewSize - roundingSafety + offset\n    }));\n  }\n  function findLoopPoints(indexes, offset, isEndEdge) {\n    const slideBounds = findSlideBounds(offset);\n    return indexes.map(index => {\n      const initial = isEndEdge ? 0 : -contentSize;\n      const altered = isEndEdge ? contentSize : 0;\n      const boundEdge = isEndEdge ? 'end' : 'start';\n      const loopPoint = slideBounds[index][boundEdge];\n      return {\n        index,\n        loopPoint,\n        slideLocation: Vector1D(-1),\n        translate: Translate(axis, slides[index]),\n        target: () => location.get() > loopPoint ? initial : altered\n      };\n    });\n  }\n  function startPoints() {\n    const gap = scrollSnaps[0];\n    const indexes = slidesInGap(descItems, gap);\n    return findLoopPoints(indexes, contentSize, false);\n  }\n  function endPoints() {\n    const gap = viewSize - scrollSnaps[0] - 1;\n    const indexes = slidesInGap(ascItems, gap);\n    return findLoopPoints(indexes, -contentSize, true);\n  }\n  function canLoop() {\n    return loopPoints.every(({\n      index\n    }) => {\n      const otherIndexes = ascItems.filter(i => i !== index);\n      return removeSlideSizes(otherIndexes, viewSize) <= 0.1;\n    });\n  }\n  function loop() {\n    loopPoints.forEach(loopPoint => {\n      const {\n        target,\n        translate,\n        slideLocation\n      } = loopPoint;\n      const shiftLocation = target();\n      if (shiftLocation === slideLocation.get()) return;\n      translate.to(shiftLocation);\n      slideLocation.set(shiftLocation);\n    });\n  }\n  function clear() {\n    loopPoints.forEach(loopPoint => loopPoint.translate.clear());\n  }\n  const self = {\n    canLoop,\n    clear,\n    loop,\n    loopPoints\n  };\n  return self;\n}\n\nfunction SlidesHandler(container, eventHandler, watchSlides) {\n  let mutationObserver;\n  let destroyed = false;\n  function init(emblaApi) {\n    if (!watchSlides) return;\n    function defaultCallback(mutations) {\n      for (const mutation of mutations) {\n        if (mutation.type === 'childList') {\n          emblaApi.reInit();\n          eventHandler.emit('slidesChanged');\n          break;\n        }\n      }\n    }\n    mutationObserver = new MutationObserver(mutations => {\n      if (destroyed) return;\n      if (isBoolean(watchSlides) || watchSlides(emblaApi, mutations)) {\n        defaultCallback(mutations);\n      }\n    });\n    mutationObserver.observe(container, {\n      childList: true\n    });\n  }\n  function destroy() {\n    if (mutationObserver) mutationObserver.disconnect();\n    destroyed = true;\n  }\n  const self = {\n    init,\n    destroy\n  };\n  return self;\n}\n\nfunction SlidesInView(container, slides, eventHandler, threshold) {\n  const intersectionEntryMap = {};\n  let inViewCache = null;\n  let notInViewCache = null;\n  let intersectionObserver;\n  let destroyed = false;\n  function init() {\n    intersectionObserver = new IntersectionObserver(entries => {\n      if (destroyed) return;\n      entries.forEach(entry => {\n        const index = slides.indexOf(entry.target);\n        intersectionEntryMap[index] = entry;\n      });\n      inViewCache = null;\n      notInViewCache = null;\n      eventHandler.emit('slidesInView');\n    }, {\n      root: container.parentElement,\n      threshold\n    });\n    slides.forEach(slide => intersectionObserver.observe(slide));\n  }\n  function destroy() {\n    if (intersectionObserver) intersectionObserver.disconnect();\n    destroyed = true;\n  }\n  function createInViewList(inView) {\n    return objectKeys(intersectionEntryMap).reduce((list, slideIndex) => {\n      const index = parseInt(slideIndex);\n      const {\n        isIntersecting\n      } = intersectionEntryMap[index];\n      const inViewMatch = inView && isIntersecting;\n      const notInViewMatch = !inView && !isIntersecting;\n      if (inViewMatch || notInViewMatch) list.push(index);\n      return list;\n    }, []);\n  }\n  function get(inView = true) {\n    if (inView && inViewCache) return inViewCache;\n    if (!inView && notInViewCache) return notInViewCache;\n    const slideIndexes = createInViewList(inView);\n    if (inView) inViewCache = slideIndexes;\n    if (!inView) notInViewCache = slideIndexes;\n    return slideIndexes;\n  }\n  const self = {\n    init,\n    destroy,\n    get\n  };\n  return self;\n}\n\nfunction SlideSizes(axis, containerRect, slideRects, slides, readEdgeGap, ownerWindow) {\n  const {\n    measureSize,\n    startEdge,\n    endEdge\n  } = axis;\n  const withEdgeGap = slideRects[0] && readEdgeGap;\n  const startGap = measureStartGap();\n  const endGap = measureEndGap();\n  const slideSizes = slideRects.map(measureSize);\n  const slideSizesWithGaps = measureWithGaps();\n  function measureStartGap() {\n    if (!withEdgeGap) return 0;\n    const slideRect = slideRects[0];\n    return mathAbs(containerRect[startEdge] - slideRect[startEdge]);\n  }\n  function measureEndGap() {\n    if (!withEdgeGap) return 0;\n    const style = ownerWindow.getComputedStyle(arrayLast(slides));\n    return parseFloat(style.getPropertyValue(`margin-${endEdge}`));\n  }\n  function measureWithGaps() {\n    return slideRects.map((rect, index, rects) => {\n      const isFirst = !index;\n      const isLast = arrayIsLastIndex(rects, index);\n      if (isFirst) return slideSizes[index] + startGap;\n      if (isLast) return slideSizes[index] + endGap;\n      return rects[index + 1][startEdge] - rect[startEdge];\n    }).map(mathAbs);\n  }\n  const self = {\n    slideSizes,\n    slideSizesWithGaps,\n    startGap,\n    endGap\n  };\n  return self;\n}\n\nfunction SlidesToScroll(axis, viewSize, slidesToScroll, loop, containerRect, slideRects, startGap, endGap, pixelTolerance) {\n  const {\n    startEdge,\n    endEdge,\n    direction\n  } = axis;\n  const groupByNumber = isNumber(slidesToScroll);\n  function byNumber(array, groupSize) {\n    return arrayKeys(array).filter(i => i % groupSize === 0).map(i => array.slice(i, i + groupSize));\n  }\n  function bySize(array) {\n    if (!array.length) return [];\n    return arrayKeys(array).reduce((groups, rectB, index) => {\n      const rectA = arrayLast(groups) || 0;\n      const isFirst = rectA === 0;\n      const isLast = rectB === arrayLastIndex(array);\n      const edgeA = containerRect[startEdge] - slideRects[rectA][startEdge];\n      const edgeB = containerRect[startEdge] - slideRects[rectB][endEdge];\n      const gapA = !loop && isFirst ? direction(startGap) : 0;\n      const gapB = !loop && isLast ? direction(endGap) : 0;\n      const chunkSize = mathAbs(edgeB - gapB - (edgeA + gapA));\n      if (index && chunkSize > viewSize + pixelTolerance) groups.push(rectB);\n      if (isLast) groups.push(array.length);\n      return groups;\n    }, []).map((currentSize, index, groups) => {\n      const previousSize = Math.max(groups[index - 1] || 0);\n      return array.slice(previousSize, currentSize);\n    });\n  }\n  function groupSlides(array) {\n    return groupByNumber ? byNumber(array, slidesToScroll) : bySize(array);\n  }\n  const self = {\n    groupSlides\n  };\n  return self;\n}\n\nfunction Engine(root, container, slides, ownerDocument, ownerWindow, options, eventHandler) {\n  // Options\n  const {\n    align,\n    axis: scrollAxis,\n    direction,\n    startIndex,\n    loop,\n    duration,\n    dragFree,\n    dragThreshold,\n    inViewThreshold,\n    slidesToScroll: groupSlides,\n    skipSnaps,\n    containScroll,\n    watchResize,\n    watchSlides,\n    watchDrag,\n    watchFocus\n  } = options;\n  // Measurements\n  const pixelTolerance = 2;\n  const nodeRects = NodeRects();\n  const containerRect = nodeRects.measure(container);\n  const slideRects = slides.map(nodeRects.measure);\n  const axis = Axis(scrollAxis, direction);\n  const viewSize = axis.measureSize(containerRect);\n  const percentOfView = PercentOfView(viewSize);\n  const alignment = Alignment(align, viewSize);\n  const containSnaps = !loop && !!containScroll;\n  const readEdgeGap = loop || !!containScroll;\n  const {\n    slideSizes,\n    slideSizesWithGaps,\n    startGap,\n    endGap\n  } = SlideSizes(axis, containerRect, slideRects, slides, readEdgeGap, ownerWindow);\n  const slidesToScroll = SlidesToScroll(axis, viewSize, groupSlides, loop, containerRect, slideRects, startGap, endGap, pixelTolerance);\n  const {\n    snaps,\n    snapsAligned\n  } = ScrollSnaps(axis, alignment, containerRect, slideRects, slidesToScroll);\n  const contentSize = -arrayLast(snaps) + arrayLast(slideSizesWithGaps);\n  const {\n    snapsContained,\n    scrollContainLimit\n  } = ScrollContain(viewSize, contentSize, snapsAligned, containScroll, pixelTolerance);\n  const scrollSnaps = containSnaps ? snapsContained : snapsAligned;\n  const {\n    limit\n  } = ScrollLimit(contentSize, scrollSnaps, loop);\n  // Indexes\n  const index = Counter(arrayLastIndex(scrollSnaps), startIndex, loop);\n  const indexPrevious = index.clone();\n  const slideIndexes = arrayKeys(slides);\n  // Animation\n  const update = ({\n    dragHandler,\n    scrollBody,\n    scrollBounds,\n    options: {\n      loop\n    }\n  }) => {\n    if (!loop) scrollBounds.constrain(dragHandler.pointerDown());\n    scrollBody.seek();\n  };\n  const render = ({\n    scrollBody,\n    translate,\n    location,\n    offsetLocation,\n    previousLocation,\n    scrollLooper,\n    slideLooper,\n    dragHandler,\n    animation,\n    eventHandler,\n    scrollBounds,\n    options: {\n      loop\n    }\n  }, alpha) => {\n    const shouldSettle = scrollBody.settled();\n    const withinBounds = !scrollBounds.shouldConstrain();\n    const hasSettled = loop ? shouldSettle : shouldSettle && withinBounds;\n    const hasSettledAndIdle = hasSettled && !dragHandler.pointerDown();\n    if (hasSettledAndIdle) animation.stop();\n    const interpolatedLocation = location.get() * alpha + previousLocation.get() * (1 - alpha);\n    offsetLocation.set(interpolatedLocation);\n    if (loop) {\n      scrollLooper.loop(scrollBody.direction());\n      slideLooper.loop();\n    }\n    translate.to(offsetLocation.get());\n    if (hasSettledAndIdle) eventHandler.emit('settle');\n    if (!hasSettled) eventHandler.emit('scroll');\n  };\n  const animation = Animations(ownerDocument, ownerWindow, () => update(engine), alpha => render(engine, alpha));\n  // Shared\n  const friction = 0.68;\n  const startLocation = scrollSnaps[index.get()];\n  const location = Vector1D(startLocation);\n  const previousLocation = Vector1D(startLocation);\n  const offsetLocation = Vector1D(startLocation);\n  const target = Vector1D(startLocation);\n  const scrollBody = ScrollBody(location, offsetLocation, previousLocation, target, duration, friction);\n  const scrollTarget = ScrollTarget(loop, scrollSnaps, contentSize, limit, target);\n  const scrollTo = ScrollTo(animation, index, indexPrevious, scrollBody, scrollTarget, target, eventHandler);\n  const scrollProgress = ScrollProgress(limit);\n  const eventStore = EventStore();\n  const slidesInView = SlidesInView(container, slides, eventHandler, inViewThreshold);\n  const {\n    slideRegistry\n  } = SlideRegistry(containSnaps, containScroll, scrollSnaps, scrollContainLimit, slidesToScroll, slideIndexes);\n  const slideFocus = SlideFocus(root, slides, slideRegistry, scrollTo, scrollBody, eventStore, eventHandler, watchFocus);\n  // Engine\n  const engine = {\n    ownerDocument,\n    ownerWindow,\n    eventHandler,\n    containerRect,\n    slideRects,\n    animation,\n    axis,\n    dragHandler: DragHandler(axis, root, ownerDocument, ownerWindow, target, DragTracker(axis, ownerWindow), location, animation, scrollTo, scrollBody, scrollTarget, index, eventHandler, percentOfView, dragFree, dragThreshold, skipSnaps, friction, watchDrag),\n    eventStore,\n    percentOfView,\n    index,\n    indexPrevious,\n    limit,\n    location,\n    offsetLocation,\n    previousLocation,\n    options,\n    resizeHandler: ResizeHandler(container, eventHandler, ownerWindow, slides, axis, watchResize, nodeRects),\n    scrollBody,\n    scrollBounds: ScrollBounds(limit, offsetLocation, target, scrollBody, percentOfView),\n    scrollLooper: ScrollLooper(contentSize, limit, offsetLocation, [location, offsetLocation, previousLocation, target]),\n    scrollProgress,\n    scrollSnapList: scrollSnaps.map(scrollProgress.get),\n    scrollSnaps,\n    scrollTarget,\n    scrollTo,\n    slideLooper: SlideLooper(axis, viewSize, contentSize, slideSizes, slideSizesWithGaps, snaps, scrollSnaps, offsetLocation, slides),\n    slideFocus,\n    slidesHandler: SlidesHandler(container, eventHandler, watchSlides),\n    slidesInView,\n    slideIndexes,\n    slideRegistry,\n    slidesToScroll,\n    target,\n    translate: Translate(axis, container)\n  };\n  return engine;\n}\n\nfunction EventHandler() {\n  let listeners = {};\n  let api;\n  function init(emblaApi) {\n    api = emblaApi;\n  }\n  function getListeners(evt) {\n    return listeners[evt] || [];\n  }\n  function emit(evt) {\n    getListeners(evt).forEach(e => e(api, evt));\n    return self;\n  }\n  function on(evt, cb) {\n    listeners[evt] = getListeners(evt).concat([cb]);\n    return self;\n  }\n  function off(evt, cb) {\n    listeners[evt] = getListeners(evt).filter(e => e !== cb);\n    return self;\n  }\n  function clear() {\n    listeners = {};\n  }\n  const self = {\n    init,\n    emit,\n    off,\n    on,\n    clear\n  };\n  return self;\n}\n\nconst defaultOptions = {\n  align: 'center',\n  axis: 'x',\n  container: null,\n  slides: null,\n  containScroll: 'trimSnaps',\n  direction: 'ltr',\n  slidesToScroll: 1,\n  inViewThreshold: 0,\n  breakpoints: {},\n  dragFree: false,\n  dragThreshold: 10,\n  loop: false,\n  skipSnaps: false,\n  duration: 25,\n  startIndex: 0,\n  active: true,\n  watchDrag: true,\n  watchResize: true,\n  watchSlides: true,\n  watchFocus: true\n};\n\nfunction OptionsHandler(ownerWindow) {\n  function mergeOptions(optionsA, optionsB) {\n    return objectsMergeDeep(optionsA, optionsB || {});\n  }\n  function optionsAtMedia(options) {\n    const optionsAtMedia = options.breakpoints || {};\n    const matchedMediaOptions = objectKeys(optionsAtMedia).filter(media => ownerWindow.matchMedia(media).matches).map(media => optionsAtMedia[media]).reduce((a, mediaOption) => mergeOptions(a, mediaOption), {});\n    return mergeOptions(options, matchedMediaOptions);\n  }\n  function optionsMediaQueries(optionsList) {\n    return optionsList.map(options => objectKeys(options.breakpoints || {})).reduce((acc, mediaQueries) => acc.concat(mediaQueries), []).map(ownerWindow.matchMedia);\n  }\n  const self = {\n    mergeOptions,\n    optionsAtMedia,\n    optionsMediaQueries\n  };\n  return self;\n}\n\nfunction PluginsHandler(optionsHandler) {\n  let activePlugins = [];\n  function init(emblaApi, plugins) {\n    activePlugins = plugins.filter(({\n      options\n    }) => optionsHandler.optionsAtMedia(options).active !== false);\n    activePlugins.forEach(plugin => plugin.init(emblaApi, optionsHandler));\n    return plugins.reduce((map, plugin) => Object.assign(map, {\n      [plugin.name]: plugin\n    }), {});\n  }\n  function destroy() {\n    activePlugins = activePlugins.filter(plugin => plugin.destroy());\n  }\n  const self = {\n    init,\n    destroy\n  };\n  return self;\n}\n\nfunction EmblaCarousel(root, userOptions, userPlugins) {\n  const ownerDocument = root.ownerDocument;\n  const ownerWindow = ownerDocument.defaultView;\n  const optionsHandler = OptionsHandler(ownerWindow);\n  const pluginsHandler = PluginsHandler(optionsHandler);\n  const mediaHandlers = EventStore();\n  const eventHandler = EventHandler();\n  const {\n    mergeOptions,\n    optionsAtMedia,\n    optionsMediaQueries\n  } = optionsHandler;\n  const {\n    on,\n    off,\n    emit\n  } = eventHandler;\n  const reInit = reActivate;\n  let destroyed = false;\n  let engine;\n  let optionsBase = mergeOptions(defaultOptions, EmblaCarousel.globalOptions);\n  let options = mergeOptions(optionsBase);\n  let pluginList = [];\n  let pluginApis;\n  let container;\n  let slides;\n  function storeElements() {\n    const {\n      container: userContainer,\n      slides: userSlides\n    } = options;\n    const customContainer = isString(userContainer) ? root.querySelector(userContainer) : userContainer;\n    container = customContainer || root.children[0];\n    const customSlides = isString(userSlides) ? container.querySelectorAll(userSlides) : userSlides;\n    slides = [].slice.call(customSlides || container.children);\n  }\n  function createEngine(options) {\n    const engine = Engine(root, container, slides, ownerDocument, ownerWindow, options, eventHandler);\n    if (options.loop && !engine.slideLooper.canLoop()) {\n      const optionsWithoutLoop = Object.assign({}, options, {\n        loop: false\n      });\n      return createEngine(optionsWithoutLoop);\n    }\n    return engine;\n  }\n  function activate(withOptions, withPlugins) {\n    if (destroyed) return;\n    optionsBase = mergeOptions(optionsBase, withOptions);\n    options = optionsAtMedia(optionsBase);\n    pluginList = withPlugins || pluginList;\n    storeElements();\n    engine = createEngine(options);\n    optionsMediaQueries([optionsBase, ...pluginList.map(({\n      options\n    }) => options)]).forEach(query => mediaHandlers.add(query, 'change', reActivate));\n    if (!options.active) return;\n    engine.translate.to(engine.location.get());\n    engine.animation.init();\n    engine.slidesInView.init();\n    engine.slideFocus.init(self);\n    engine.eventHandler.init(self);\n    engine.resizeHandler.init(self);\n    engine.slidesHandler.init(self);\n    if (engine.options.loop) engine.slideLooper.loop();\n    if (container.offsetParent && slides.length) engine.dragHandler.init(self);\n    pluginApis = pluginsHandler.init(self, pluginList);\n  }\n  function reActivate(withOptions, withPlugins) {\n    const startIndex = selectedScrollSnap();\n    deActivate();\n    activate(mergeOptions({\n      startIndex\n    }, withOptions), withPlugins);\n    eventHandler.emit('reInit');\n  }\n  function deActivate() {\n    engine.dragHandler.destroy();\n    engine.eventStore.clear();\n    engine.translate.clear();\n    engine.slideLooper.clear();\n    engine.resizeHandler.destroy();\n    engine.slidesHandler.destroy();\n    engine.slidesInView.destroy();\n    engine.animation.destroy();\n    pluginsHandler.destroy();\n    mediaHandlers.clear();\n  }\n  function destroy() {\n    if (destroyed) return;\n    destroyed = true;\n    mediaHandlers.clear();\n    deActivate();\n    eventHandler.emit('destroy');\n    eventHandler.clear();\n  }\n  function scrollTo(index, jump, direction) {\n    if (!options.active || destroyed) return;\n    engine.scrollBody.useBaseFriction().useDuration(jump === true ? 0 : options.duration);\n    engine.scrollTo.index(index, direction || 0);\n  }\n  function scrollNext(jump) {\n    const next = engine.index.add(1).get();\n    scrollTo(next, jump, -1);\n  }\n  function scrollPrev(jump) {\n    const prev = engine.index.add(-1).get();\n    scrollTo(prev, jump, 1);\n  }\n  function canScrollNext() {\n    const next = engine.index.add(1).get();\n    return next !== selectedScrollSnap();\n  }\n  function canScrollPrev() {\n    const prev = engine.index.add(-1).get();\n    return prev !== selectedScrollSnap();\n  }\n  function scrollSnapList() {\n    return engine.scrollSnapList;\n  }\n  function scrollProgress() {\n    return engine.scrollProgress.get(engine.offsetLocation.get());\n  }\n  function selectedScrollSnap() {\n    return engine.index.get();\n  }\n  function previousScrollSnap() {\n    return engine.indexPrevious.get();\n  }\n  function slidesInView() {\n    return engine.slidesInView.get();\n  }\n  function slidesNotInView() {\n    return engine.slidesInView.get(false);\n  }\n  function plugins() {\n    return pluginApis;\n  }\n  function internalEngine() {\n    return engine;\n  }\n  function rootNode() {\n    return root;\n  }\n  function containerNode() {\n    return container;\n  }\n  function slideNodes() {\n    return slides;\n  }\n  const self = {\n    canScrollNext,\n    canScrollPrev,\n    containerNode,\n    internalEngine,\n    destroy,\n    off,\n    on,\n    emit,\n    plugins,\n    previousScrollSnap,\n    reInit,\n    rootNode,\n    scrollNext,\n    scrollPrev,\n    scrollProgress,\n    scrollSnapList,\n    scrollTo,\n    selectedScrollSnap,\n    slideNodes,\n    slidesInView,\n    slidesNotInView\n  };\n  activate(userOptions, userPlugins);\n  setTimeout(() => eventHandler.emit('init'), 0);\n  return self;\n}\nEmblaCarousel.globalOptions = undefined;\n\n\n//# sourceMappingURL=embla-carousel.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZW1ibGEtY2Fyb3VzZWwvZXNtL2VtYmxhLWNhcm91c2VsLmVzbS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxHQUFHLElBQUk7QUFDUDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsNkJBQTZCO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQixFQUFFO0FBQzVCO0FBQ0E7QUFDQSw4QkFBOEIsRUFBRTtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdURBQXVELFFBQVE7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0Esb0RBQW9EO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBLGlOQUFpTjtBQUNqTjtBQUNBO0FBQ0E7QUFDQSwwRUFBMEU7QUFDMUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUssS0FBSztBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlEQUFpRDtBQUNqRDtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRW9DO0FBQ3BDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvZW1ibGEtY2Fyb3VzZWwvZXNtL2VtYmxhLWNhcm91c2VsLmVzbS5qcz8wZTA5Il0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGlzTnVtYmVyKHN1YmplY3QpIHtcbiAgcmV0dXJuIHR5cGVvZiBzdWJqZWN0ID09PSAnbnVtYmVyJztcbn1cbmZ1bmN0aW9uIGlzU3RyaW5nKHN1YmplY3QpIHtcbiAgcmV0dXJuIHR5cGVvZiBzdWJqZWN0ID09PSAnc3RyaW5nJztcbn1cbmZ1bmN0aW9uIGlzQm9vbGVhbihzdWJqZWN0KSB7XG4gIHJldHVybiB0eXBlb2Ygc3ViamVjdCA9PT0gJ2Jvb2xlYW4nO1xufVxuZnVuY3Rpb24gaXNPYmplY3Qoc3ViamVjdCkge1xuICByZXR1cm4gT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKHN1YmplY3QpID09PSAnW29iamVjdCBPYmplY3RdJztcbn1cbmZ1bmN0aW9uIG1hdGhBYnMobikge1xuICByZXR1cm4gTWF0aC5hYnMobik7XG59XG5mdW5jdGlvbiBtYXRoU2lnbihuKSB7XG4gIHJldHVybiBNYXRoLnNpZ24obik7XG59XG5mdW5jdGlvbiBkZWx0YUFicyh2YWx1ZUIsIHZhbHVlQSkge1xuICByZXR1cm4gbWF0aEFicyh2YWx1ZUIgLSB2YWx1ZUEpO1xufVxuZnVuY3Rpb24gZmFjdG9yQWJzKHZhbHVlQiwgdmFsdWVBKSB7XG4gIGlmICh2YWx1ZUIgPT09IDAgfHwgdmFsdWVBID09PSAwKSByZXR1cm4gMDtcbiAgaWYgKG1hdGhBYnModmFsdWVCKSA8PSBtYXRoQWJzKHZhbHVlQSkpIHJldHVybiAwO1xuICBjb25zdCBkaWZmID0gZGVsdGFBYnMobWF0aEFicyh2YWx1ZUIpLCBtYXRoQWJzKHZhbHVlQSkpO1xuICByZXR1cm4gbWF0aEFicyhkaWZmIC8gdmFsdWVCKTtcbn1cbmZ1bmN0aW9uIHJvdW5kVG9Ud29EZWNpbWFscyhudW0pIHtcbiAgcmV0dXJuIE1hdGgucm91bmQobnVtICogMTAwKSAvIDEwMDtcbn1cbmZ1bmN0aW9uIGFycmF5S2V5cyhhcnJheSkge1xuICByZXR1cm4gb2JqZWN0S2V5cyhhcnJheSkubWFwKE51bWJlcik7XG59XG5mdW5jdGlvbiBhcnJheUxhc3QoYXJyYXkpIHtcbiAgcmV0dXJuIGFycmF5W2FycmF5TGFzdEluZGV4KGFycmF5KV07XG59XG5mdW5jdGlvbiBhcnJheUxhc3RJbmRleChhcnJheSkge1xuICByZXR1cm4gTWF0aC5tYXgoMCwgYXJyYXkubGVuZ3RoIC0gMSk7XG59XG5mdW5jdGlvbiBhcnJheUlzTGFzdEluZGV4KGFycmF5LCBpbmRleCkge1xuICByZXR1cm4gaW5kZXggPT09IGFycmF5TGFzdEluZGV4KGFycmF5KTtcbn1cbmZ1bmN0aW9uIGFycmF5RnJvbU51bWJlcihuLCBzdGFydEF0ID0gMCkge1xuICByZXR1cm4gQXJyYXkuZnJvbShBcnJheShuKSwgKF8sIGkpID0+IHN0YXJ0QXQgKyBpKTtcbn1cbmZ1bmN0aW9uIG9iamVjdEtleXMob2JqZWN0KSB7XG4gIHJldHVybiBPYmplY3Qua2V5cyhvYmplY3QpO1xufVxuZnVuY3Rpb24gb2JqZWN0c01lcmdlRGVlcChvYmplY3RBLCBvYmplY3RCKSB7XG4gIHJldHVybiBbb2JqZWN0QSwgb2JqZWN0Ql0ucmVkdWNlKChtZXJnZWRPYmplY3RzLCBjdXJyZW50T2JqZWN0KSA9PiB7XG4gICAgb2JqZWN0S2V5cyhjdXJyZW50T2JqZWN0KS5mb3JFYWNoKGtleSA9PiB7XG4gICAgICBjb25zdCB2YWx1ZUEgPSBtZXJnZWRPYmplY3RzW2tleV07XG4gICAgICBjb25zdCB2YWx1ZUIgPSBjdXJyZW50T2JqZWN0W2tleV07XG4gICAgICBjb25zdCBhcmVPYmplY3RzID0gaXNPYmplY3QodmFsdWVBKSAmJiBpc09iamVjdCh2YWx1ZUIpO1xuICAgICAgbWVyZ2VkT2JqZWN0c1trZXldID0gYXJlT2JqZWN0cyA/IG9iamVjdHNNZXJnZURlZXAodmFsdWVBLCB2YWx1ZUIpIDogdmFsdWVCO1xuICAgIH0pO1xuICAgIHJldHVybiBtZXJnZWRPYmplY3RzO1xuICB9LCB7fSk7XG59XG5mdW5jdGlvbiBpc01vdXNlRXZlbnQoZXZ0LCBvd25lcldpbmRvdykge1xuICByZXR1cm4gdHlwZW9mIG93bmVyV2luZG93Lk1vdXNlRXZlbnQgIT09ICd1bmRlZmluZWQnICYmIGV2dCBpbnN0YW5jZW9mIG93bmVyV2luZG93Lk1vdXNlRXZlbnQ7XG59XG5cbmZ1bmN0aW9uIEFsaWdubWVudChhbGlnbiwgdmlld1NpemUpIHtcbiAgY29uc3QgcHJlZGVmaW5lZCA9IHtcbiAgICBzdGFydCxcbiAgICBjZW50ZXIsXG4gICAgZW5kXG4gIH07XG4gIGZ1bmN0aW9uIHN0YXJ0KCkge1xuICAgIHJldHVybiAwO1xuICB9XG4gIGZ1bmN0aW9uIGNlbnRlcihuKSB7XG4gICAgcmV0dXJuIGVuZChuKSAvIDI7XG4gIH1cbiAgZnVuY3Rpb24gZW5kKG4pIHtcbiAgICByZXR1cm4gdmlld1NpemUgLSBuO1xuICB9XG4gIGZ1bmN0aW9uIG1lYXN1cmUobiwgaW5kZXgpIHtcbiAgICBpZiAoaXNTdHJpbmcoYWxpZ24pKSByZXR1cm4gcHJlZGVmaW5lZFthbGlnbl0obik7XG4gICAgcmV0dXJuIGFsaWduKHZpZXdTaXplLCBuLCBpbmRleCk7XG4gIH1cbiAgY29uc3Qgc2VsZiA9IHtcbiAgICBtZWFzdXJlXG4gIH07XG4gIHJldHVybiBzZWxmO1xufVxuXG5mdW5jdGlvbiBFdmVudFN0b3JlKCkge1xuICBsZXQgbGlzdGVuZXJzID0gW107XG4gIGZ1bmN0aW9uIGFkZChub2RlLCB0eXBlLCBoYW5kbGVyLCBvcHRpb25zID0ge1xuICAgIHBhc3NpdmU6IHRydWVcbiAgfSkge1xuICAgIGxldCByZW1vdmVMaXN0ZW5lcjtcbiAgICBpZiAoJ2FkZEV2ZW50TGlzdGVuZXInIGluIG5vZGUpIHtcbiAgICAgIG5vZGUuYWRkRXZlbnRMaXN0ZW5lcih0eXBlLCBoYW5kbGVyLCBvcHRpb25zKTtcbiAgICAgIHJlbW92ZUxpc3RlbmVyID0gKCkgPT4gbm9kZS5yZW1vdmVFdmVudExpc3RlbmVyKHR5cGUsIGhhbmRsZXIsIG9wdGlvbnMpO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zdCBsZWdhY3lNZWRpYVF1ZXJ5TGlzdCA9IG5vZGU7XG4gICAgICBsZWdhY3lNZWRpYVF1ZXJ5TGlzdC5hZGRMaXN0ZW5lcihoYW5kbGVyKTtcbiAgICAgIHJlbW92ZUxpc3RlbmVyID0gKCkgPT4gbGVnYWN5TWVkaWFRdWVyeUxpc3QucmVtb3ZlTGlzdGVuZXIoaGFuZGxlcik7XG4gICAgfVxuICAgIGxpc3RlbmVycy5wdXNoKHJlbW92ZUxpc3RlbmVyKTtcbiAgICByZXR1cm4gc2VsZjtcbiAgfVxuICBmdW5jdGlvbiBjbGVhcigpIHtcbiAgICBsaXN0ZW5lcnMgPSBsaXN0ZW5lcnMuZmlsdGVyKHJlbW92ZSA9PiByZW1vdmUoKSk7XG4gIH1cbiAgY29uc3Qgc2VsZiA9IHtcbiAgICBhZGQsXG4gICAgY2xlYXJcbiAgfTtcbiAgcmV0dXJuIHNlbGY7XG59XG5cbmZ1bmN0aW9uIEFuaW1hdGlvbnMob3duZXJEb2N1bWVudCwgb3duZXJXaW5kb3csIHVwZGF0ZSwgcmVuZGVyKSB7XG4gIGNvbnN0IGRvY3VtZW50VmlzaWJsZUhhbmRsZXIgPSBFdmVudFN0b3JlKCk7XG4gIGNvbnN0IGZpeGVkVGltZVN0ZXAgPSAxMDAwIC8gNjA7XG4gIGxldCBsYXN0VGltZVN0YW1wID0gbnVsbDtcbiAgbGV0IGFjY3VtdWxhdGVkVGltZSA9IDA7XG4gIGxldCBhbmltYXRpb25JZCA9IDA7XG4gIGZ1bmN0aW9uIGluaXQoKSB7XG4gICAgZG9jdW1lbnRWaXNpYmxlSGFuZGxlci5hZGQob3duZXJEb2N1bWVudCwgJ3Zpc2liaWxpdHljaGFuZ2UnLCAoKSA9PiB7XG4gICAgICBpZiAob3duZXJEb2N1bWVudC5oaWRkZW4pIHJlc2V0KCk7XG4gICAgfSk7XG4gIH1cbiAgZnVuY3Rpb24gZGVzdHJveSgpIHtcbiAgICBzdG9wKCk7XG4gICAgZG9jdW1lbnRWaXNpYmxlSGFuZGxlci5jbGVhcigpO1xuICB9XG4gIGZ1bmN0aW9uIGFuaW1hdGUodGltZVN0YW1wKSB7XG4gICAgaWYgKCFhbmltYXRpb25JZCkgcmV0dXJuO1xuICAgIGlmICghbGFzdFRpbWVTdGFtcCkge1xuICAgICAgbGFzdFRpbWVTdGFtcCA9IHRpbWVTdGFtcDtcbiAgICAgIHVwZGF0ZSgpO1xuICAgICAgdXBkYXRlKCk7XG4gICAgfVxuICAgIGNvbnN0IHRpbWVFbGFwc2VkID0gdGltZVN0YW1wIC0gbGFzdFRpbWVTdGFtcDtcbiAgICBsYXN0VGltZVN0YW1wID0gdGltZVN0YW1wO1xuICAgIGFjY3VtdWxhdGVkVGltZSArPSB0aW1lRWxhcHNlZDtcbiAgICB3aGlsZSAoYWNjdW11bGF0ZWRUaW1lID49IGZpeGVkVGltZVN0ZXApIHtcbiAgICAgIHVwZGF0ZSgpO1xuICAgICAgYWNjdW11bGF0ZWRUaW1lIC09IGZpeGVkVGltZVN0ZXA7XG4gICAgfVxuICAgIGNvbnN0IGFscGhhID0gYWNjdW11bGF0ZWRUaW1lIC8gZml4ZWRUaW1lU3RlcDtcbiAgICByZW5kZXIoYWxwaGEpO1xuICAgIGlmIChhbmltYXRpb25JZCkge1xuICAgICAgYW5pbWF0aW9uSWQgPSBvd25lcldpbmRvdy5yZXF1ZXN0QW5pbWF0aW9uRnJhbWUoYW5pbWF0ZSk7XG4gICAgfVxuICB9XG4gIGZ1bmN0aW9uIHN0YXJ0KCkge1xuICAgIGlmIChhbmltYXRpb25JZCkgcmV0dXJuO1xuICAgIGFuaW1hdGlvbklkID0gb3duZXJXaW5kb3cucmVxdWVzdEFuaW1hdGlvbkZyYW1lKGFuaW1hdGUpO1xuICB9XG4gIGZ1bmN0aW9uIHN0b3AoKSB7XG4gICAgb3duZXJXaW5kb3cuY2FuY2VsQW5pbWF0aW9uRnJhbWUoYW5pbWF0aW9uSWQpO1xuICAgIGxhc3RUaW1lU3RhbXAgPSBudWxsO1xuICAgIGFjY3VtdWxhdGVkVGltZSA9IDA7XG4gICAgYW5pbWF0aW9uSWQgPSAwO1xuICB9XG4gIGZ1bmN0aW9uIHJlc2V0KCkge1xuICAgIGxhc3RUaW1lU3RhbXAgPSBudWxsO1xuICAgIGFjY3VtdWxhdGVkVGltZSA9IDA7XG4gIH1cbiAgY29uc3Qgc2VsZiA9IHtcbiAgICBpbml0LFxuICAgIGRlc3Ryb3ksXG4gICAgc3RhcnQsXG4gICAgc3RvcCxcbiAgICB1cGRhdGUsXG4gICAgcmVuZGVyXG4gIH07XG4gIHJldHVybiBzZWxmO1xufVxuXG5mdW5jdGlvbiBBeGlzKGF4aXMsIGNvbnRlbnREaXJlY3Rpb24pIHtcbiAgY29uc3QgaXNSaWdodFRvTGVmdCA9IGNvbnRlbnREaXJlY3Rpb24gPT09ICdydGwnO1xuICBjb25zdCBpc1ZlcnRpY2FsID0gYXhpcyA9PT0gJ3knO1xuICBjb25zdCBzY3JvbGwgPSBpc1ZlcnRpY2FsID8gJ3knIDogJ3gnO1xuICBjb25zdCBjcm9zcyA9IGlzVmVydGljYWwgPyAneCcgOiAneSc7XG4gIGNvbnN0IHNpZ24gPSAhaXNWZXJ0aWNhbCAmJiBpc1JpZ2h0VG9MZWZ0ID8gLTEgOiAxO1xuICBjb25zdCBzdGFydEVkZ2UgPSBnZXRTdGFydEVkZ2UoKTtcbiAgY29uc3QgZW5kRWRnZSA9IGdldEVuZEVkZ2UoKTtcbiAgZnVuY3Rpb24gbWVhc3VyZVNpemUobm9kZVJlY3QpIHtcbiAgICBjb25zdCB7XG4gICAgICBoZWlnaHQsXG4gICAgICB3aWR0aFxuICAgIH0gPSBub2RlUmVjdDtcbiAgICByZXR1cm4gaXNWZXJ0aWNhbCA/IGhlaWdodCA6IHdpZHRoO1xuICB9XG4gIGZ1bmN0aW9uIGdldFN0YXJ0RWRnZSgpIHtcbiAgICBpZiAoaXNWZXJ0aWNhbCkgcmV0dXJuICd0b3AnO1xuICAgIHJldHVybiBpc1JpZ2h0VG9MZWZ0ID8gJ3JpZ2h0JyA6ICdsZWZ0JztcbiAgfVxuICBmdW5jdGlvbiBnZXRFbmRFZGdlKCkge1xuICAgIGlmIChpc1ZlcnRpY2FsKSByZXR1cm4gJ2JvdHRvbSc7XG4gICAgcmV0dXJuIGlzUmlnaHRUb0xlZnQgPyAnbGVmdCcgOiAncmlnaHQnO1xuICB9XG4gIGZ1bmN0aW9uIGRpcmVjdGlvbihuKSB7XG4gICAgcmV0dXJuIG4gKiBzaWduO1xuICB9XG4gIGNvbnN0IHNlbGYgPSB7XG4gICAgc2Nyb2xsLFxuICAgIGNyb3NzLFxuICAgIHN0YXJ0RWRnZSxcbiAgICBlbmRFZGdlLFxuICAgIG1lYXN1cmVTaXplLFxuICAgIGRpcmVjdGlvblxuICB9O1xuICByZXR1cm4gc2VsZjtcbn1cblxuZnVuY3Rpb24gTGltaXQobWluID0gMCwgbWF4ID0gMCkge1xuICBjb25zdCBsZW5ndGggPSBtYXRoQWJzKG1pbiAtIG1heCk7XG4gIGZ1bmN0aW9uIHJlYWNoZWRNaW4obikge1xuICAgIHJldHVybiBuIDwgbWluO1xuICB9XG4gIGZ1bmN0aW9uIHJlYWNoZWRNYXgobikge1xuICAgIHJldHVybiBuID4gbWF4O1xuICB9XG4gIGZ1bmN0aW9uIHJlYWNoZWRBbnkobikge1xuICAgIHJldHVybiByZWFjaGVkTWluKG4pIHx8IHJlYWNoZWRNYXgobik7XG4gIH1cbiAgZnVuY3Rpb24gY29uc3RyYWluKG4pIHtcbiAgICBpZiAoIXJlYWNoZWRBbnkobikpIHJldHVybiBuO1xuICAgIHJldHVybiByZWFjaGVkTWluKG4pID8gbWluIDogbWF4O1xuICB9XG4gIGZ1bmN0aW9uIHJlbW92ZU9mZnNldChuKSB7XG4gICAgaWYgKCFsZW5ndGgpIHJldHVybiBuO1xuICAgIHJldHVybiBuIC0gbGVuZ3RoICogTWF0aC5jZWlsKChuIC0gbWF4KSAvIGxlbmd0aCk7XG4gIH1cbiAgY29uc3Qgc2VsZiA9IHtcbiAgICBsZW5ndGgsXG4gICAgbWF4LFxuICAgIG1pbixcbiAgICBjb25zdHJhaW4sXG4gICAgcmVhY2hlZEFueSxcbiAgICByZWFjaGVkTWF4LFxuICAgIHJlYWNoZWRNaW4sXG4gICAgcmVtb3ZlT2Zmc2V0XG4gIH07XG4gIHJldHVybiBzZWxmO1xufVxuXG5mdW5jdGlvbiBDb3VudGVyKG1heCwgc3RhcnQsIGxvb3ApIHtcbiAgY29uc3Qge1xuICAgIGNvbnN0cmFpblxuICB9ID0gTGltaXQoMCwgbWF4KTtcbiAgY29uc3QgbG9vcEVuZCA9IG1heCArIDE7XG4gIGxldCBjb3VudGVyID0gd2l0aGluTGltaXQoc3RhcnQpO1xuICBmdW5jdGlvbiB3aXRoaW5MaW1pdChuKSB7XG4gICAgcmV0dXJuICFsb29wID8gY29uc3RyYWluKG4pIDogbWF0aEFicygobG9vcEVuZCArIG4pICUgbG9vcEVuZCk7XG4gIH1cbiAgZnVuY3Rpb24gZ2V0KCkge1xuICAgIHJldHVybiBjb3VudGVyO1xuICB9XG4gIGZ1bmN0aW9uIHNldChuKSB7XG4gICAgY291bnRlciA9IHdpdGhpbkxpbWl0KG4pO1xuICAgIHJldHVybiBzZWxmO1xuICB9XG4gIGZ1bmN0aW9uIGFkZChuKSB7XG4gICAgcmV0dXJuIGNsb25lKCkuc2V0KGdldCgpICsgbik7XG4gIH1cbiAgZnVuY3Rpb24gY2xvbmUoKSB7XG4gICAgcmV0dXJuIENvdW50ZXIobWF4LCBnZXQoKSwgbG9vcCk7XG4gIH1cbiAgY29uc3Qgc2VsZiA9IHtcbiAgICBnZXQsXG4gICAgc2V0LFxuICAgIGFkZCxcbiAgICBjbG9uZVxuICB9O1xuICByZXR1cm4gc2VsZjtcbn1cblxuZnVuY3Rpb24gRHJhZ0hhbmRsZXIoYXhpcywgcm9vdE5vZGUsIG93bmVyRG9jdW1lbnQsIG93bmVyV2luZG93LCB0YXJnZXQsIGRyYWdUcmFja2VyLCBsb2NhdGlvbiwgYW5pbWF0aW9uLCBzY3JvbGxUbywgc2Nyb2xsQm9keSwgc2Nyb2xsVGFyZ2V0LCBpbmRleCwgZXZlbnRIYW5kbGVyLCBwZXJjZW50T2ZWaWV3LCBkcmFnRnJlZSwgZHJhZ1RocmVzaG9sZCwgc2tpcFNuYXBzLCBiYXNlRnJpY3Rpb24sIHdhdGNoRHJhZykge1xuICBjb25zdCB7XG4gICAgY3Jvc3M6IGNyb3NzQXhpcyxcbiAgICBkaXJlY3Rpb25cbiAgfSA9IGF4aXM7XG4gIGNvbnN0IGZvY3VzTm9kZXMgPSBbJ0lOUFVUJywgJ1NFTEVDVCcsICdURVhUQVJFQSddO1xuICBjb25zdCBub25QYXNzaXZlRXZlbnQgPSB7XG4gICAgcGFzc2l2ZTogZmFsc2VcbiAgfTtcbiAgY29uc3QgaW5pdEV2ZW50cyA9IEV2ZW50U3RvcmUoKTtcbiAgY29uc3QgZHJhZ0V2ZW50cyA9IEV2ZW50U3RvcmUoKTtcbiAgY29uc3QgZ29Ub05leHRUaHJlc2hvbGQgPSBMaW1pdCg1MCwgMjI1KS5jb25zdHJhaW4ocGVyY2VudE9mVmlldy5tZWFzdXJlKDIwKSk7XG4gIGNvbnN0IHNuYXBGb3JjZUJvb3N0ID0ge1xuICAgIG1vdXNlOiAzMDAsXG4gICAgdG91Y2g6IDQwMFxuICB9O1xuICBjb25zdCBmcmVlRm9yY2VCb29zdCA9IHtcbiAgICBtb3VzZTogNTAwLFxuICAgIHRvdWNoOiA2MDBcbiAgfTtcbiAgY29uc3QgYmFzZVNwZWVkID0gZHJhZ0ZyZWUgPyA0MyA6IDI1O1xuICBsZXQgaXNNb3ZpbmcgPSBmYWxzZTtcbiAgbGV0IHN0YXJ0U2Nyb2xsID0gMDtcbiAgbGV0IHN0YXJ0Q3Jvc3MgPSAwO1xuICBsZXQgcG9pbnRlcklzRG93biA9IGZhbHNlO1xuICBsZXQgcHJldmVudFNjcm9sbCA9IGZhbHNlO1xuICBsZXQgcHJldmVudENsaWNrID0gZmFsc2U7XG4gIGxldCBpc01vdXNlID0gZmFsc2U7XG4gIGZ1bmN0aW9uIGluaXQoZW1ibGFBcGkpIHtcbiAgICBpZiAoIXdhdGNoRHJhZykgcmV0dXJuO1xuICAgIGZ1bmN0aW9uIGRvd25JZkFsbG93ZWQoZXZ0KSB7XG4gICAgICBpZiAoaXNCb29sZWFuKHdhdGNoRHJhZykgfHwgd2F0Y2hEcmFnKGVtYmxhQXBpLCBldnQpKSBkb3duKGV2dCk7XG4gICAgfVxuICAgIGNvbnN0IG5vZGUgPSByb290Tm9kZTtcbiAgICBpbml0RXZlbnRzLmFkZChub2RlLCAnZHJhZ3N0YXJ0JywgZXZ0ID0+IGV2dC5wcmV2ZW50RGVmYXVsdCgpLCBub25QYXNzaXZlRXZlbnQpLmFkZChub2RlLCAndG91Y2htb3ZlJywgKCkgPT4gdW5kZWZpbmVkLCBub25QYXNzaXZlRXZlbnQpLmFkZChub2RlLCAndG91Y2hlbmQnLCAoKSA9PiB1bmRlZmluZWQpLmFkZChub2RlLCAndG91Y2hzdGFydCcsIGRvd25JZkFsbG93ZWQpLmFkZChub2RlLCAnbW91c2Vkb3duJywgZG93bklmQWxsb3dlZCkuYWRkKG5vZGUsICd0b3VjaGNhbmNlbCcsIHVwKS5hZGQobm9kZSwgJ2NvbnRleHRtZW51JywgdXApLmFkZChub2RlLCAnY2xpY2snLCBjbGljaywgdHJ1ZSk7XG4gIH1cbiAgZnVuY3Rpb24gZGVzdHJveSgpIHtcbiAgICBpbml0RXZlbnRzLmNsZWFyKCk7XG4gICAgZHJhZ0V2ZW50cy5jbGVhcigpO1xuICB9XG4gIGZ1bmN0aW9uIGFkZERyYWdFdmVudHMoKSB7XG4gICAgY29uc3Qgbm9kZSA9IGlzTW91c2UgPyBvd25lckRvY3VtZW50IDogcm9vdE5vZGU7XG4gICAgZHJhZ0V2ZW50cy5hZGQobm9kZSwgJ3RvdWNobW92ZScsIG1vdmUsIG5vblBhc3NpdmVFdmVudCkuYWRkKG5vZGUsICd0b3VjaGVuZCcsIHVwKS5hZGQobm9kZSwgJ21vdXNlbW92ZScsIG1vdmUsIG5vblBhc3NpdmVFdmVudCkuYWRkKG5vZGUsICdtb3VzZXVwJywgdXApO1xuICB9XG4gIGZ1bmN0aW9uIGlzRm9jdXNOb2RlKG5vZGUpIHtcbiAgICBjb25zdCBub2RlTmFtZSA9IG5vZGUubm9kZU5hbWUgfHwgJyc7XG4gICAgcmV0dXJuIGZvY3VzTm9kZXMuaW5jbHVkZXMobm9kZU5hbWUpO1xuICB9XG4gIGZ1bmN0aW9uIGZvcmNlQm9vc3QoKSB7XG4gICAgY29uc3QgYm9vc3QgPSBkcmFnRnJlZSA/IGZyZWVGb3JjZUJvb3N0IDogc25hcEZvcmNlQm9vc3Q7XG4gICAgY29uc3QgdHlwZSA9IGlzTW91c2UgPyAnbW91c2UnIDogJ3RvdWNoJztcbiAgICByZXR1cm4gYm9vc3RbdHlwZV07XG4gIH1cbiAgZnVuY3Rpb24gYWxsb3dlZEZvcmNlKGZvcmNlLCB0YXJnZXRDaGFuZ2VkKSB7XG4gICAgY29uc3QgbmV4dCA9IGluZGV4LmFkZChtYXRoU2lnbihmb3JjZSkgKiAtMSk7XG4gICAgY29uc3QgYmFzZUZvcmNlID0gc2Nyb2xsVGFyZ2V0LmJ5RGlzdGFuY2UoZm9yY2UsICFkcmFnRnJlZSkuZGlzdGFuY2U7XG4gICAgaWYgKGRyYWdGcmVlIHx8IG1hdGhBYnMoZm9yY2UpIDwgZ29Ub05leHRUaHJlc2hvbGQpIHJldHVybiBiYXNlRm9yY2U7XG4gICAgaWYgKHNraXBTbmFwcyAmJiB0YXJnZXRDaGFuZ2VkKSByZXR1cm4gYmFzZUZvcmNlICogMC41O1xuICAgIHJldHVybiBzY3JvbGxUYXJnZXQuYnlJbmRleChuZXh0LmdldCgpLCAwKS5kaXN0YW5jZTtcbiAgfVxuICBmdW5jdGlvbiBkb3duKGV2dCkge1xuICAgIGNvbnN0IGlzTW91c2VFdnQgPSBpc01vdXNlRXZlbnQoZXZ0LCBvd25lcldpbmRvdyk7XG4gICAgaXNNb3VzZSA9IGlzTW91c2VFdnQ7XG4gICAgcHJldmVudENsaWNrID0gZHJhZ0ZyZWUgJiYgaXNNb3VzZUV2dCAmJiAhZXZ0LmJ1dHRvbnMgJiYgaXNNb3Zpbmc7XG4gICAgaXNNb3ZpbmcgPSBkZWx0YUFicyh0YXJnZXQuZ2V0KCksIGxvY2F0aW9uLmdldCgpKSA+PSAyO1xuICAgIGlmIChpc01vdXNlRXZ0ICYmIGV2dC5idXR0b24gIT09IDApIHJldHVybjtcbiAgICBpZiAoaXNGb2N1c05vZGUoZXZ0LnRhcmdldCkpIHJldHVybjtcbiAgICBwb2ludGVySXNEb3duID0gdHJ1ZTtcbiAgICBkcmFnVHJhY2tlci5wb2ludGVyRG93bihldnQpO1xuICAgIHNjcm9sbEJvZHkudXNlRnJpY3Rpb24oMCkudXNlRHVyYXRpb24oMCk7XG4gICAgdGFyZ2V0LnNldChsb2NhdGlvbik7XG4gICAgYWRkRHJhZ0V2ZW50cygpO1xuICAgIHN0YXJ0U2Nyb2xsID0gZHJhZ1RyYWNrZXIucmVhZFBvaW50KGV2dCk7XG4gICAgc3RhcnRDcm9zcyA9IGRyYWdUcmFja2VyLnJlYWRQb2ludChldnQsIGNyb3NzQXhpcyk7XG4gICAgZXZlbnRIYW5kbGVyLmVtaXQoJ3BvaW50ZXJEb3duJyk7XG4gIH1cbiAgZnVuY3Rpb24gbW92ZShldnQpIHtcbiAgICBjb25zdCBpc1RvdWNoRXZ0ID0gIWlzTW91c2VFdmVudChldnQsIG93bmVyV2luZG93KTtcbiAgICBpZiAoaXNUb3VjaEV2dCAmJiBldnQudG91Y2hlcy5sZW5ndGggPj0gMikgcmV0dXJuIHVwKGV2dCk7XG4gICAgY29uc3QgbGFzdFNjcm9sbCA9IGRyYWdUcmFja2VyLnJlYWRQb2ludChldnQpO1xuICAgIGNvbnN0IGxhc3RDcm9zcyA9IGRyYWdUcmFja2VyLnJlYWRQb2ludChldnQsIGNyb3NzQXhpcyk7XG4gICAgY29uc3QgZGlmZlNjcm9sbCA9IGRlbHRhQWJzKGxhc3RTY3JvbGwsIHN0YXJ0U2Nyb2xsKTtcbiAgICBjb25zdCBkaWZmQ3Jvc3MgPSBkZWx0YUFicyhsYXN0Q3Jvc3MsIHN0YXJ0Q3Jvc3MpO1xuICAgIGlmICghcHJldmVudFNjcm9sbCAmJiAhaXNNb3VzZSkge1xuICAgICAgaWYgKCFldnQuY2FuY2VsYWJsZSkgcmV0dXJuIHVwKGV2dCk7XG4gICAgICBwcmV2ZW50U2Nyb2xsID0gZGlmZlNjcm9sbCA+IGRpZmZDcm9zcztcbiAgICAgIGlmICghcHJldmVudFNjcm9sbCkgcmV0dXJuIHVwKGV2dCk7XG4gICAgfVxuICAgIGNvbnN0IGRpZmYgPSBkcmFnVHJhY2tlci5wb2ludGVyTW92ZShldnQpO1xuICAgIGlmIChkaWZmU2Nyb2xsID4gZHJhZ1RocmVzaG9sZCkgcHJldmVudENsaWNrID0gdHJ1ZTtcbiAgICBzY3JvbGxCb2R5LnVzZUZyaWN0aW9uKDAuMykudXNlRHVyYXRpb24oMC43NSk7XG4gICAgYW5pbWF0aW9uLnN0YXJ0KCk7XG4gICAgdGFyZ2V0LmFkZChkaXJlY3Rpb24oZGlmZikpO1xuICAgIGV2dC5wcmV2ZW50RGVmYXVsdCgpO1xuICB9XG4gIGZ1bmN0aW9uIHVwKGV2dCkge1xuICAgIGNvbnN0IGN1cnJlbnRMb2NhdGlvbiA9IHNjcm9sbFRhcmdldC5ieURpc3RhbmNlKDAsIGZhbHNlKTtcbiAgICBjb25zdCB0YXJnZXRDaGFuZ2VkID0gY3VycmVudExvY2F0aW9uLmluZGV4ICE9PSBpbmRleC5nZXQoKTtcbiAgICBjb25zdCByYXdGb3JjZSA9IGRyYWdUcmFja2VyLnBvaW50ZXJVcChldnQpICogZm9yY2VCb29zdCgpO1xuICAgIGNvbnN0IGZvcmNlID0gYWxsb3dlZEZvcmNlKGRpcmVjdGlvbihyYXdGb3JjZSksIHRhcmdldENoYW5nZWQpO1xuICAgIGNvbnN0IGZvcmNlRmFjdG9yID0gZmFjdG9yQWJzKHJhd0ZvcmNlLCBmb3JjZSk7XG4gICAgY29uc3Qgc3BlZWQgPSBiYXNlU3BlZWQgLSAxMCAqIGZvcmNlRmFjdG9yO1xuICAgIGNvbnN0IGZyaWN0aW9uID0gYmFzZUZyaWN0aW9uICsgZm9yY2VGYWN0b3IgLyA1MDtcbiAgICBwcmV2ZW50U2Nyb2xsID0gZmFsc2U7XG4gICAgcG9pbnRlcklzRG93biA9IGZhbHNlO1xuICAgIGRyYWdFdmVudHMuY2xlYXIoKTtcbiAgICBzY3JvbGxCb2R5LnVzZUR1cmF0aW9uKHNwZWVkKS51c2VGcmljdGlvbihmcmljdGlvbik7XG4gICAgc2Nyb2xsVG8uZGlzdGFuY2UoZm9yY2UsICFkcmFnRnJlZSk7XG4gICAgaXNNb3VzZSA9IGZhbHNlO1xuICAgIGV2ZW50SGFuZGxlci5lbWl0KCdwb2ludGVyVXAnKTtcbiAgfVxuICBmdW5jdGlvbiBjbGljayhldnQpIHtcbiAgICBpZiAocHJldmVudENsaWNrKSB7XG4gICAgICBldnQuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICBldnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgIHByZXZlbnRDbGljayA9IGZhbHNlO1xuICAgIH1cbiAgfVxuICBmdW5jdGlvbiBwb2ludGVyRG93bigpIHtcbiAgICByZXR1cm4gcG9pbnRlcklzRG93bjtcbiAgfVxuICBjb25zdCBzZWxmID0ge1xuICAgIGluaXQsXG4gICAgZGVzdHJveSxcbiAgICBwb2ludGVyRG93blxuICB9O1xuICByZXR1cm4gc2VsZjtcbn1cblxuZnVuY3Rpb24gRHJhZ1RyYWNrZXIoYXhpcywgb3duZXJXaW5kb3cpIHtcbiAgY29uc3QgbG9nSW50ZXJ2YWwgPSAxNzA7XG4gIGxldCBzdGFydEV2ZW50O1xuICBsZXQgbGFzdEV2ZW50O1xuICBmdW5jdGlvbiByZWFkVGltZShldnQpIHtcbiAgICByZXR1cm4gZXZ0LnRpbWVTdGFtcDtcbiAgfVxuICBmdW5jdGlvbiByZWFkUG9pbnQoZXZ0LCBldnRBeGlzKSB7XG4gICAgY29uc3QgcHJvcGVydHkgPSBldnRBeGlzIHx8IGF4aXMuc2Nyb2xsO1xuICAgIGNvbnN0IGNvb3JkID0gYGNsaWVudCR7cHJvcGVydHkgPT09ICd4JyA/ICdYJyA6ICdZJ31gO1xuICAgIHJldHVybiAoaXNNb3VzZUV2ZW50KGV2dCwgb3duZXJXaW5kb3cpID8gZXZ0IDogZXZ0LnRvdWNoZXNbMF0pW2Nvb3JkXTtcbiAgfVxuICBmdW5jdGlvbiBwb2ludGVyRG93bihldnQpIHtcbiAgICBzdGFydEV2ZW50ID0gZXZ0O1xuICAgIGxhc3RFdmVudCA9IGV2dDtcbiAgICByZXR1cm4gcmVhZFBvaW50KGV2dCk7XG4gIH1cbiAgZnVuY3Rpb24gcG9pbnRlck1vdmUoZXZ0KSB7XG4gICAgY29uc3QgZGlmZiA9IHJlYWRQb2ludChldnQpIC0gcmVhZFBvaW50KGxhc3RFdmVudCk7XG4gICAgY29uc3QgZXhwaXJlZCA9IHJlYWRUaW1lKGV2dCkgLSByZWFkVGltZShzdGFydEV2ZW50KSA+IGxvZ0ludGVydmFsO1xuICAgIGxhc3RFdmVudCA9IGV2dDtcbiAgICBpZiAoZXhwaXJlZCkgc3RhcnRFdmVudCA9IGV2dDtcbiAgICByZXR1cm4gZGlmZjtcbiAgfVxuICBmdW5jdGlvbiBwb2ludGVyVXAoZXZ0KSB7XG4gICAgaWYgKCFzdGFydEV2ZW50IHx8ICFsYXN0RXZlbnQpIHJldHVybiAwO1xuICAgIGNvbnN0IGRpZmZEcmFnID0gcmVhZFBvaW50KGxhc3RFdmVudCkgLSByZWFkUG9pbnQoc3RhcnRFdmVudCk7XG4gICAgY29uc3QgZGlmZlRpbWUgPSByZWFkVGltZShldnQpIC0gcmVhZFRpbWUoc3RhcnRFdmVudCk7XG4gICAgY29uc3QgZXhwaXJlZCA9IHJlYWRUaW1lKGV2dCkgLSByZWFkVGltZShsYXN0RXZlbnQpID4gbG9nSW50ZXJ2YWw7XG4gICAgY29uc3QgZm9yY2UgPSBkaWZmRHJhZyAvIGRpZmZUaW1lO1xuICAgIGNvbnN0IGlzRmxpY2sgPSBkaWZmVGltZSAmJiAhZXhwaXJlZCAmJiBtYXRoQWJzKGZvcmNlKSA+IDAuMTtcbiAgICByZXR1cm4gaXNGbGljayA/IGZvcmNlIDogMDtcbiAgfVxuICBjb25zdCBzZWxmID0ge1xuICAgIHBvaW50ZXJEb3duLFxuICAgIHBvaW50ZXJNb3ZlLFxuICAgIHBvaW50ZXJVcCxcbiAgICByZWFkUG9pbnRcbiAgfTtcbiAgcmV0dXJuIHNlbGY7XG59XG5cbmZ1bmN0aW9uIE5vZGVSZWN0cygpIHtcbiAgZnVuY3Rpb24gbWVhc3VyZShub2RlKSB7XG4gICAgY29uc3Qge1xuICAgICAgb2Zmc2V0VG9wLFxuICAgICAgb2Zmc2V0TGVmdCxcbiAgICAgIG9mZnNldFdpZHRoLFxuICAgICAgb2Zmc2V0SGVpZ2h0XG4gICAgfSA9IG5vZGU7XG4gICAgY29uc3Qgb2Zmc2V0ID0ge1xuICAgICAgdG9wOiBvZmZzZXRUb3AsXG4gICAgICByaWdodDogb2Zmc2V0TGVmdCArIG9mZnNldFdpZHRoLFxuICAgICAgYm90dG9tOiBvZmZzZXRUb3AgKyBvZmZzZXRIZWlnaHQsXG4gICAgICBsZWZ0OiBvZmZzZXRMZWZ0LFxuICAgICAgd2lkdGg6IG9mZnNldFdpZHRoLFxuICAgICAgaGVpZ2h0OiBvZmZzZXRIZWlnaHRcbiAgICB9O1xuICAgIHJldHVybiBvZmZzZXQ7XG4gIH1cbiAgY29uc3Qgc2VsZiA9IHtcbiAgICBtZWFzdXJlXG4gIH07XG4gIHJldHVybiBzZWxmO1xufVxuXG5mdW5jdGlvbiBQZXJjZW50T2ZWaWV3KHZpZXdTaXplKSB7XG4gIGZ1bmN0aW9uIG1lYXN1cmUobikge1xuICAgIHJldHVybiB2aWV3U2l6ZSAqIChuIC8gMTAwKTtcbiAgfVxuICBjb25zdCBzZWxmID0ge1xuICAgIG1lYXN1cmVcbiAgfTtcbiAgcmV0dXJuIHNlbGY7XG59XG5cbmZ1bmN0aW9uIFJlc2l6ZUhhbmRsZXIoY29udGFpbmVyLCBldmVudEhhbmRsZXIsIG93bmVyV2luZG93LCBzbGlkZXMsIGF4aXMsIHdhdGNoUmVzaXplLCBub2RlUmVjdHMpIHtcbiAgY29uc3Qgb2JzZXJ2ZU5vZGVzID0gW2NvbnRhaW5lcl0uY29uY2F0KHNsaWRlcyk7XG4gIGxldCByZXNpemVPYnNlcnZlcjtcbiAgbGV0IGNvbnRhaW5lclNpemU7XG4gIGxldCBzbGlkZVNpemVzID0gW107XG4gIGxldCBkZXN0cm95ZWQgPSBmYWxzZTtcbiAgZnVuY3Rpb24gcmVhZFNpemUobm9kZSkge1xuICAgIHJldHVybiBheGlzLm1lYXN1cmVTaXplKG5vZGVSZWN0cy5tZWFzdXJlKG5vZGUpKTtcbiAgfVxuICBmdW5jdGlvbiBpbml0KGVtYmxhQXBpKSB7XG4gICAgaWYgKCF3YXRjaFJlc2l6ZSkgcmV0dXJuO1xuICAgIGNvbnRhaW5lclNpemUgPSByZWFkU2l6ZShjb250YWluZXIpO1xuICAgIHNsaWRlU2l6ZXMgPSBzbGlkZXMubWFwKHJlYWRTaXplKTtcbiAgICBmdW5jdGlvbiBkZWZhdWx0Q2FsbGJhY2soZW50cmllcykge1xuICAgICAgZm9yIChjb25zdCBlbnRyeSBvZiBlbnRyaWVzKSB7XG4gICAgICAgIGlmIChkZXN0cm95ZWQpIHJldHVybjtcbiAgICAgICAgY29uc3QgaXNDb250YWluZXIgPSBlbnRyeS50YXJnZXQgPT09IGNvbnRhaW5lcjtcbiAgICAgICAgY29uc3Qgc2xpZGVJbmRleCA9IHNsaWRlcy5pbmRleE9mKGVudHJ5LnRhcmdldCk7XG4gICAgICAgIGNvbnN0IGxhc3RTaXplID0gaXNDb250YWluZXIgPyBjb250YWluZXJTaXplIDogc2xpZGVTaXplc1tzbGlkZUluZGV4XTtcbiAgICAgICAgY29uc3QgbmV3U2l6ZSA9IHJlYWRTaXplKGlzQ29udGFpbmVyID8gY29udGFpbmVyIDogc2xpZGVzW3NsaWRlSW5kZXhdKTtcbiAgICAgICAgY29uc3QgZGlmZlNpemUgPSBtYXRoQWJzKG5ld1NpemUgLSBsYXN0U2l6ZSk7XG4gICAgICAgIGlmIChkaWZmU2l6ZSA+PSAwLjUpIHtcbiAgICAgICAgICBlbWJsYUFwaS5yZUluaXQoKTtcbiAgICAgICAgICBldmVudEhhbmRsZXIuZW1pdCgncmVzaXplJyk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gICAgcmVzaXplT2JzZXJ2ZXIgPSBuZXcgUmVzaXplT2JzZXJ2ZXIoZW50cmllcyA9PiB7XG4gICAgICBpZiAoaXNCb29sZWFuKHdhdGNoUmVzaXplKSB8fCB3YXRjaFJlc2l6ZShlbWJsYUFwaSwgZW50cmllcykpIHtcbiAgICAgICAgZGVmYXVsdENhbGxiYWNrKGVudHJpZXMpO1xuICAgICAgfVxuICAgIH0pO1xuICAgIG93bmVyV2luZG93LnJlcXVlc3RBbmltYXRpb25GcmFtZSgoKSA9PiB7XG4gICAgICBvYnNlcnZlTm9kZXMuZm9yRWFjaChub2RlID0+IHJlc2l6ZU9ic2VydmVyLm9ic2VydmUobm9kZSkpO1xuICAgIH0pO1xuICB9XG4gIGZ1bmN0aW9uIGRlc3Ryb3koKSB7XG4gICAgZGVzdHJveWVkID0gdHJ1ZTtcbiAgICBpZiAocmVzaXplT2JzZXJ2ZXIpIHJlc2l6ZU9ic2VydmVyLmRpc2Nvbm5lY3QoKTtcbiAgfVxuICBjb25zdCBzZWxmID0ge1xuICAgIGluaXQsXG4gICAgZGVzdHJveVxuICB9O1xuICByZXR1cm4gc2VsZjtcbn1cblxuZnVuY3Rpb24gU2Nyb2xsQm9keShsb2NhdGlvbiwgb2Zmc2V0TG9jYXRpb24sIHByZXZpb3VzTG9jYXRpb24sIHRhcmdldCwgYmFzZUR1cmF0aW9uLCBiYXNlRnJpY3Rpb24pIHtcbiAgbGV0IHNjcm9sbFZlbG9jaXR5ID0gMDtcbiAgbGV0IHNjcm9sbERpcmVjdGlvbiA9IDA7XG4gIGxldCBzY3JvbGxEdXJhdGlvbiA9IGJhc2VEdXJhdGlvbjtcbiAgbGV0IHNjcm9sbEZyaWN0aW9uID0gYmFzZUZyaWN0aW9uO1xuICBsZXQgcmF3TG9jYXRpb24gPSBsb2NhdGlvbi5nZXQoKTtcbiAgbGV0IHJhd0xvY2F0aW9uUHJldmlvdXMgPSAwO1xuICBmdW5jdGlvbiBzZWVrKCkge1xuICAgIGNvbnN0IGRpc3BsYWNlbWVudCA9IHRhcmdldC5nZXQoKSAtIGxvY2F0aW9uLmdldCgpO1xuICAgIGNvbnN0IGlzSW5zdGFudCA9ICFzY3JvbGxEdXJhdGlvbjtcbiAgICBsZXQgc2Nyb2xsRGlzdGFuY2UgPSAwO1xuICAgIGlmIChpc0luc3RhbnQpIHtcbiAgICAgIHNjcm9sbFZlbG9jaXR5ID0gMDtcbiAgICAgIHByZXZpb3VzTG9jYXRpb24uc2V0KHRhcmdldCk7XG4gICAgICBsb2NhdGlvbi5zZXQodGFyZ2V0KTtcbiAgICAgIHNjcm9sbERpc3RhbmNlID0gZGlzcGxhY2VtZW50O1xuICAgIH0gZWxzZSB7XG4gICAgICBwcmV2aW91c0xvY2F0aW9uLnNldChsb2NhdGlvbik7XG4gICAgICBzY3JvbGxWZWxvY2l0eSArPSBkaXNwbGFjZW1lbnQgLyBzY3JvbGxEdXJhdGlvbjtcbiAgICAgIHNjcm9sbFZlbG9jaXR5ICo9IHNjcm9sbEZyaWN0aW9uO1xuICAgICAgcmF3TG9jYXRpb24gKz0gc2Nyb2xsVmVsb2NpdHk7XG4gICAgICBsb2NhdGlvbi5hZGQoc2Nyb2xsVmVsb2NpdHkpO1xuICAgICAgc2Nyb2xsRGlzdGFuY2UgPSByYXdMb2NhdGlvbiAtIHJhd0xvY2F0aW9uUHJldmlvdXM7XG4gICAgfVxuICAgIHNjcm9sbERpcmVjdGlvbiA9IG1hdGhTaWduKHNjcm9sbERpc3RhbmNlKTtcbiAgICByYXdMb2NhdGlvblByZXZpb3VzID0gcmF3TG9jYXRpb247XG4gICAgcmV0dXJuIHNlbGY7XG4gIH1cbiAgZnVuY3Rpb24gc2V0dGxlZCgpIHtcbiAgICBjb25zdCBkaWZmID0gdGFyZ2V0LmdldCgpIC0gb2Zmc2V0TG9jYXRpb24uZ2V0KCk7XG4gICAgcmV0dXJuIG1hdGhBYnMoZGlmZikgPCAwLjAwMTtcbiAgfVxuICBmdW5jdGlvbiBkdXJhdGlvbigpIHtcbiAgICByZXR1cm4gc2Nyb2xsRHVyYXRpb247XG4gIH1cbiAgZnVuY3Rpb24gZGlyZWN0aW9uKCkge1xuICAgIHJldHVybiBzY3JvbGxEaXJlY3Rpb247XG4gIH1cbiAgZnVuY3Rpb24gdmVsb2NpdHkoKSB7XG4gICAgcmV0dXJuIHNjcm9sbFZlbG9jaXR5O1xuICB9XG4gIGZ1bmN0aW9uIHVzZUJhc2VEdXJhdGlvbigpIHtcbiAgICByZXR1cm4gdXNlRHVyYXRpb24oYmFzZUR1cmF0aW9uKTtcbiAgfVxuICBmdW5jdGlvbiB1c2VCYXNlRnJpY3Rpb24oKSB7XG4gICAgcmV0dXJuIHVzZUZyaWN0aW9uKGJhc2VGcmljdGlvbik7XG4gIH1cbiAgZnVuY3Rpb24gdXNlRHVyYXRpb24obikge1xuICAgIHNjcm9sbER1cmF0aW9uID0gbjtcbiAgICByZXR1cm4gc2VsZjtcbiAgfVxuICBmdW5jdGlvbiB1c2VGcmljdGlvbihuKSB7XG4gICAgc2Nyb2xsRnJpY3Rpb24gPSBuO1xuICAgIHJldHVybiBzZWxmO1xuICB9XG4gIGNvbnN0IHNlbGYgPSB7XG4gICAgZGlyZWN0aW9uLFxuICAgIGR1cmF0aW9uLFxuICAgIHZlbG9jaXR5LFxuICAgIHNlZWssXG4gICAgc2V0dGxlZCxcbiAgICB1c2VCYXNlRnJpY3Rpb24sXG4gICAgdXNlQmFzZUR1cmF0aW9uLFxuICAgIHVzZUZyaWN0aW9uLFxuICAgIHVzZUR1cmF0aW9uXG4gIH07XG4gIHJldHVybiBzZWxmO1xufVxuXG5mdW5jdGlvbiBTY3JvbGxCb3VuZHMobGltaXQsIGxvY2F0aW9uLCB0YXJnZXQsIHNjcm9sbEJvZHksIHBlcmNlbnRPZlZpZXcpIHtcbiAgY29uc3QgcHVsbEJhY2tUaHJlc2hvbGQgPSBwZXJjZW50T2ZWaWV3Lm1lYXN1cmUoMTApO1xuICBjb25zdCBlZGdlT2Zmc2V0VG9sZXJhbmNlID0gcGVyY2VudE9mVmlldy5tZWFzdXJlKDUwKTtcbiAgY29uc3QgZnJpY3Rpb25MaW1pdCA9IExpbWl0KDAuMSwgMC45OSk7XG4gIGxldCBkaXNhYmxlZCA9IGZhbHNlO1xuICBmdW5jdGlvbiBzaG91bGRDb25zdHJhaW4oKSB7XG4gICAgaWYgKGRpc2FibGVkKSByZXR1cm4gZmFsc2U7XG4gICAgaWYgKCFsaW1pdC5yZWFjaGVkQW55KHRhcmdldC5nZXQoKSkpIHJldHVybiBmYWxzZTtcbiAgICBpZiAoIWxpbWl0LnJlYWNoZWRBbnkobG9jYXRpb24uZ2V0KCkpKSByZXR1cm4gZmFsc2U7XG4gICAgcmV0dXJuIHRydWU7XG4gIH1cbiAgZnVuY3Rpb24gY29uc3RyYWluKHBvaW50ZXJEb3duKSB7XG4gICAgaWYgKCFzaG91bGRDb25zdHJhaW4oKSkgcmV0dXJuO1xuICAgIGNvbnN0IGVkZ2UgPSBsaW1pdC5yZWFjaGVkTWluKGxvY2F0aW9uLmdldCgpKSA/ICdtaW4nIDogJ21heCc7XG4gICAgY29uc3QgZGlmZlRvRWRnZSA9IG1hdGhBYnMobGltaXRbZWRnZV0gLSBsb2NhdGlvbi5nZXQoKSk7XG4gICAgY29uc3QgZGlmZlRvVGFyZ2V0ID0gdGFyZ2V0LmdldCgpIC0gbG9jYXRpb24uZ2V0KCk7XG4gICAgY29uc3QgZnJpY3Rpb24gPSBmcmljdGlvbkxpbWl0LmNvbnN0cmFpbihkaWZmVG9FZGdlIC8gZWRnZU9mZnNldFRvbGVyYW5jZSk7XG4gICAgdGFyZ2V0LnN1YnRyYWN0KGRpZmZUb1RhcmdldCAqIGZyaWN0aW9uKTtcbiAgICBpZiAoIXBvaW50ZXJEb3duICYmIG1hdGhBYnMoZGlmZlRvVGFyZ2V0KSA8IHB1bGxCYWNrVGhyZXNob2xkKSB7XG4gICAgICB0YXJnZXQuc2V0KGxpbWl0LmNvbnN0cmFpbih0YXJnZXQuZ2V0KCkpKTtcbiAgICAgIHNjcm9sbEJvZHkudXNlRHVyYXRpb24oMjUpLnVzZUJhc2VGcmljdGlvbigpO1xuICAgIH1cbiAgfVxuICBmdW5jdGlvbiB0b2dnbGVBY3RpdmUoYWN0aXZlKSB7XG4gICAgZGlzYWJsZWQgPSAhYWN0aXZlO1xuICB9XG4gIGNvbnN0IHNlbGYgPSB7XG4gICAgc2hvdWxkQ29uc3RyYWluLFxuICAgIGNvbnN0cmFpbixcbiAgICB0b2dnbGVBY3RpdmVcbiAgfTtcbiAgcmV0dXJuIHNlbGY7XG59XG5cbmZ1bmN0aW9uIFNjcm9sbENvbnRhaW4odmlld1NpemUsIGNvbnRlbnRTaXplLCBzbmFwc0FsaWduZWQsIGNvbnRhaW5TY3JvbGwsIHBpeGVsVG9sZXJhbmNlKSB7XG4gIGNvbnN0IHNjcm9sbEJvdW5kcyA9IExpbWl0KC1jb250ZW50U2l6ZSArIHZpZXdTaXplLCAwKTtcbiAgY29uc3Qgc25hcHNCb3VuZGVkID0gbWVhc3VyZUJvdW5kZWQoKTtcbiAgY29uc3Qgc2Nyb2xsQ29udGFpbkxpbWl0ID0gZmluZFNjcm9sbENvbnRhaW5MaW1pdCgpO1xuICBjb25zdCBzbmFwc0NvbnRhaW5lZCA9IG1lYXN1cmVDb250YWluZWQoKTtcbiAgZnVuY3Rpb24gdXNlUGl4ZWxUb2xlcmFuY2UoYm91bmQsIHNuYXApIHtcbiAgICByZXR1cm4gZGVsdGFBYnMoYm91bmQsIHNuYXApIDw9IDE7XG4gIH1cbiAgZnVuY3Rpb24gZmluZFNjcm9sbENvbnRhaW5MaW1pdCgpIHtcbiAgICBjb25zdCBzdGFydFNuYXAgPSBzbmFwc0JvdW5kZWRbMF07XG4gICAgY29uc3QgZW5kU25hcCA9IGFycmF5TGFzdChzbmFwc0JvdW5kZWQpO1xuICAgIGNvbnN0IG1pbiA9IHNuYXBzQm91bmRlZC5sYXN0SW5kZXhPZihzdGFydFNuYXApO1xuICAgIGNvbnN0IG1heCA9IHNuYXBzQm91bmRlZC5pbmRleE9mKGVuZFNuYXApICsgMTtcbiAgICByZXR1cm4gTGltaXQobWluLCBtYXgpO1xuICB9XG4gIGZ1bmN0aW9uIG1lYXN1cmVCb3VuZGVkKCkge1xuICAgIHJldHVybiBzbmFwc0FsaWduZWQubWFwKChzbmFwQWxpZ25lZCwgaW5kZXgpID0+IHtcbiAgICAgIGNvbnN0IHtcbiAgICAgICAgbWluLFxuICAgICAgICBtYXhcbiAgICAgIH0gPSBzY3JvbGxCb3VuZHM7XG4gICAgICBjb25zdCBzbmFwID0gc2Nyb2xsQm91bmRzLmNvbnN0cmFpbihzbmFwQWxpZ25lZCk7XG4gICAgICBjb25zdCBpc0ZpcnN0ID0gIWluZGV4O1xuICAgICAgY29uc3QgaXNMYXN0ID0gYXJyYXlJc0xhc3RJbmRleChzbmFwc0FsaWduZWQsIGluZGV4KTtcbiAgICAgIGlmIChpc0ZpcnN0KSByZXR1cm4gbWF4O1xuICAgICAgaWYgKGlzTGFzdCkgcmV0dXJuIG1pbjtcbiAgICAgIGlmICh1c2VQaXhlbFRvbGVyYW5jZShtaW4sIHNuYXApKSByZXR1cm4gbWluO1xuICAgICAgaWYgKHVzZVBpeGVsVG9sZXJhbmNlKG1heCwgc25hcCkpIHJldHVybiBtYXg7XG4gICAgICByZXR1cm4gc25hcDtcbiAgICB9KS5tYXAoc2Nyb2xsQm91bmQgPT4gcGFyc2VGbG9hdChzY3JvbGxCb3VuZC50b0ZpeGVkKDMpKSk7XG4gIH1cbiAgZnVuY3Rpb24gbWVhc3VyZUNvbnRhaW5lZCgpIHtcbiAgICBpZiAoY29udGVudFNpemUgPD0gdmlld1NpemUgKyBwaXhlbFRvbGVyYW5jZSkgcmV0dXJuIFtzY3JvbGxCb3VuZHMubWF4XTtcbiAgICBpZiAoY29udGFpblNjcm9sbCA9PT0gJ2tlZXBTbmFwcycpIHJldHVybiBzbmFwc0JvdW5kZWQ7XG4gICAgY29uc3Qge1xuICAgICAgbWluLFxuICAgICAgbWF4XG4gICAgfSA9IHNjcm9sbENvbnRhaW5MaW1pdDtcbiAgICByZXR1cm4gc25hcHNCb3VuZGVkLnNsaWNlKG1pbiwgbWF4KTtcbiAgfVxuICBjb25zdCBzZWxmID0ge1xuICAgIHNuYXBzQ29udGFpbmVkLFxuICAgIHNjcm9sbENvbnRhaW5MaW1pdFxuICB9O1xuICByZXR1cm4gc2VsZjtcbn1cblxuZnVuY3Rpb24gU2Nyb2xsTGltaXQoY29udGVudFNpemUsIHNjcm9sbFNuYXBzLCBsb29wKSB7XG4gIGNvbnN0IG1heCA9IHNjcm9sbFNuYXBzWzBdO1xuICBjb25zdCBtaW4gPSBsb29wID8gbWF4IC0gY29udGVudFNpemUgOiBhcnJheUxhc3Qoc2Nyb2xsU25hcHMpO1xuICBjb25zdCBsaW1pdCA9IExpbWl0KG1pbiwgbWF4KTtcbiAgY29uc3Qgc2VsZiA9IHtcbiAgICBsaW1pdFxuICB9O1xuICByZXR1cm4gc2VsZjtcbn1cblxuZnVuY3Rpb24gU2Nyb2xsTG9vcGVyKGNvbnRlbnRTaXplLCBsaW1pdCwgbG9jYXRpb24sIHZlY3RvcnMpIHtcbiAgY29uc3Qgam9pbnRTYWZldHkgPSAwLjE7XG4gIGNvbnN0IG1pbiA9IGxpbWl0Lm1pbiArIGpvaW50U2FmZXR5O1xuICBjb25zdCBtYXggPSBsaW1pdC5tYXggKyBqb2ludFNhZmV0eTtcbiAgY29uc3Qge1xuICAgIHJlYWNoZWRNaW4sXG4gICAgcmVhY2hlZE1heFxuICB9ID0gTGltaXQobWluLCBtYXgpO1xuICBmdW5jdGlvbiBzaG91bGRMb29wKGRpcmVjdGlvbikge1xuICAgIGlmIChkaXJlY3Rpb24gPT09IDEpIHJldHVybiByZWFjaGVkTWF4KGxvY2F0aW9uLmdldCgpKTtcbiAgICBpZiAoZGlyZWN0aW9uID09PSAtMSkgcmV0dXJuIHJlYWNoZWRNaW4obG9jYXRpb24uZ2V0KCkpO1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICBmdW5jdGlvbiBsb29wKGRpcmVjdGlvbikge1xuICAgIGlmICghc2hvdWxkTG9vcChkaXJlY3Rpb24pKSByZXR1cm47XG4gICAgY29uc3QgbG9vcERpc3RhbmNlID0gY29udGVudFNpemUgKiAoZGlyZWN0aW9uICogLTEpO1xuICAgIHZlY3RvcnMuZm9yRWFjaCh2ID0+IHYuYWRkKGxvb3BEaXN0YW5jZSkpO1xuICB9XG4gIGNvbnN0IHNlbGYgPSB7XG4gICAgbG9vcFxuICB9O1xuICByZXR1cm4gc2VsZjtcbn1cblxuZnVuY3Rpb24gU2Nyb2xsUHJvZ3Jlc3MobGltaXQpIHtcbiAgY29uc3Qge1xuICAgIG1heCxcbiAgICBsZW5ndGhcbiAgfSA9IGxpbWl0O1xuICBmdW5jdGlvbiBnZXQobikge1xuICAgIGNvbnN0IGN1cnJlbnRMb2NhdGlvbiA9IG4gLSBtYXg7XG4gICAgcmV0dXJuIGxlbmd0aCA/IGN1cnJlbnRMb2NhdGlvbiAvIC1sZW5ndGggOiAwO1xuICB9XG4gIGNvbnN0IHNlbGYgPSB7XG4gICAgZ2V0XG4gIH07XG4gIHJldHVybiBzZWxmO1xufVxuXG5mdW5jdGlvbiBTY3JvbGxTbmFwcyhheGlzLCBhbGlnbm1lbnQsIGNvbnRhaW5lclJlY3QsIHNsaWRlUmVjdHMsIHNsaWRlc1RvU2Nyb2xsKSB7XG4gIGNvbnN0IHtcbiAgICBzdGFydEVkZ2UsXG4gICAgZW5kRWRnZVxuICB9ID0gYXhpcztcbiAgY29uc3Qge1xuICAgIGdyb3VwU2xpZGVzXG4gIH0gPSBzbGlkZXNUb1Njcm9sbDtcbiAgY29uc3QgYWxpZ25tZW50cyA9IG1lYXN1cmVTaXplcygpLm1hcChhbGlnbm1lbnQubWVhc3VyZSk7XG4gIGNvbnN0IHNuYXBzID0gbWVhc3VyZVVuYWxpZ25lZCgpO1xuICBjb25zdCBzbmFwc0FsaWduZWQgPSBtZWFzdXJlQWxpZ25lZCgpO1xuICBmdW5jdGlvbiBtZWFzdXJlU2l6ZXMoKSB7XG4gICAgcmV0dXJuIGdyb3VwU2xpZGVzKHNsaWRlUmVjdHMpLm1hcChyZWN0cyA9PiBhcnJheUxhc3QocmVjdHMpW2VuZEVkZ2VdIC0gcmVjdHNbMF1bc3RhcnRFZGdlXSkubWFwKG1hdGhBYnMpO1xuICB9XG4gIGZ1bmN0aW9uIG1lYXN1cmVVbmFsaWduZWQoKSB7XG4gICAgcmV0dXJuIHNsaWRlUmVjdHMubWFwKHJlY3QgPT4gY29udGFpbmVyUmVjdFtzdGFydEVkZ2VdIC0gcmVjdFtzdGFydEVkZ2VdKS5tYXAoc25hcCA9PiAtbWF0aEFicyhzbmFwKSk7XG4gIH1cbiAgZnVuY3Rpb24gbWVhc3VyZUFsaWduZWQoKSB7XG4gICAgcmV0dXJuIGdyb3VwU2xpZGVzKHNuYXBzKS5tYXAoZyA9PiBnWzBdKS5tYXAoKHNuYXAsIGluZGV4KSA9PiBzbmFwICsgYWxpZ25tZW50c1tpbmRleF0pO1xuICB9XG4gIGNvbnN0IHNlbGYgPSB7XG4gICAgc25hcHMsXG4gICAgc25hcHNBbGlnbmVkXG4gIH07XG4gIHJldHVybiBzZWxmO1xufVxuXG5mdW5jdGlvbiBTbGlkZVJlZ2lzdHJ5KGNvbnRhaW5TbmFwcywgY29udGFpblNjcm9sbCwgc2Nyb2xsU25hcHMsIHNjcm9sbENvbnRhaW5MaW1pdCwgc2xpZGVzVG9TY3JvbGwsIHNsaWRlSW5kZXhlcykge1xuICBjb25zdCB7XG4gICAgZ3JvdXBTbGlkZXNcbiAgfSA9IHNsaWRlc1RvU2Nyb2xsO1xuICBjb25zdCB7XG4gICAgbWluLFxuICAgIG1heFxuICB9ID0gc2Nyb2xsQ29udGFpbkxpbWl0O1xuICBjb25zdCBzbGlkZVJlZ2lzdHJ5ID0gY3JlYXRlU2xpZGVSZWdpc3RyeSgpO1xuICBmdW5jdGlvbiBjcmVhdGVTbGlkZVJlZ2lzdHJ5KCkge1xuICAgIGNvbnN0IGdyb3VwZWRTbGlkZUluZGV4ZXMgPSBncm91cFNsaWRlcyhzbGlkZUluZGV4ZXMpO1xuICAgIGNvbnN0IGRvTm90Q29udGFpbiA9ICFjb250YWluU25hcHMgfHwgY29udGFpblNjcm9sbCA9PT0gJ2tlZXBTbmFwcyc7XG4gICAgaWYgKHNjcm9sbFNuYXBzLmxlbmd0aCA9PT0gMSkgcmV0dXJuIFtzbGlkZUluZGV4ZXNdO1xuICAgIGlmIChkb05vdENvbnRhaW4pIHJldHVybiBncm91cGVkU2xpZGVJbmRleGVzO1xuICAgIHJldHVybiBncm91cGVkU2xpZGVJbmRleGVzLnNsaWNlKG1pbiwgbWF4KS5tYXAoKGdyb3VwLCBpbmRleCwgZ3JvdXBzKSA9PiB7XG4gICAgICBjb25zdCBpc0ZpcnN0ID0gIWluZGV4O1xuICAgICAgY29uc3QgaXNMYXN0ID0gYXJyYXlJc0xhc3RJbmRleChncm91cHMsIGluZGV4KTtcbiAgICAgIGlmIChpc0ZpcnN0KSB7XG4gICAgICAgIGNvbnN0IHJhbmdlID0gYXJyYXlMYXN0KGdyb3Vwc1swXSkgKyAxO1xuICAgICAgICByZXR1cm4gYXJyYXlGcm9tTnVtYmVyKHJhbmdlKTtcbiAgICAgIH1cbiAgICAgIGlmIChpc0xhc3QpIHtcbiAgICAgICAgY29uc3QgcmFuZ2UgPSBhcnJheUxhc3RJbmRleChzbGlkZUluZGV4ZXMpIC0gYXJyYXlMYXN0KGdyb3VwcylbMF0gKyAxO1xuICAgICAgICByZXR1cm4gYXJyYXlGcm9tTnVtYmVyKHJhbmdlLCBhcnJheUxhc3QoZ3JvdXBzKVswXSk7XG4gICAgICB9XG4gICAgICByZXR1cm4gZ3JvdXA7XG4gICAgfSk7XG4gIH1cbiAgY29uc3Qgc2VsZiA9IHtcbiAgICBzbGlkZVJlZ2lzdHJ5XG4gIH07XG4gIHJldHVybiBzZWxmO1xufVxuXG5mdW5jdGlvbiBTY3JvbGxUYXJnZXQobG9vcCwgc2Nyb2xsU25hcHMsIGNvbnRlbnRTaXplLCBsaW1pdCwgdGFyZ2V0VmVjdG9yKSB7XG4gIGNvbnN0IHtcbiAgICByZWFjaGVkQW55LFxuICAgIHJlbW92ZU9mZnNldCxcbiAgICBjb25zdHJhaW5cbiAgfSA9IGxpbWl0O1xuICBmdW5jdGlvbiBtaW5EaXN0YW5jZShkaXN0YW5jZXMpIHtcbiAgICByZXR1cm4gZGlzdGFuY2VzLmNvbmNhdCgpLnNvcnQoKGEsIGIpID0+IG1hdGhBYnMoYSkgLSBtYXRoQWJzKGIpKVswXTtcbiAgfVxuICBmdW5jdGlvbiBmaW5kVGFyZ2V0U25hcCh0YXJnZXQpIHtcbiAgICBjb25zdCBkaXN0YW5jZSA9IGxvb3AgPyByZW1vdmVPZmZzZXQodGFyZ2V0KSA6IGNvbnN0cmFpbih0YXJnZXQpO1xuICAgIGNvbnN0IGFzY0RpZmZzVG9TbmFwcyA9IHNjcm9sbFNuYXBzLm1hcCgoc25hcCwgaW5kZXgpID0+ICh7XG4gICAgICBkaWZmOiBzaG9ydGN1dChzbmFwIC0gZGlzdGFuY2UsIDApLFxuICAgICAgaW5kZXhcbiAgICB9KSkuc29ydCgoZDEsIGQyKSA9PiBtYXRoQWJzKGQxLmRpZmYpIC0gbWF0aEFicyhkMi5kaWZmKSk7XG4gICAgY29uc3Qge1xuICAgICAgaW5kZXhcbiAgICB9ID0gYXNjRGlmZnNUb1NuYXBzWzBdO1xuICAgIHJldHVybiB7XG4gICAgICBpbmRleCxcbiAgICAgIGRpc3RhbmNlXG4gICAgfTtcbiAgfVxuICBmdW5jdGlvbiBzaG9ydGN1dCh0YXJnZXQsIGRpcmVjdGlvbikge1xuICAgIGNvbnN0IHRhcmdldHMgPSBbdGFyZ2V0LCB0YXJnZXQgKyBjb250ZW50U2l6ZSwgdGFyZ2V0IC0gY29udGVudFNpemVdO1xuICAgIGlmICghbG9vcCkgcmV0dXJuIHRhcmdldDtcbiAgICBpZiAoIWRpcmVjdGlvbikgcmV0dXJuIG1pbkRpc3RhbmNlKHRhcmdldHMpO1xuICAgIGNvbnN0IG1hdGNoaW5nVGFyZ2V0cyA9IHRhcmdldHMuZmlsdGVyKHQgPT4gbWF0aFNpZ24odCkgPT09IGRpcmVjdGlvbik7XG4gICAgaWYgKG1hdGNoaW5nVGFyZ2V0cy5sZW5ndGgpIHJldHVybiBtaW5EaXN0YW5jZShtYXRjaGluZ1RhcmdldHMpO1xuICAgIHJldHVybiBhcnJheUxhc3QodGFyZ2V0cykgLSBjb250ZW50U2l6ZTtcbiAgfVxuICBmdW5jdGlvbiBieUluZGV4KGluZGV4LCBkaXJlY3Rpb24pIHtcbiAgICBjb25zdCBkaWZmVG9TbmFwID0gc2Nyb2xsU25hcHNbaW5kZXhdIC0gdGFyZ2V0VmVjdG9yLmdldCgpO1xuICAgIGNvbnN0IGRpc3RhbmNlID0gc2hvcnRjdXQoZGlmZlRvU25hcCwgZGlyZWN0aW9uKTtcbiAgICByZXR1cm4ge1xuICAgICAgaW5kZXgsXG4gICAgICBkaXN0YW5jZVxuICAgIH07XG4gIH1cbiAgZnVuY3Rpb24gYnlEaXN0YW5jZShkaXN0YW5jZSwgc25hcCkge1xuICAgIGNvbnN0IHRhcmdldCA9IHRhcmdldFZlY3Rvci5nZXQoKSArIGRpc3RhbmNlO1xuICAgIGNvbnN0IHtcbiAgICAgIGluZGV4LFxuICAgICAgZGlzdGFuY2U6IHRhcmdldFNuYXBEaXN0YW5jZVxuICAgIH0gPSBmaW5kVGFyZ2V0U25hcCh0YXJnZXQpO1xuICAgIGNvbnN0IHJlYWNoZWRCb3VuZCA9ICFsb29wICYmIHJlYWNoZWRBbnkodGFyZ2V0KTtcbiAgICBpZiAoIXNuYXAgfHwgcmVhY2hlZEJvdW5kKSByZXR1cm4ge1xuICAgICAgaW5kZXgsXG4gICAgICBkaXN0YW5jZVxuICAgIH07XG4gICAgY29uc3QgZGlmZlRvU25hcCA9IHNjcm9sbFNuYXBzW2luZGV4XSAtIHRhcmdldFNuYXBEaXN0YW5jZTtcbiAgICBjb25zdCBzbmFwRGlzdGFuY2UgPSBkaXN0YW5jZSArIHNob3J0Y3V0KGRpZmZUb1NuYXAsIDApO1xuICAgIHJldHVybiB7XG4gICAgICBpbmRleCxcbiAgICAgIGRpc3RhbmNlOiBzbmFwRGlzdGFuY2VcbiAgICB9O1xuICB9XG4gIGNvbnN0IHNlbGYgPSB7XG4gICAgYnlEaXN0YW5jZSxcbiAgICBieUluZGV4LFxuICAgIHNob3J0Y3V0XG4gIH07XG4gIHJldHVybiBzZWxmO1xufVxuXG5mdW5jdGlvbiBTY3JvbGxUbyhhbmltYXRpb24sIGluZGV4Q3VycmVudCwgaW5kZXhQcmV2aW91cywgc2Nyb2xsQm9keSwgc2Nyb2xsVGFyZ2V0LCB0YXJnZXRWZWN0b3IsIGV2ZW50SGFuZGxlcikge1xuICBmdW5jdGlvbiBzY3JvbGxUbyh0YXJnZXQpIHtcbiAgICBjb25zdCBkaXN0YW5jZURpZmYgPSB0YXJnZXQuZGlzdGFuY2U7XG4gICAgY29uc3QgaW5kZXhEaWZmID0gdGFyZ2V0LmluZGV4ICE9PSBpbmRleEN1cnJlbnQuZ2V0KCk7XG4gICAgdGFyZ2V0VmVjdG9yLmFkZChkaXN0YW5jZURpZmYpO1xuICAgIGlmIChkaXN0YW5jZURpZmYpIHtcbiAgICAgIGlmIChzY3JvbGxCb2R5LmR1cmF0aW9uKCkpIHtcbiAgICAgICAgYW5pbWF0aW9uLnN0YXJ0KCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBhbmltYXRpb24udXBkYXRlKCk7XG4gICAgICAgIGFuaW1hdGlvbi5yZW5kZXIoMSk7XG4gICAgICAgIGFuaW1hdGlvbi51cGRhdGUoKTtcbiAgICAgIH1cbiAgICB9XG4gICAgaWYgKGluZGV4RGlmZikge1xuICAgICAgaW5kZXhQcmV2aW91cy5zZXQoaW5kZXhDdXJyZW50LmdldCgpKTtcbiAgICAgIGluZGV4Q3VycmVudC5zZXQodGFyZ2V0LmluZGV4KTtcbiAgICAgIGV2ZW50SGFuZGxlci5lbWl0KCdzZWxlY3QnKTtcbiAgICB9XG4gIH1cbiAgZnVuY3Rpb24gZGlzdGFuY2Uobiwgc25hcCkge1xuICAgIGNvbnN0IHRhcmdldCA9IHNjcm9sbFRhcmdldC5ieURpc3RhbmNlKG4sIHNuYXApO1xuICAgIHNjcm9sbFRvKHRhcmdldCk7XG4gIH1cbiAgZnVuY3Rpb24gaW5kZXgobiwgZGlyZWN0aW9uKSB7XG4gICAgY29uc3QgdGFyZ2V0SW5kZXggPSBpbmRleEN1cnJlbnQuY2xvbmUoKS5zZXQobik7XG4gICAgY29uc3QgdGFyZ2V0ID0gc2Nyb2xsVGFyZ2V0LmJ5SW5kZXgodGFyZ2V0SW5kZXguZ2V0KCksIGRpcmVjdGlvbik7XG4gICAgc2Nyb2xsVG8odGFyZ2V0KTtcbiAgfVxuICBjb25zdCBzZWxmID0ge1xuICAgIGRpc3RhbmNlLFxuICAgIGluZGV4XG4gIH07XG4gIHJldHVybiBzZWxmO1xufVxuXG5mdW5jdGlvbiBTbGlkZUZvY3VzKHJvb3QsIHNsaWRlcywgc2xpZGVSZWdpc3RyeSwgc2Nyb2xsVG8sIHNjcm9sbEJvZHksIGV2ZW50U3RvcmUsIGV2ZW50SGFuZGxlciwgd2F0Y2hGb2N1cykge1xuICBjb25zdCBmb2N1c0xpc3RlbmVyT3B0aW9ucyA9IHtcbiAgICBwYXNzaXZlOiB0cnVlLFxuICAgIGNhcHR1cmU6IHRydWVcbiAgfTtcbiAgbGV0IGxhc3RUYWJQcmVzc1RpbWUgPSAwO1xuICBmdW5jdGlvbiBpbml0KGVtYmxhQXBpKSB7XG4gICAgaWYgKCF3YXRjaEZvY3VzKSByZXR1cm47XG4gICAgZnVuY3Rpb24gZGVmYXVsdENhbGxiYWNrKGluZGV4KSB7XG4gICAgICBjb25zdCBub3dUaW1lID0gbmV3IERhdGUoKS5nZXRUaW1lKCk7XG4gICAgICBjb25zdCBkaWZmVGltZSA9IG5vd1RpbWUgLSBsYXN0VGFiUHJlc3NUaW1lO1xuICAgICAgaWYgKGRpZmZUaW1lID4gMTApIHJldHVybjtcbiAgICAgIGV2ZW50SGFuZGxlci5lbWl0KCdzbGlkZUZvY3VzU3RhcnQnKTtcbiAgICAgIHJvb3Quc2Nyb2xsTGVmdCA9IDA7XG4gICAgICBjb25zdCBncm91cCA9IHNsaWRlUmVnaXN0cnkuZmluZEluZGV4KGdyb3VwID0+IGdyb3VwLmluY2x1ZGVzKGluZGV4KSk7XG4gICAgICBpZiAoIWlzTnVtYmVyKGdyb3VwKSkgcmV0dXJuO1xuICAgICAgc2Nyb2xsQm9keS51c2VEdXJhdGlvbigwKTtcbiAgICAgIHNjcm9sbFRvLmluZGV4KGdyb3VwLCAwKTtcbiAgICAgIGV2ZW50SGFuZGxlci5lbWl0KCdzbGlkZUZvY3VzJyk7XG4gICAgfVxuICAgIGV2ZW50U3RvcmUuYWRkKGRvY3VtZW50LCAna2V5ZG93bicsIHJlZ2lzdGVyVGFiUHJlc3MsIGZhbHNlKTtcbiAgICBzbGlkZXMuZm9yRWFjaCgoc2xpZGUsIHNsaWRlSW5kZXgpID0+IHtcbiAgICAgIGV2ZW50U3RvcmUuYWRkKHNsaWRlLCAnZm9jdXMnLCBldnQgPT4ge1xuICAgICAgICBpZiAoaXNCb29sZWFuKHdhdGNoRm9jdXMpIHx8IHdhdGNoRm9jdXMoZW1ibGFBcGksIGV2dCkpIHtcbiAgICAgICAgICBkZWZhdWx0Q2FsbGJhY2soc2xpZGVJbmRleCk7XG4gICAgICAgIH1cbiAgICAgIH0sIGZvY3VzTGlzdGVuZXJPcHRpb25zKTtcbiAgICB9KTtcbiAgfVxuICBmdW5jdGlvbiByZWdpc3RlclRhYlByZXNzKGV2ZW50KSB7XG4gICAgaWYgKGV2ZW50LmNvZGUgPT09ICdUYWInKSBsYXN0VGFiUHJlc3NUaW1lID0gbmV3IERhdGUoKS5nZXRUaW1lKCk7XG4gIH1cbiAgY29uc3Qgc2VsZiA9IHtcbiAgICBpbml0XG4gIH07XG4gIHJldHVybiBzZWxmO1xufVxuXG5mdW5jdGlvbiBWZWN0b3IxRChpbml0aWFsVmFsdWUpIHtcbiAgbGV0IHZhbHVlID0gaW5pdGlhbFZhbHVlO1xuICBmdW5jdGlvbiBnZXQoKSB7XG4gICAgcmV0dXJuIHZhbHVlO1xuICB9XG4gIGZ1bmN0aW9uIHNldChuKSB7XG4gICAgdmFsdWUgPSBub3JtYWxpemVJbnB1dChuKTtcbiAgfVxuICBmdW5jdGlvbiBhZGQobikge1xuICAgIHZhbHVlICs9IG5vcm1hbGl6ZUlucHV0KG4pO1xuICB9XG4gIGZ1bmN0aW9uIHN1YnRyYWN0KG4pIHtcbiAgICB2YWx1ZSAtPSBub3JtYWxpemVJbnB1dChuKTtcbiAgfVxuICBmdW5jdGlvbiBub3JtYWxpemVJbnB1dChuKSB7XG4gICAgcmV0dXJuIGlzTnVtYmVyKG4pID8gbiA6IG4uZ2V0KCk7XG4gIH1cbiAgY29uc3Qgc2VsZiA9IHtcbiAgICBnZXQsXG4gICAgc2V0LFxuICAgIGFkZCxcbiAgICBzdWJ0cmFjdFxuICB9O1xuICByZXR1cm4gc2VsZjtcbn1cblxuZnVuY3Rpb24gVHJhbnNsYXRlKGF4aXMsIGNvbnRhaW5lcikge1xuICBjb25zdCB0cmFuc2xhdGUgPSBheGlzLnNjcm9sbCA9PT0gJ3gnID8geCA6IHk7XG4gIGNvbnN0IGNvbnRhaW5lclN0eWxlID0gY29udGFpbmVyLnN0eWxlO1xuICBsZXQgcHJldmlvdXNUYXJnZXQgPSBudWxsO1xuICBsZXQgZGlzYWJsZWQgPSBmYWxzZTtcbiAgZnVuY3Rpb24geChuKSB7XG4gICAgcmV0dXJuIGB0cmFuc2xhdGUzZCgke259cHgsMHB4LDBweClgO1xuICB9XG4gIGZ1bmN0aW9uIHkobikge1xuICAgIHJldHVybiBgdHJhbnNsYXRlM2QoMHB4LCR7bn1weCwwcHgpYDtcbiAgfVxuICBmdW5jdGlvbiB0byh0YXJnZXQpIHtcbiAgICBpZiAoZGlzYWJsZWQpIHJldHVybjtcbiAgICBjb25zdCBuZXdUYXJnZXQgPSByb3VuZFRvVHdvRGVjaW1hbHMoYXhpcy5kaXJlY3Rpb24odGFyZ2V0KSk7XG4gICAgaWYgKG5ld1RhcmdldCA9PT0gcHJldmlvdXNUYXJnZXQpIHJldHVybjtcbiAgICBjb250YWluZXJTdHlsZS50cmFuc2Zvcm0gPSB0cmFuc2xhdGUobmV3VGFyZ2V0KTtcbiAgICBwcmV2aW91c1RhcmdldCA9IG5ld1RhcmdldDtcbiAgfVxuICBmdW5jdGlvbiB0b2dnbGVBY3RpdmUoYWN0aXZlKSB7XG4gICAgZGlzYWJsZWQgPSAhYWN0aXZlO1xuICB9XG4gIGZ1bmN0aW9uIGNsZWFyKCkge1xuICAgIGlmIChkaXNhYmxlZCkgcmV0dXJuO1xuICAgIGNvbnRhaW5lclN0eWxlLnRyYW5zZm9ybSA9ICcnO1xuICAgIGlmICghY29udGFpbmVyLmdldEF0dHJpYnV0ZSgnc3R5bGUnKSkgY29udGFpbmVyLnJlbW92ZUF0dHJpYnV0ZSgnc3R5bGUnKTtcbiAgfVxuICBjb25zdCBzZWxmID0ge1xuICAgIGNsZWFyLFxuICAgIHRvLFxuICAgIHRvZ2dsZUFjdGl2ZVxuICB9O1xuICByZXR1cm4gc2VsZjtcbn1cblxuZnVuY3Rpb24gU2xpZGVMb29wZXIoYXhpcywgdmlld1NpemUsIGNvbnRlbnRTaXplLCBzbGlkZVNpemVzLCBzbGlkZVNpemVzV2l0aEdhcHMsIHNuYXBzLCBzY3JvbGxTbmFwcywgbG9jYXRpb24sIHNsaWRlcykge1xuICBjb25zdCByb3VuZGluZ1NhZmV0eSA9IDAuNTtcbiAgY29uc3QgYXNjSXRlbXMgPSBhcnJheUtleXMoc2xpZGVTaXplc1dpdGhHYXBzKTtcbiAgY29uc3QgZGVzY0l0ZW1zID0gYXJyYXlLZXlzKHNsaWRlU2l6ZXNXaXRoR2FwcykucmV2ZXJzZSgpO1xuICBjb25zdCBsb29wUG9pbnRzID0gc3RhcnRQb2ludHMoKS5jb25jYXQoZW5kUG9pbnRzKCkpO1xuICBmdW5jdGlvbiByZW1vdmVTbGlkZVNpemVzKGluZGV4ZXMsIGZyb20pIHtcbiAgICByZXR1cm4gaW5kZXhlcy5yZWR1Y2UoKGEsIGkpID0+IHtcbiAgICAgIHJldHVybiBhIC0gc2xpZGVTaXplc1dpdGhHYXBzW2ldO1xuICAgIH0sIGZyb20pO1xuICB9XG4gIGZ1bmN0aW9uIHNsaWRlc0luR2FwKGluZGV4ZXMsIGdhcCkge1xuICAgIHJldHVybiBpbmRleGVzLnJlZHVjZSgoYSwgaSkgPT4ge1xuICAgICAgY29uc3QgcmVtYWluaW5nR2FwID0gcmVtb3ZlU2xpZGVTaXplcyhhLCBnYXApO1xuICAgICAgcmV0dXJuIHJlbWFpbmluZ0dhcCA+IDAgPyBhLmNvbmNhdChbaV0pIDogYTtcbiAgICB9LCBbXSk7XG4gIH1cbiAgZnVuY3Rpb24gZmluZFNsaWRlQm91bmRzKG9mZnNldCkge1xuICAgIHJldHVybiBzbmFwcy5tYXAoKHNuYXAsIGluZGV4KSA9PiAoe1xuICAgICAgc3RhcnQ6IHNuYXAgLSBzbGlkZVNpemVzW2luZGV4XSArIHJvdW5kaW5nU2FmZXR5ICsgb2Zmc2V0LFxuICAgICAgZW5kOiBzbmFwICsgdmlld1NpemUgLSByb3VuZGluZ1NhZmV0eSArIG9mZnNldFxuICAgIH0pKTtcbiAgfVxuICBmdW5jdGlvbiBmaW5kTG9vcFBvaW50cyhpbmRleGVzLCBvZmZzZXQsIGlzRW5kRWRnZSkge1xuICAgIGNvbnN0IHNsaWRlQm91bmRzID0gZmluZFNsaWRlQm91bmRzKG9mZnNldCk7XG4gICAgcmV0dXJuIGluZGV4ZXMubWFwKGluZGV4ID0+IHtcbiAgICAgIGNvbnN0IGluaXRpYWwgPSBpc0VuZEVkZ2UgPyAwIDogLWNvbnRlbnRTaXplO1xuICAgICAgY29uc3QgYWx0ZXJlZCA9IGlzRW5kRWRnZSA/IGNvbnRlbnRTaXplIDogMDtcbiAgICAgIGNvbnN0IGJvdW5kRWRnZSA9IGlzRW5kRWRnZSA/ICdlbmQnIDogJ3N0YXJ0JztcbiAgICAgIGNvbnN0IGxvb3BQb2ludCA9IHNsaWRlQm91bmRzW2luZGV4XVtib3VuZEVkZ2VdO1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgaW5kZXgsXG4gICAgICAgIGxvb3BQb2ludCxcbiAgICAgICAgc2xpZGVMb2NhdGlvbjogVmVjdG9yMUQoLTEpLFxuICAgICAgICB0cmFuc2xhdGU6IFRyYW5zbGF0ZShheGlzLCBzbGlkZXNbaW5kZXhdKSxcbiAgICAgICAgdGFyZ2V0OiAoKSA9PiBsb2NhdGlvbi5nZXQoKSA+IGxvb3BQb2ludCA/IGluaXRpYWwgOiBhbHRlcmVkXG4gICAgICB9O1xuICAgIH0pO1xuICB9XG4gIGZ1bmN0aW9uIHN0YXJ0UG9pbnRzKCkge1xuICAgIGNvbnN0IGdhcCA9IHNjcm9sbFNuYXBzWzBdO1xuICAgIGNvbnN0IGluZGV4ZXMgPSBzbGlkZXNJbkdhcChkZXNjSXRlbXMsIGdhcCk7XG4gICAgcmV0dXJuIGZpbmRMb29wUG9pbnRzKGluZGV4ZXMsIGNvbnRlbnRTaXplLCBmYWxzZSk7XG4gIH1cbiAgZnVuY3Rpb24gZW5kUG9pbnRzKCkge1xuICAgIGNvbnN0IGdhcCA9IHZpZXdTaXplIC0gc2Nyb2xsU25hcHNbMF0gLSAxO1xuICAgIGNvbnN0IGluZGV4ZXMgPSBzbGlkZXNJbkdhcChhc2NJdGVtcywgZ2FwKTtcbiAgICByZXR1cm4gZmluZExvb3BQb2ludHMoaW5kZXhlcywgLWNvbnRlbnRTaXplLCB0cnVlKTtcbiAgfVxuICBmdW5jdGlvbiBjYW5Mb29wKCkge1xuICAgIHJldHVybiBsb29wUG9pbnRzLmV2ZXJ5KCh7XG4gICAgICBpbmRleFxuICAgIH0pID0+IHtcbiAgICAgIGNvbnN0IG90aGVySW5kZXhlcyA9IGFzY0l0ZW1zLmZpbHRlcihpID0+IGkgIT09IGluZGV4KTtcbiAgICAgIHJldHVybiByZW1vdmVTbGlkZVNpemVzKG90aGVySW5kZXhlcywgdmlld1NpemUpIDw9IDAuMTtcbiAgICB9KTtcbiAgfVxuICBmdW5jdGlvbiBsb29wKCkge1xuICAgIGxvb3BQb2ludHMuZm9yRWFjaChsb29wUG9pbnQgPT4ge1xuICAgICAgY29uc3Qge1xuICAgICAgICB0YXJnZXQsXG4gICAgICAgIHRyYW5zbGF0ZSxcbiAgICAgICAgc2xpZGVMb2NhdGlvblxuICAgICAgfSA9IGxvb3BQb2ludDtcbiAgICAgIGNvbnN0IHNoaWZ0TG9jYXRpb24gPSB0YXJnZXQoKTtcbiAgICAgIGlmIChzaGlmdExvY2F0aW9uID09PSBzbGlkZUxvY2F0aW9uLmdldCgpKSByZXR1cm47XG4gICAgICB0cmFuc2xhdGUudG8oc2hpZnRMb2NhdGlvbik7XG4gICAgICBzbGlkZUxvY2F0aW9uLnNldChzaGlmdExvY2F0aW9uKTtcbiAgICB9KTtcbiAgfVxuICBmdW5jdGlvbiBjbGVhcigpIHtcbiAgICBsb29wUG9pbnRzLmZvckVhY2gobG9vcFBvaW50ID0+IGxvb3BQb2ludC50cmFuc2xhdGUuY2xlYXIoKSk7XG4gIH1cbiAgY29uc3Qgc2VsZiA9IHtcbiAgICBjYW5Mb29wLFxuICAgIGNsZWFyLFxuICAgIGxvb3AsXG4gICAgbG9vcFBvaW50c1xuICB9O1xuICByZXR1cm4gc2VsZjtcbn1cblxuZnVuY3Rpb24gU2xpZGVzSGFuZGxlcihjb250YWluZXIsIGV2ZW50SGFuZGxlciwgd2F0Y2hTbGlkZXMpIHtcbiAgbGV0IG11dGF0aW9uT2JzZXJ2ZXI7XG4gIGxldCBkZXN0cm95ZWQgPSBmYWxzZTtcbiAgZnVuY3Rpb24gaW5pdChlbWJsYUFwaSkge1xuICAgIGlmICghd2F0Y2hTbGlkZXMpIHJldHVybjtcbiAgICBmdW5jdGlvbiBkZWZhdWx0Q2FsbGJhY2sobXV0YXRpb25zKSB7XG4gICAgICBmb3IgKGNvbnN0IG11dGF0aW9uIG9mIG11dGF0aW9ucykge1xuICAgICAgICBpZiAobXV0YXRpb24udHlwZSA9PT0gJ2NoaWxkTGlzdCcpIHtcbiAgICAgICAgICBlbWJsYUFwaS5yZUluaXQoKTtcbiAgICAgICAgICBldmVudEhhbmRsZXIuZW1pdCgnc2xpZGVzQ2hhbmdlZCcpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICAgIG11dGF0aW9uT2JzZXJ2ZXIgPSBuZXcgTXV0YXRpb25PYnNlcnZlcihtdXRhdGlvbnMgPT4ge1xuICAgICAgaWYgKGRlc3Ryb3llZCkgcmV0dXJuO1xuICAgICAgaWYgKGlzQm9vbGVhbih3YXRjaFNsaWRlcykgfHwgd2F0Y2hTbGlkZXMoZW1ibGFBcGksIG11dGF0aW9ucykpIHtcbiAgICAgICAgZGVmYXVsdENhbGxiYWNrKG11dGF0aW9ucyk7XG4gICAgICB9XG4gICAgfSk7XG4gICAgbXV0YXRpb25PYnNlcnZlci5vYnNlcnZlKGNvbnRhaW5lciwge1xuICAgICAgY2hpbGRMaXN0OiB0cnVlXG4gICAgfSk7XG4gIH1cbiAgZnVuY3Rpb24gZGVzdHJveSgpIHtcbiAgICBpZiAobXV0YXRpb25PYnNlcnZlcikgbXV0YXRpb25PYnNlcnZlci5kaXNjb25uZWN0KCk7XG4gICAgZGVzdHJveWVkID0gdHJ1ZTtcbiAgfVxuICBjb25zdCBzZWxmID0ge1xuICAgIGluaXQsXG4gICAgZGVzdHJveVxuICB9O1xuICByZXR1cm4gc2VsZjtcbn1cblxuZnVuY3Rpb24gU2xpZGVzSW5WaWV3KGNvbnRhaW5lciwgc2xpZGVzLCBldmVudEhhbmRsZXIsIHRocmVzaG9sZCkge1xuICBjb25zdCBpbnRlcnNlY3Rpb25FbnRyeU1hcCA9IHt9O1xuICBsZXQgaW5WaWV3Q2FjaGUgPSBudWxsO1xuICBsZXQgbm90SW5WaWV3Q2FjaGUgPSBudWxsO1xuICBsZXQgaW50ZXJzZWN0aW9uT2JzZXJ2ZXI7XG4gIGxldCBkZXN0cm95ZWQgPSBmYWxzZTtcbiAgZnVuY3Rpb24gaW5pdCgpIHtcbiAgICBpbnRlcnNlY3Rpb25PYnNlcnZlciA9IG5ldyBJbnRlcnNlY3Rpb25PYnNlcnZlcihlbnRyaWVzID0+IHtcbiAgICAgIGlmIChkZXN0cm95ZWQpIHJldHVybjtcbiAgICAgIGVudHJpZXMuZm9yRWFjaChlbnRyeSA9PiB7XG4gICAgICAgIGNvbnN0IGluZGV4ID0gc2xpZGVzLmluZGV4T2YoZW50cnkudGFyZ2V0KTtcbiAgICAgICAgaW50ZXJzZWN0aW9uRW50cnlNYXBbaW5kZXhdID0gZW50cnk7XG4gICAgICB9KTtcbiAgICAgIGluVmlld0NhY2hlID0gbnVsbDtcbiAgICAgIG5vdEluVmlld0NhY2hlID0gbnVsbDtcbiAgICAgIGV2ZW50SGFuZGxlci5lbWl0KCdzbGlkZXNJblZpZXcnKTtcbiAgICB9LCB7XG4gICAgICByb290OiBjb250YWluZXIucGFyZW50RWxlbWVudCxcbiAgICAgIHRocmVzaG9sZFxuICAgIH0pO1xuICAgIHNsaWRlcy5mb3JFYWNoKHNsaWRlID0+IGludGVyc2VjdGlvbk9ic2VydmVyLm9ic2VydmUoc2xpZGUpKTtcbiAgfVxuICBmdW5jdGlvbiBkZXN0cm95KCkge1xuICAgIGlmIChpbnRlcnNlY3Rpb25PYnNlcnZlcikgaW50ZXJzZWN0aW9uT2JzZXJ2ZXIuZGlzY29ubmVjdCgpO1xuICAgIGRlc3Ryb3llZCA9IHRydWU7XG4gIH1cbiAgZnVuY3Rpb24gY3JlYXRlSW5WaWV3TGlzdChpblZpZXcpIHtcbiAgICByZXR1cm4gb2JqZWN0S2V5cyhpbnRlcnNlY3Rpb25FbnRyeU1hcCkucmVkdWNlKChsaXN0LCBzbGlkZUluZGV4KSA9PiB7XG4gICAgICBjb25zdCBpbmRleCA9IHBhcnNlSW50KHNsaWRlSW5kZXgpO1xuICAgICAgY29uc3Qge1xuICAgICAgICBpc0ludGVyc2VjdGluZ1xuICAgICAgfSA9IGludGVyc2VjdGlvbkVudHJ5TWFwW2luZGV4XTtcbiAgICAgIGNvbnN0IGluVmlld01hdGNoID0gaW5WaWV3ICYmIGlzSW50ZXJzZWN0aW5nO1xuICAgICAgY29uc3Qgbm90SW5WaWV3TWF0Y2ggPSAhaW5WaWV3ICYmICFpc0ludGVyc2VjdGluZztcbiAgICAgIGlmIChpblZpZXdNYXRjaCB8fCBub3RJblZpZXdNYXRjaCkgbGlzdC5wdXNoKGluZGV4KTtcbiAgICAgIHJldHVybiBsaXN0O1xuICAgIH0sIFtdKTtcbiAgfVxuICBmdW5jdGlvbiBnZXQoaW5WaWV3ID0gdHJ1ZSkge1xuICAgIGlmIChpblZpZXcgJiYgaW5WaWV3Q2FjaGUpIHJldHVybiBpblZpZXdDYWNoZTtcbiAgICBpZiAoIWluVmlldyAmJiBub3RJblZpZXdDYWNoZSkgcmV0dXJuIG5vdEluVmlld0NhY2hlO1xuICAgIGNvbnN0IHNsaWRlSW5kZXhlcyA9IGNyZWF0ZUluVmlld0xpc3QoaW5WaWV3KTtcbiAgICBpZiAoaW5WaWV3KSBpblZpZXdDYWNoZSA9IHNsaWRlSW5kZXhlcztcbiAgICBpZiAoIWluVmlldykgbm90SW5WaWV3Q2FjaGUgPSBzbGlkZUluZGV4ZXM7XG4gICAgcmV0dXJuIHNsaWRlSW5kZXhlcztcbiAgfVxuICBjb25zdCBzZWxmID0ge1xuICAgIGluaXQsXG4gICAgZGVzdHJveSxcbiAgICBnZXRcbiAgfTtcbiAgcmV0dXJuIHNlbGY7XG59XG5cbmZ1bmN0aW9uIFNsaWRlU2l6ZXMoYXhpcywgY29udGFpbmVyUmVjdCwgc2xpZGVSZWN0cywgc2xpZGVzLCByZWFkRWRnZUdhcCwgb3duZXJXaW5kb3cpIHtcbiAgY29uc3Qge1xuICAgIG1lYXN1cmVTaXplLFxuICAgIHN0YXJ0RWRnZSxcbiAgICBlbmRFZGdlXG4gIH0gPSBheGlzO1xuICBjb25zdCB3aXRoRWRnZUdhcCA9IHNsaWRlUmVjdHNbMF0gJiYgcmVhZEVkZ2VHYXA7XG4gIGNvbnN0IHN0YXJ0R2FwID0gbWVhc3VyZVN0YXJ0R2FwKCk7XG4gIGNvbnN0IGVuZEdhcCA9IG1lYXN1cmVFbmRHYXAoKTtcbiAgY29uc3Qgc2xpZGVTaXplcyA9IHNsaWRlUmVjdHMubWFwKG1lYXN1cmVTaXplKTtcbiAgY29uc3Qgc2xpZGVTaXplc1dpdGhHYXBzID0gbWVhc3VyZVdpdGhHYXBzKCk7XG4gIGZ1bmN0aW9uIG1lYXN1cmVTdGFydEdhcCgpIHtcbiAgICBpZiAoIXdpdGhFZGdlR2FwKSByZXR1cm4gMDtcbiAgICBjb25zdCBzbGlkZVJlY3QgPSBzbGlkZVJlY3RzWzBdO1xuICAgIHJldHVybiBtYXRoQWJzKGNvbnRhaW5lclJlY3Rbc3RhcnRFZGdlXSAtIHNsaWRlUmVjdFtzdGFydEVkZ2VdKTtcbiAgfVxuICBmdW5jdGlvbiBtZWFzdXJlRW5kR2FwKCkge1xuICAgIGlmICghd2l0aEVkZ2VHYXApIHJldHVybiAwO1xuICAgIGNvbnN0IHN0eWxlID0gb3duZXJXaW5kb3cuZ2V0Q29tcHV0ZWRTdHlsZShhcnJheUxhc3Qoc2xpZGVzKSk7XG4gICAgcmV0dXJuIHBhcnNlRmxvYXQoc3R5bGUuZ2V0UHJvcGVydHlWYWx1ZShgbWFyZ2luLSR7ZW5kRWRnZX1gKSk7XG4gIH1cbiAgZnVuY3Rpb24gbWVhc3VyZVdpdGhHYXBzKCkge1xuICAgIHJldHVybiBzbGlkZVJlY3RzLm1hcCgocmVjdCwgaW5kZXgsIHJlY3RzKSA9PiB7XG4gICAgICBjb25zdCBpc0ZpcnN0ID0gIWluZGV4O1xuICAgICAgY29uc3QgaXNMYXN0ID0gYXJyYXlJc0xhc3RJbmRleChyZWN0cywgaW5kZXgpO1xuICAgICAgaWYgKGlzRmlyc3QpIHJldHVybiBzbGlkZVNpemVzW2luZGV4XSArIHN0YXJ0R2FwO1xuICAgICAgaWYgKGlzTGFzdCkgcmV0dXJuIHNsaWRlU2l6ZXNbaW5kZXhdICsgZW5kR2FwO1xuICAgICAgcmV0dXJuIHJlY3RzW2luZGV4ICsgMV1bc3RhcnRFZGdlXSAtIHJlY3Rbc3RhcnRFZGdlXTtcbiAgICB9KS5tYXAobWF0aEFicyk7XG4gIH1cbiAgY29uc3Qgc2VsZiA9IHtcbiAgICBzbGlkZVNpemVzLFxuICAgIHNsaWRlU2l6ZXNXaXRoR2FwcyxcbiAgICBzdGFydEdhcCxcbiAgICBlbmRHYXBcbiAgfTtcbiAgcmV0dXJuIHNlbGY7XG59XG5cbmZ1bmN0aW9uIFNsaWRlc1RvU2Nyb2xsKGF4aXMsIHZpZXdTaXplLCBzbGlkZXNUb1Njcm9sbCwgbG9vcCwgY29udGFpbmVyUmVjdCwgc2xpZGVSZWN0cywgc3RhcnRHYXAsIGVuZEdhcCwgcGl4ZWxUb2xlcmFuY2UpIHtcbiAgY29uc3Qge1xuICAgIHN0YXJ0RWRnZSxcbiAgICBlbmRFZGdlLFxuICAgIGRpcmVjdGlvblxuICB9ID0gYXhpcztcbiAgY29uc3QgZ3JvdXBCeU51bWJlciA9IGlzTnVtYmVyKHNsaWRlc1RvU2Nyb2xsKTtcbiAgZnVuY3Rpb24gYnlOdW1iZXIoYXJyYXksIGdyb3VwU2l6ZSkge1xuICAgIHJldHVybiBhcnJheUtleXMoYXJyYXkpLmZpbHRlcihpID0+IGkgJSBncm91cFNpemUgPT09IDApLm1hcChpID0+IGFycmF5LnNsaWNlKGksIGkgKyBncm91cFNpemUpKTtcbiAgfVxuICBmdW5jdGlvbiBieVNpemUoYXJyYXkpIHtcbiAgICBpZiAoIWFycmF5Lmxlbmd0aCkgcmV0dXJuIFtdO1xuICAgIHJldHVybiBhcnJheUtleXMoYXJyYXkpLnJlZHVjZSgoZ3JvdXBzLCByZWN0QiwgaW5kZXgpID0+IHtcbiAgICAgIGNvbnN0IHJlY3RBID0gYXJyYXlMYXN0KGdyb3VwcykgfHwgMDtcbiAgICAgIGNvbnN0IGlzRmlyc3QgPSByZWN0QSA9PT0gMDtcbiAgICAgIGNvbnN0IGlzTGFzdCA9IHJlY3RCID09PSBhcnJheUxhc3RJbmRleChhcnJheSk7XG4gICAgICBjb25zdCBlZGdlQSA9IGNvbnRhaW5lclJlY3Rbc3RhcnRFZGdlXSAtIHNsaWRlUmVjdHNbcmVjdEFdW3N0YXJ0RWRnZV07XG4gICAgICBjb25zdCBlZGdlQiA9IGNvbnRhaW5lclJlY3Rbc3RhcnRFZGdlXSAtIHNsaWRlUmVjdHNbcmVjdEJdW2VuZEVkZ2VdO1xuICAgICAgY29uc3QgZ2FwQSA9ICFsb29wICYmIGlzRmlyc3QgPyBkaXJlY3Rpb24oc3RhcnRHYXApIDogMDtcbiAgICAgIGNvbnN0IGdhcEIgPSAhbG9vcCAmJiBpc0xhc3QgPyBkaXJlY3Rpb24oZW5kR2FwKSA6IDA7XG4gICAgICBjb25zdCBjaHVua1NpemUgPSBtYXRoQWJzKGVkZ2VCIC0gZ2FwQiAtIChlZGdlQSArIGdhcEEpKTtcbiAgICAgIGlmIChpbmRleCAmJiBjaHVua1NpemUgPiB2aWV3U2l6ZSArIHBpeGVsVG9sZXJhbmNlKSBncm91cHMucHVzaChyZWN0Qik7XG4gICAgICBpZiAoaXNMYXN0KSBncm91cHMucHVzaChhcnJheS5sZW5ndGgpO1xuICAgICAgcmV0dXJuIGdyb3VwcztcbiAgICB9LCBbXSkubWFwKChjdXJyZW50U2l6ZSwgaW5kZXgsIGdyb3VwcykgPT4ge1xuICAgICAgY29uc3QgcHJldmlvdXNTaXplID0gTWF0aC5tYXgoZ3JvdXBzW2luZGV4IC0gMV0gfHwgMCk7XG4gICAgICByZXR1cm4gYXJyYXkuc2xpY2UocHJldmlvdXNTaXplLCBjdXJyZW50U2l6ZSk7XG4gICAgfSk7XG4gIH1cbiAgZnVuY3Rpb24gZ3JvdXBTbGlkZXMoYXJyYXkpIHtcbiAgICByZXR1cm4gZ3JvdXBCeU51bWJlciA/IGJ5TnVtYmVyKGFycmF5LCBzbGlkZXNUb1Njcm9sbCkgOiBieVNpemUoYXJyYXkpO1xuICB9XG4gIGNvbnN0IHNlbGYgPSB7XG4gICAgZ3JvdXBTbGlkZXNcbiAgfTtcbiAgcmV0dXJuIHNlbGY7XG59XG5cbmZ1bmN0aW9uIEVuZ2luZShyb290LCBjb250YWluZXIsIHNsaWRlcywgb3duZXJEb2N1bWVudCwgb3duZXJXaW5kb3csIG9wdGlvbnMsIGV2ZW50SGFuZGxlcikge1xuICAvLyBPcHRpb25zXG4gIGNvbnN0IHtcbiAgICBhbGlnbixcbiAgICBheGlzOiBzY3JvbGxBeGlzLFxuICAgIGRpcmVjdGlvbixcbiAgICBzdGFydEluZGV4LFxuICAgIGxvb3AsXG4gICAgZHVyYXRpb24sXG4gICAgZHJhZ0ZyZWUsXG4gICAgZHJhZ1RocmVzaG9sZCxcbiAgICBpblZpZXdUaHJlc2hvbGQsXG4gICAgc2xpZGVzVG9TY3JvbGw6IGdyb3VwU2xpZGVzLFxuICAgIHNraXBTbmFwcyxcbiAgICBjb250YWluU2Nyb2xsLFxuICAgIHdhdGNoUmVzaXplLFxuICAgIHdhdGNoU2xpZGVzLFxuICAgIHdhdGNoRHJhZyxcbiAgICB3YXRjaEZvY3VzXG4gIH0gPSBvcHRpb25zO1xuICAvLyBNZWFzdXJlbWVudHNcbiAgY29uc3QgcGl4ZWxUb2xlcmFuY2UgPSAyO1xuICBjb25zdCBub2RlUmVjdHMgPSBOb2RlUmVjdHMoKTtcbiAgY29uc3QgY29udGFpbmVyUmVjdCA9IG5vZGVSZWN0cy5tZWFzdXJlKGNvbnRhaW5lcik7XG4gIGNvbnN0IHNsaWRlUmVjdHMgPSBzbGlkZXMubWFwKG5vZGVSZWN0cy5tZWFzdXJlKTtcbiAgY29uc3QgYXhpcyA9IEF4aXMoc2Nyb2xsQXhpcywgZGlyZWN0aW9uKTtcbiAgY29uc3Qgdmlld1NpemUgPSBheGlzLm1lYXN1cmVTaXplKGNvbnRhaW5lclJlY3QpO1xuICBjb25zdCBwZXJjZW50T2ZWaWV3ID0gUGVyY2VudE9mVmlldyh2aWV3U2l6ZSk7XG4gIGNvbnN0IGFsaWdubWVudCA9IEFsaWdubWVudChhbGlnbiwgdmlld1NpemUpO1xuICBjb25zdCBjb250YWluU25hcHMgPSAhbG9vcCAmJiAhIWNvbnRhaW5TY3JvbGw7XG4gIGNvbnN0IHJlYWRFZGdlR2FwID0gbG9vcCB8fCAhIWNvbnRhaW5TY3JvbGw7XG4gIGNvbnN0IHtcbiAgICBzbGlkZVNpemVzLFxuICAgIHNsaWRlU2l6ZXNXaXRoR2FwcyxcbiAgICBzdGFydEdhcCxcbiAgICBlbmRHYXBcbiAgfSA9IFNsaWRlU2l6ZXMoYXhpcywgY29udGFpbmVyUmVjdCwgc2xpZGVSZWN0cywgc2xpZGVzLCByZWFkRWRnZUdhcCwgb3duZXJXaW5kb3cpO1xuICBjb25zdCBzbGlkZXNUb1Njcm9sbCA9IFNsaWRlc1RvU2Nyb2xsKGF4aXMsIHZpZXdTaXplLCBncm91cFNsaWRlcywgbG9vcCwgY29udGFpbmVyUmVjdCwgc2xpZGVSZWN0cywgc3RhcnRHYXAsIGVuZEdhcCwgcGl4ZWxUb2xlcmFuY2UpO1xuICBjb25zdCB7XG4gICAgc25hcHMsXG4gICAgc25hcHNBbGlnbmVkXG4gIH0gPSBTY3JvbGxTbmFwcyhheGlzLCBhbGlnbm1lbnQsIGNvbnRhaW5lclJlY3QsIHNsaWRlUmVjdHMsIHNsaWRlc1RvU2Nyb2xsKTtcbiAgY29uc3QgY29udGVudFNpemUgPSAtYXJyYXlMYXN0KHNuYXBzKSArIGFycmF5TGFzdChzbGlkZVNpemVzV2l0aEdhcHMpO1xuICBjb25zdCB7XG4gICAgc25hcHNDb250YWluZWQsXG4gICAgc2Nyb2xsQ29udGFpbkxpbWl0XG4gIH0gPSBTY3JvbGxDb250YWluKHZpZXdTaXplLCBjb250ZW50U2l6ZSwgc25hcHNBbGlnbmVkLCBjb250YWluU2Nyb2xsLCBwaXhlbFRvbGVyYW5jZSk7XG4gIGNvbnN0IHNjcm9sbFNuYXBzID0gY29udGFpblNuYXBzID8gc25hcHNDb250YWluZWQgOiBzbmFwc0FsaWduZWQ7XG4gIGNvbnN0IHtcbiAgICBsaW1pdFxuICB9ID0gU2Nyb2xsTGltaXQoY29udGVudFNpemUsIHNjcm9sbFNuYXBzLCBsb29wKTtcbiAgLy8gSW5kZXhlc1xuICBjb25zdCBpbmRleCA9IENvdW50ZXIoYXJyYXlMYXN0SW5kZXgoc2Nyb2xsU25hcHMpLCBzdGFydEluZGV4LCBsb29wKTtcbiAgY29uc3QgaW5kZXhQcmV2aW91cyA9IGluZGV4LmNsb25lKCk7XG4gIGNvbnN0IHNsaWRlSW5kZXhlcyA9IGFycmF5S2V5cyhzbGlkZXMpO1xuICAvLyBBbmltYXRpb25cbiAgY29uc3QgdXBkYXRlID0gKHtcbiAgICBkcmFnSGFuZGxlcixcbiAgICBzY3JvbGxCb2R5LFxuICAgIHNjcm9sbEJvdW5kcyxcbiAgICBvcHRpb25zOiB7XG4gICAgICBsb29wXG4gICAgfVxuICB9KSA9PiB7XG4gICAgaWYgKCFsb29wKSBzY3JvbGxCb3VuZHMuY29uc3RyYWluKGRyYWdIYW5kbGVyLnBvaW50ZXJEb3duKCkpO1xuICAgIHNjcm9sbEJvZHkuc2VlaygpO1xuICB9O1xuICBjb25zdCByZW5kZXIgPSAoe1xuICAgIHNjcm9sbEJvZHksXG4gICAgdHJhbnNsYXRlLFxuICAgIGxvY2F0aW9uLFxuICAgIG9mZnNldExvY2F0aW9uLFxuICAgIHByZXZpb3VzTG9jYXRpb24sXG4gICAgc2Nyb2xsTG9vcGVyLFxuICAgIHNsaWRlTG9vcGVyLFxuICAgIGRyYWdIYW5kbGVyLFxuICAgIGFuaW1hdGlvbixcbiAgICBldmVudEhhbmRsZXIsXG4gICAgc2Nyb2xsQm91bmRzLFxuICAgIG9wdGlvbnM6IHtcbiAgICAgIGxvb3BcbiAgICB9XG4gIH0sIGFscGhhKSA9PiB7XG4gICAgY29uc3Qgc2hvdWxkU2V0dGxlID0gc2Nyb2xsQm9keS5zZXR0bGVkKCk7XG4gICAgY29uc3Qgd2l0aGluQm91bmRzID0gIXNjcm9sbEJvdW5kcy5zaG91bGRDb25zdHJhaW4oKTtcbiAgICBjb25zdCBoYXNTZXR0bGVkID0gbG9vcCA/IHNob3VsZFNldHRsZSA6IHNob3VsZFNldHRsZSAmJiB3aXRoaW5Cb3VuZHM7XG4gICAgY29uc3QgaGFzU2V0dGxlZEFuZElkbGUgPSBoYXNTZXR0bGVkICYmICFkcmFnSGFuZGxlci5wb2ludGVyRG93bigpO1xuICAgIGlmIChoYXNTZXR0bGVkQW5kSWRsZSkgYW5pbWF0aW9uLnN0b3AoKTtcbiAgICBjb25zdCBpbnRlcnBvbGF0ZWRMb2NhdGlvbiA9IGxvY2F0aW9uLmdldCgpICogYWxwaGEgKyBwcmV2aW91c0xvY2F0aW9uLmdldCgpICogKDEgLSBhbHBoYSk7XG4gICAgb2Zmc2V0TG9jYXRpb24uc2V0KGludGVycG9sYXRlZExvY2F0aW9uKTtcbiAgICBpZiAobG9vcCkge1xuICAgICAgc2Nyb2xsTG9vcGVyLmxvb3Aoc2Nyb2xsQm9keS5kaXJlY3Rpb24oKSk7XG4gICAgICBzbGlkZUxvb3Blci5sb29wKCk7XG4gICAgfVxuICAgIHRyYW5zbGF0ZS50byhvZmZzZXRMb2NhdGlvbi5nZXQoKSk7XG4gICAgaWYgKGhhc1NldHRsZWRBbmRJZGxlKSBldmVudEhhbmRsZXIuZW1pdCgnc2V0dGxlJyk7XG4gICAgaWYgKCFoYXNTZXR0bGVkKSBldmVudEhhbmRsZXIuZW1pdCgnc2Nyb2xsJyk7XG4gIH07XG4gIGNvbnN0IGFuaW1hdGlvbiA9IEFuaW1hdGlvbnMob3duZXJEb2N1bWVudCwgb3duZXJXaW5kb3csICgpID0+IHVwZGF0ZShlbmdpbmUpLCBhbHBoYSA9PiByZW5kZXIoZW5naW5lLCBhbHBoYSkpO1xuICAvLyBTaGFyZWRcbiAgY29uc3QgZnJpY3Rpb24gPSAwLjY4O1xuICBjb25zdCBzdGFydExvY2F0aW9uID0gc2Nyb2xsU25hcHNbaW5kZXguZ2V0KCldO1xuICBjb25zdCBsb2NhdGlvbiA9IFZlY3RvcjFEKHN0YXJ0TG9jYXRpb24pO1xuICBjb25zdCBwcmV2aW91c0xvY2F0aW9uID0gVmVjdG9yMUQoc3RhcnRMb2NhdGlvbik7XG4gIGNvbnN0IG9mZnNldExvY2F0aW9uID0gVmVjdG9yMUQoc3RhcnRMb2NhdGlvbik7XG4gIGNvbnN0IHRhcmdldCA9IFZlY3RvcjFEKHN0YXJ0TG9jYXRpb24pO1xuICBjb25zdCBzY3JvbGxCb2R5ID0gU2Nyb2xsQm9keShsb2NhdGlvbiwgb2Zmc2V0TG9jYXRpb24sIHByZXZpb3VzTG9jYXRpb24sIHRhcmdldCwgZHVyYXRpb24sIGZyaWN0aW9uKTtcbiAgY29uc3Qgc2Nyb2xsVGFyZ2V0ID0gU2Nyb2xsVGFyZ2V0KGxvb3AsIHNjcm9sbFNuYXBzLCBjb250ZW50U2l6ZSwgbGltaXQsIHRhcmdldCk7XG4gIGNvbnN0IHNjcm9sbFRvID0gU2Nyb2xsVG8oYW5pbWF0aW9uLCBpbmRleCwgaW5kZXhQcmV2aW91cywgc2Nyb2xsQm9keSwgc2Nyb2xsVGFyZ2V0LCB0YXJnZXQsIGV2ZW50SGFuZGxlcik7XG4gIGNvbnN0IHNjcm9sbFByb2dyZXNzID0gU2Nyb2xsUHJvZ3Jlc3MobGltaXQpO1xuICBjb25zdCBldmVudFN0b3JlID0gRXZlbnRTdG9yZSgpO1xuICBjb25zdCBzbGlkZXNJblZpZXcgPSBTbGlkZXNJblZpZXcoY29udGFpbmVyLCBzbGlkZXMsIGV2ZW50SGFuZGxlciwgaW5WaWV3VGhyZXNob2xkKTtcbiAgY29uc3Qge1xuICAgIHNsaWRlUmVnaXN0cnlcbiAgfSA9IFNsaWRlUmVnaXN0cnkoY29udGFpblNuYXBzLCBjb250YWluU2Nyb2xsLCBzY3JvbGxTbmFwcywgc2Nyb2xsQ29udGFpbkxpbWl0LCBzbGlkZXNUb1Njcm9sbCwgc2xpZGVJbmRleGVzKTtcbiAgY29uc3Qgc2xpZGVGb2N1cyA9IFNsaWRlRm9jdXMocm9vdCwgc2xpZGVzLCBzbGlkZVJlZ2lzdHJ5LCBzY3JvbGxUbywgc2Nyb2xsQm9keSwgZXZlbnRTdG9yZSwgZXZlbnRIYW5kbGVyLCB3YXRjaEZvY3VzKTtcbiAgLy8gRW5naW5lXG4gIGNvbnN0IGVuZ2luZSA9IHtcbiAgICBvd25lckRvY3VtZW50LFxuICAgIG93bmVyV2luZG93LFxuICAgIGV2ZW50SGFuZGxlcixcbiAgICBjb250YWluZXJSZWN0LFxuICAgIHNsaWRlUmVjdHMsXG4gICAgYW5pbWF0aW9uLFxuICAgIGF4aXMsXG4gICAgZHJhZ0hhbmRsZXI6IERyYWdIYW5kbGVyKGF4aXMsIHJvb3QsIG93bmVyRG9jdW1lbnQsIG93bmVyV2luZG93LCB0YXJnZXQsIERyYWdUcmFja2VyKGF4aXMsIG93bmVyV2luZG93KSwgbG9jYXRpb24sIGFuaW1hdGlvbiwgc2Nyb2xsVG8sIHNjcm9sbEJvZHksIHNjcm9sbFRhcmdldCwgaW5kZXgsIGV2ZW50SGFuZGxlciwgcGVyY2VudE9mVmlldywgZHJhZ0ZyZWUsIGRyYWdUaHJlc2hvbGQsIHNraXBTbmFwcywgZnJpY3Rpb24sIHdhdGNoRHJhZyksXG4gICAgZXZlbnRTdG9yZSxcbiAgICBwZXJjZW50T2ZWaWV3LFxuICAgIGluZGV4LFxuICAgIGluZGV4UHJldmlvdXMsXG4gICAgbGltaXQsXG4gICAgbG9jYXRpb24sXG4gICAgb2Zmc2V0TG9jYXRpb24sXG4gICAgcHJldmlvdXNMb2NhdGlvbixcbiAgICBvcHRpb25zLFxuICAgIHJlc2l6ZUhhbmRsZXI6IFJlc2l6ZUhhbmRsZXIoY29udGFpbmVyLCBldmVudEhhbmRsZXIsIG93bmVyV2luZG93LCBzbGlkZXMsIGF4aXMsIHdhdGNoUmVzaXplLCBub2RlUmVjdHMpLFxuICAgIHNjcm9sbEJvZHksXG4gICAgc2Nyb2xsQm91bmRzOiBTY3JvbGxCb3VuZHMobGltaXQsIG9mZnNldExvY2F0aW9uLCB0YXJnZXQsIHNjcm9sbEJvZHksIHBlcmNlbnRPZlZpZXcpLFxuICAgIHNjcm9sbExvb3BlcjogU2Nyb2xsTG9vcGVyKGNvbnRlbnRTaXplLCBsaW1pdCwgb2Zmc2V0TG9jYXRpb24sIFtsb2NhdGlvbiwgb2Zmc2V0TG9jYXRpb24sIHByZXZpb3VzTG9jYXRpb24sIHRhcmdldF0pLFxuICAgIHNjcm9sbFByb2dyZXNzLFxuICAgIHNjcm9sbFNuYXBMaXN0OiBzY3JvbGxTbmFwcy5tYXAoc2Nyb2xsUHJvZ3Jlc3MuZ2V0KSxcbiAgICBzY3JvbGxTbmFwcyxcbiAgICBzY3JvbGxUYXJnZXQsXG4gICAgc2Nyb2xsVG8sXG4gICAgc2xpZGVMb29wZXI6IFNsaWRlTG9vcGVyKGF4aXMsIHZpZXdTaXplLCBjb250ZW50U2l6ZSwgc2xpZGVTaXplcywgc2xpZGVTaXplc1dpdGhHYXBzLCBzbmFwcywgc2Nyb2xsU25hcHMsIG9mZnNldExvY2F0aW9uLCBzbGlkZXMpLFxuICAgIHNsaWRlRm9jdXMsXG4gICAgc2xpZGVzSGFuZGxlcjogU2xpZGVzSGFuZGxlcihjb250YWluZXIsIGV2ZW50SGFuZGxlciwgd2F0Y2hTbGlkZXMpLFxuICAgIHNsaWRlc0luVmlldyxcbiAgICBzbGlkZUluZGV4ZXMsXG4gICAgc2xpZGVSZWdpc3RyeSxcbiAgICBzbGlkZXNUb1Njcm9sbCxcbiAgICB0YXJnZXQsXG4gICAgdHJhbnNsYXRlOiBUcmFuc2xhdGUoYXhpcywgY29udGFpbmVyKVxuICB9O1xuICByZXR1cm4gZW5naW5lO1xufVxuXG5mdW5jdGlvbiBFdmVudEhhbmRsZXIoKSB7XG4gIGxldCBsaXN0ZW5lcnMgPSB7fTtcbiAgbGV0IGFwaTtcbiAgZnVuY3Rpb24gaW5pdChlbWJsYUFwaSkge1xuICAgIGFwaSA9IGVtYmxhQXBpO1xuICB9XG4gIGZ1bmN0aW9uIGdldExpc3RlbmVycyhldnQpIHtcbiAgICByZXR1cm4gbGlzdGVuZXJzW2V2dF0gfHwgW107XG4gIH1cbiAgZnVuY3Rpb24gZW1pdChldnQpIHtcbiAgICBnZXRMaXN0ZW5lcnMoZXZ0KS5mb3JFYWNoKGUgPT4gZShhcGksIGV2dCkpO1xuICAgIHJldHVybiBzZWxmO1xuICB9XG4gIGZ1bmN0aW9uIG9uKGV2dCwgY2IpIHtcbiAgICBsaXN0ZW5lcnNbZXZ0XSA9IGdldExpc3RlbmVycyhldnQpLmNvbmNhdChbY2JdKTtcbiAgICByZXR1cm4gc2VsZjtcbiAgfVxuICBmdW5jdGlvbiBvZmYoZXZ0LCBjYikge1xuICAgIGxpc3RlbmVyc1tldnRdID0gZ2V0TGlzdGVuZXJzKGV2dCkuZmlsdGVyKGUgPT4gZSAhPT0gY2IpO1xuICAgIHJldHVybiBzZWxmO1xuICB9XG4gIGZ1bmN0aW9uIGNsZWFyKCkge1xuICAgIGxpc3RlbmVycyA9IHt9O1xuICB9XG4gIGNvbnN0IHNlbGYgPSB7XG4gICAgaW5pdCxcbiAgICBlbWl0LFxuICAgIG9mZixcbiAgICBvbixcbiAgICBjbGVhclxuICB9O1xuICByZXR1cm4gc2VsZjtcbn1cblxuY29uc3QgZGVmYXVsdE9wdGlvbnMgPSB7XG4gIGFsaWduOiAnY2VudGVyJyxcbiAgYXhpczogJ3gnLFxuICBjb250YWluZXI6IG51bGwsXG4gIHNsaWRlczogbnVsbCxcbiAgY29udGFpblNjcm9sbDogJ3RyaW1TbmFwcycsXG4gIGRpcmVjdGlvbjogJ2x0cicsXG4gIHNsaWRlc1RvU2Nyb2xsOiAxLFxuICBpblZpZXdUaHJlc2hvbGQ6IDAsXG4gIGJyZWFrcG9pbnRzOiB7fSxcbiAgZHJhZ0ZyZWU6IGZhbHNlLFxuICBkcmFnVGhyZXNob2xkOiAxMCxcbiAgbG9vcDogZmFsc2UsXG4gIHNraXBTbmFwczogZmFsc2UsXG4gIGR1cmF0aW9uOiAyNSxcbiAgc3RhcnRJbmRleDogMCxcbiAgYWN0aXZlOiB0cnVlLFxuICB3YXRjaERyYWc6IHRydWUsXG4gIHdhdGNoUmVzaXplOiB0cnVlLFxuICB3YXRjaFNsaWRlczogdHJ1ZSxcbiAgd2F0Y2hGb2N1czogdHJ1ZVxufTtcblxuZnVuY3Rpb24gT3B0aW9uc0hhbmRsZXIob3duZXJXaW5kb3cpIHtcbiAgZnVuY3Rpb24gbWVyZ2VPcHRpb25zKG9wdGlvbnNBLCBvcHRpb25zQikge1xuICAgIHJldHVybiBvYmplY3RzTWVyZ2VEZWVwKG9wdGlvbnNBLCBvcHRpb25zQiB8fCB7fSk7XG4gIH1cbiAgZnVuY3Rpb24gb3B0aW9uc0F0TWVkaWEob3B0aW9ucykge1xuICAgIGNvbnN0IG9wdGlvbnNBdE1lZGlhID0gb3B0aW9ucy5icmVha3BvaW50cyB8fCB7fTtcbiAgICBjb25zdCBtYXRjaGVkTWVkaWFPcHRpb25zID0gb2JqZWN0S2V5cyhvcHRpb25zQXRNZWRpYSkuZmlsdGVyKG1lZGlhID0+IG93bmVyV2luZG93Lm1hdGNoTWVkaWEobWVkaWEpLm1hdGNoZXMpLm1hcChtZWRpYSA9PiBvcHRpb25zQXRNZWRpYVttZWRpYV0pLnJlZHVjZSgoYSwgbWVkaWFPcHRpb24pID0+IG1lcmdlT3B0aW9ucyhhLCBtZWRpYU9wdGlvbiksIHt9KTtcbiAgICByZXR1cm4gbWVyZ2VPcHRpb25zKG9wdGlvbnMsIG1hdGNoZWRNZWRpYU9wdGlvbnMpO1xuICB9XG4gIGZ1bmN0aW9uIG9wdGlvbnNNZWRpYVF1ZXJpZXMob3B0aW9uc0xpc3QpIHtcbiAgICByZXR1cm4gb3B0aW9uc0xpc3QubWFwKG9wdGlvbnMgPT4gb2JqZWN0S2V5cyhvcHRpb25zLmJyZWFrcG9pbnRzIHx8IHt9KSkucmVkdWNlKChhY2MsIG1lZGlhUXVlcmllcykgPT4gYWNjLmNvbmNhdChtZWRpYVF1ZXJpZXMpLCBbXSkubWFwKG93bmVyV2luZG93Lm1hdGNoTWVkaWEpO1xuICB9XG4gIGNvbnN0IHNlbGYgPSB7XG4gICAgbWVyZ2VPcHRpb25zLFxuICAgIG9wdGlvbnNBdE1lZGlhLFxuICAgIG9wdGlvbnNNZWRpYVF1ZXJpZXNcbiAgfTtcbiAgcmV0dXJuIHNlbGY7XG59XG5cbmZ1bmN0aW9uIFBsdWdpbnNIYW5kbGVyKG9wdGlvbnNIYW5kbGVyKSB7XG4gIGxldCBhY3RpdmVQbHVnaW5zID0gW107XG4gIGZ1bmN0aW9uIGluaXQoZW1ibGFBcGksIHBsdWdpbnMpIHtcbiAgICBhY3RpdmVQbHVnaW5zID0gcGx1Z2lucy5maWx0ZXIoKHtcbiAgICAgIG9wdGlvbnNcbiAgICB9KSA9PiBvcHRpb25zSGFuZGxlci5vcHRpb25zQXRNZWRpYShvcHRpb25zKS5hY3RpdmUgIT09IGZhbHNlKTtcbiAgICBhY3RpdmVQbHVnaW5zLmZvckVhY2gocGx1Z2luID0+IHBsdWdpbi5pbml0KGVtYmxhQXBpLCBvcHRpb25zSGFuZGxlcikpO1xuICAgIHJldHVybiBwbHVnaW5zLnJlZHVjZSgobWFwLCBwbHVnaW4pID0+IE9iamVjdC5hc3NpZ24obWFwLCB7XG4gICAgICBbcGx1Z2luLm5hbWVdOiBwbHVnaW5cbiAgICB9KSwge30pO1xuICB9XG4gIGZ1bmN0aW9uIGRlc3Ryb3koKSB7XG4gICAgYWN0aXZlUGx1Z2lucyA9IGFjdGl2ZVBsdWdpbnMuZmlsdGVyKHBsdWdpbiA9PiBwbHVnaW4uZGVzdHJveSgpKTtcbiAgfVxuICBjb25zdCBzZWxmID0ge1xuICAgIGluaXQsXG4gICAgZGVzdHJveVxuICB9O1xuICByZXR1cm4gc2VsZjtcbn1cblxuZnVuY3Rpb24gRW1ibGFDYXJvdXNlbChyb290LCB1c2VyT3B0aW9ucywgdXNlclBsdWdpbnMpIHtcbiAgY29uc3Qgb3duZXJEb2N1bWVudCA9IHJvb3Qub3duZXJEb2N1bWVudDtcbiAgY29uc3Qgb3duZXJXaW5kb3cgPSBvd25lckRvY3VtZW50LmRlZmF1bHRWaWV3O1xuICBjb25zdCBvcHRpb25zSGFuZGxlciA9IE9wdGlvbnNIYW5kbGVyKG93bmVyV2luZG93KTtcbiAgY29uc3QgcGx1Z2luc0hhbmRsZXIgPSBQbHVnaW5zSGFuZGxlcihvcHRpb25zSGFuZGxlcik7XG4gIGNvbnN0IG1lZGlhSGFuZGxlcnMgPSBFdmVudFN0b3JlKCk7XG4gIGNvbnN0IGV2ZW50SGFuZGxlciA9IEV2ZW50SGFuZGxlcigpO1xuICBjb25zdCB7XG4gICAgbWVyZ2VPcHRpb25zLFxuICAgIG9wdGlvbnNBdE1lZGlhLFxuICAgIG9wdGlvbnNNZWRpYVF1ZXJpZXNcbiAgfSA9IG9wdGlvbnNIYW5kbGVyO1xuICBjb25zdCB7XG4gICAgb24sXG4gICAgb2ZmLFxuICAgIGVtaXRcbiAgfSA9IGV2ZW50SGFuZGxlcjtcbiAgY29uc3QgcmVJbml0ID0gcmVBY3RpdmF0ZTtcbiAgbGV0IGRlc3Ryb3llZCA9IGZhbHNlO1xuICBsZXQgZW5naW5lO1xuICBsZXQgb3B0aW9uc0Jhc2UgPSBtZXJnZU9wdGlvbnMoZGVmYXVsdE9wdGlvbnMsIEVtYmxhQ2Fyb3VzZWwuZ2xvYmFsT3B0aW9ucyk7XG4gIGxldCBvcHRpb25zID0gbWVyZ2VPcHRpb25zKG9wdGlvbnNCYXNlKTtcbiAgbGV0IHBsdWdpbkxpc3QgPSBbXTtcbiAgbGV0IHBsdWdpbkFwaXM7XG4gIGxldCBjb250YWluZXI7XG4gIGxldCBzbGlkZXM7XG4gIGZ1bmN0aW9uIHN0b3JlRWxlbWVudHMoKSB7XG4gICAgY29uc3Qge1xuICAgICAgY29udGFpbmVyOiB1c2VyQ29udGFpbmVyLFxuICAgICAgc2xpZGVzOiB1c2VyU2xpZGVzXG4gICAgfSA9IG9wdGlvbnM7XG4gICAgY29uc3QgY3VzdG9tQ29udGFpbmVyID0gaXNTdHJpbmcodXNlckNvbnRhaW5lcikgPyByb290LnF1ZXJ5U2VsZWN0b3IodXNlckNvbnRhaW5lcikgOiB1c2VyQ29udGFpbmVyO1xuICAgIGNvbnRhaW5lciA9IGN1c3RvbUNvbnRhaW5lciB8fCByb290LmNoaWxkcmVuWzBdO1xuICAgIGNvbnN0IGN1c3RvbVNsaWRlcyA9IGlzU3RyaW5nKHVzZXJTbGlkZXMpID8gY29udGFpbmVyLnF1ZXJ5U2VsZWN0b3JBbGwodXNlclNsaWRlcykgOiB1c2VyU2xpZGVzO1xuICAgIHNsaWRlcyA9IFtdLnNsaWNlLmNhbGwoY3VzdG9tU2xpZGVzIHx8IGNvbnRhaW5lci5jaGlsZHJlbik7XG4gIH1cbiAgZnVuY3Rpb24gY3JlYXRlRW5naW5lKG9wdGlvbnMpIHtcbiAgICBjb25zdCBlbmdpbmUgPSBFbmdpbmUocm9vdCwgY29udGFpbmVyLCBzbGlkZXMsIG93bmVyRG9jdW1lbnQsIG93bmVyV2luZG93LCBvcHRpb25zLCBldmVudEhhbmRsZXIpO1xuICAgIGlmIChvcHRpb25zLmxvb3AgJiYgIWVuZ2luZS5zbGlkZUxvb3Blci5jYW5Mb29wKCkpIHtcbiAgICAgIGNvbnN0IG9wdGlvbnNXaXRob3V0TG9vcCA9IE9iamVjdC5hc3NpZ24oe30sIG9wdGlvbnMsIHtcbiAgICAgICAgbG9vcDogZmFsc2VcbiAgICAgIH0pO1xuICAgICAgcmV0dXJuIGNyZWF0ZUVuZ2luZShvcHRpb25zV2l0aG91dExvb3ApO1xuICAgIH1cbiAgICByZXR1cm4gZW5naW5lO1xuICB9XG4gIGZ1bmN0aW9uIGFjdGl2YXRlKHdpdGhPcHRpb25zLCB3aXRoUGx1Z2lucykge1xuICAgIGlmIChkZXN0cm95ZWQpIHJldHVybjtcbiAgICBvcHRpb25zQmFzZSA9IG1lcmdlT3B0aW9ucyhvcHRpb25zQmFzZSwgd2l0aE9wdGlvbnMpO1xuICAgIG9wdGlvbnMgPSBvcHRpb25zQXRNZWRpYShvcHRpb25zQmFzZSk7XG4gICAgcGx1Z2luTGlzdCA9IHdpdGhQbHVnaW5zIHx8IHBsdWdpbkxpc3Q7XG4gICAgc3RvcmVFbGVtZW50cygpO1xuICAgIGVuZ2luZSA9IGNyZWF0ZUVuZ2luZShvcHRpb25zKTtcbiAgICBvcHRpb25zTWVkaWFRdWVyaWVzKFtvcHRpb25zQmFzZSwgLi4ucGx1Z2luTGlzdC5tYXAoKHtcbiAgICAgIG9wdGlvbnNcbiAgICB9KSA9PiBvcHRpb25zKV0pLmZvckVhY2gocXVlcnkgPT4gbWVkaWFIYW5kbGVycy5hZGQocXVlcnksICdjaGFuZ2UnLCByZUFjdGl2YXRlKSk7XG4gICAgaWYgKCFvcHRpb25zLmFjdGl2ZSkgcmV0dXJuO1xuICAgIGVuZ2luZS50cmFuc2xhdGUudG8oZW5naW5lLmxvY2F0aW9uLmdldCgpKTtcbiAgICBlbmdpbmUuYW5pbWF0aW9uLmluaXQoKTtcbiAgICBlbmdpbmUuc2xpZGVzSW5WaWV3LmluaXQoKTtcbiAgICBlbmdpbmUuc2xpZGVGb2N1cy5pbml0KHNlbGYpO1xuICAgIGVuZ2luZS5ldmVudEhhbmRsZXIuaW5pdChzZWxmKTtcbiAgICBlbmdpbmUucmVzaXplSGFuZGxlci5pbml0KHNlbGYpO1xuICAgIGVuZ2luZS5zbGlkZXNIYW5kbGVyLmluaXQoc2VsZik7XG4gICAgaWYgKGVuZ2luZS5vcHRpb25zLmxvb3ApIGVuZ2luZS5zbGlkZUxvb3Blci5sb29wKCk7XG4gICAgaWYgKGNvbnRhaW5lci5vZmZzZXRQYXJlbnQgJiYgc2xpZGVzLmxlbmd0aCkgZW5naW5lLmRyYWdIYW5kbGVyLmluaXQoc2VsZik7XG4gICAgcGx1Z2luQXBpcyA9IHBsdWdpbnNIYW5kbGVyLmluaXQoc2VsZiwgcGx1Z2luTGlzdCk7XG4gIH1cbiAgZnVuY3Rpb24gcmVBY3RpdmF0ZSh3aXRoT3B0aW9ucywgd2l0aFBsdWdpbnMpIHtcbiAgICBjb25zdCBzdGFydEluZGV4ID0gc2VsZWN0ZWRTY3JvbGxTbmFwKCk7XG4gICAgZGVBY3RpdmF0ZSgpO1xuICAgIGFjdGl2YXRlKG1lcmdlT3B0aW9ucyh7XG4gICAgICBzdGFydEluZGV4XG4gICAgfSwgd2l0aE9wdGlvbnMpLCB3aXRoUGx1Z2lucyk7XG4gICAgZXZlbnRIYW5kbGVyLmVtaXQoJ3JlSW5pdCcpO1xuICB9XG4gIGZ1bmN0aW9uIGRlQWN0aXZhdGUoKSB7XG4gICAgZW5naW5lLmRyYWdIYW5kbGVyLmRlc3Ryb3koKTtcbiAgICBlbmdpbmUuZXZlbnRTdG9yZS5jbGVhcigpO1xuICAgIGVuZ2luZS50cmFuc2xhdGUuY2xlYXIoKTtcbiAgICBlbmdpbmUuc2xpZGVMb29wZXIuY2xlYXIoKTtcbiAgICBlbmdpbmUucmVzaXplSGFuZGxlci5kZXN0cm95KCk7XG4gICAgZW5naW5lLnNsaWRlc0hhbmRsZXIuZGVzdHJveSgpO1xuICAgIGVuZ2luZS5zbGlkZXNJblZpZXcuZGVzdHJveSgpO1xuICAgIGVuZ2luZS5hbmltYXRpb24uZGVzdHJveSgpO1xuICAgIHBsdWdpbnNIYW5kbGVyLmRlc3Ryb3koKTtcbiAgICBtZWRpYUhhbmRsZXJzLmNsZWFyKCk7XG4gIH1cbiAgZnVuY3Rpb24gZGVzdHJveSgpIHtcbiAgICBpZiAoZGVzdHJveWVkKSByZXR1cm47XG4gICAgZGVzdHJveWVkID0gdHJ1ZTtcbiAgICBtZWRpYUhhbmRsZXJzLmNsZWFyKCk7XG4gICAgZGVBY3RpdmF0ZSgpO1xuICAgIGV2ZW50SGFuZGxlci5lbWl0KCdkZXN0cm95Jyk7XG4gICAgZXZlbnRIYW5kbGVyLmNsZWFyKCk7XG4gIH1cbiAgZnVuY3Rpb24gc2Nyb2xsVG8oaW5kZXgsIGp1bXAsIGRpcmVjdGlvbikge1xuICAgIGlmICghb3B0aW9ucy5hY3RpdmUgfHwgZGVzdHJveWVkKSByZXR1cm47XG4gICAgZW5naW5lLnNjcm9sbEJvZHkudXNlQmFzZUZyaWN0aW9uKCkudXNlRHVyYXRpb24oanVtcCA9PT0gdHJ1ZSA/IDAgOiBvcHRpb25zLmR1cmF0aW9uKTtcbiAgICBlbmdpbmUuc2Nyb2xsVG8uaW5kZXgoaW5kZXgsIGRpcmVjdGlvbiB8fCAwKTtcbiAgfVxuICBmdW5jdGlvbiBzY3JvbGxOZXh0KGp1bXApIHtcbiAgICBjb25zdCBuZXh0ID0gZW5naW5lLmluZGV4LmFkZCgxKS5nZXQoKTtcbiAgICBzY3JvbGxUbyhuZXh0LCBqdW1wLCAtMSk7XG4gIH1cbiAgZnVuY3Rpb24gc2Nyb2xsUHJldihqdW1wKSB7XG4gICAgY29uc3QgcHJldiA9IGVuZ2luZS5pbmRleC5hZGQoLTEpLmdldCgpO1xuICAgIHNjcm9sbFRvKHByZXYsIGp1bXAsIDEpO1xuICB9XG4gIGZ1bmN0aW9uIGNhblNjcm9sbE5leHQoKSB7XG4gICAgY29uc3QgbmV4dCA9IGVuZ2luZS5pbmRleC5hZGQoMSkuZ2V0KCk7XG4gICAgcmV0dXJuIG5leHQgIT09IHNlbGVjdGVkU2Nyb2xsU25hcCgpO1xuICB9XG4gIGZ1bmN0aW9uIGNhblNjcm9sbFByZXYoKSB7XG4gICAgY29uc3QgcHJldiA9IGVuZ2luZS5pbmRleC5hZGQoLTEpLmdldCgpO1xuICAgIHJldHVybiBwcmV2ICE9PSBzZWxlY3RlZFNjcm9sbFNuYXAoKTtcbiAgfVxuICBmdW5jdGlvbiBzY3JvbGxTbmFwTGlzdCgpIHtcbiAgICByZXR1cm4gZW5naW5lLnNjcm9sbFNuYXBMaXN0O1xuICB9XG4gIGZ1bmN0aW9uIHNjcm9sbFByb2dyZXNzKCkge1xuICAgIHJldHVybiBlbmdpbmUuc2Nyb2xsUHJvZ3Jlc3MuZ2V0KGVuZ2luZS5vZmZzZXRMb2NhdGlvbi5nZXQoKSk7XG4gIH1cbiAgZnVuY3Rpb24gc2VsZWN0ZWRTY3JvbGxTbmFwKCkge1xuICAgIHJldHVybiBlbmdpbmUuaW5kZXguZ2V0KCk7XG4gIH1cbiAgZnVuY3Rpb24gcHJldmlvdXNTY3JvbGxTbmFwKCkge1xuICAgIHJldHVybiBlbmdpbmUuaW5kZXhQcmV2aW91cy5nZXQoKTtcbiAgfVxuICBmdW5jdGlvbiBzbGlkZXNJblZpZXcoKSB7XG4gICAgcmV0dXJuIGVuZ2luZS5zbGlkZXNJblZpZXcuZ2V0KCk7XG4gIH1cbiAgZnVuY3Rpb24gc2xpZGVzTm90SW5WaWV3KCkge1xuICAgIHJldHVybiBlbmdpbmUuc2xpZGVzSW5WaWV3LmdldChmYWxzZSk7XG4gIH1cbiAgZnVuY3Rpb24gcGx1Z2lucygpIHtcbiAgICByZXR1cm4gcGx1Z2luQXBpcztcbiAgfVxuICBmdW5jdGlvbiBpbnRlcm5hbEVuZ2luZSgpIHtcbiAgICByZXR1cm4gZW5naW5lO1xuICB9XG4gIGZ1bmN0aW9uIHJvb3ROb2RlKCkge1xuICAgIHJldHVybiByb290O1xuICB9XG4gIGZ1bmN0aW9uIGNvbnRhaW5lck5vZGUoKSB7XG4gICAgcmV0dXJuIGNvbnRhaW5lcjtcbiAgfVxuICBmdW5jdGlvbiBzbGlkZU5vZGVzKCkge1xuICAgIHJldHVybiBzbGlkZXM7XG4gIH1cbiAgY29uc3Qgc2VsZiA9IHtcbiAgICBjYW5TY3JvbGxOZXh0LFxuICAgIGNhblNjcm9sbFByZXYsXG4gICAgY29udGFpbmVyTm9kZSxcbiAgICBpbnRlcm5hbEVuZ2luZSxcbiAgICBkZXN0cm95LFxuICAgIG9mZixcbiAgICBvbixcbiAgICBlbWl0LFxuICAgIHBsdWdpbnMsXG4gICAgcHJldmlvdXNTY3JvbGxTbmFwLFxuICAgIHJlSW5pdCxcbiAgICByb290Tm9kZSxcbiAgICBzY3JvbGxOZXh0LFxuICAgIHNjcm9sbFByZXYsXG4gICAgc2Nyb2xsUHJvZ3Jlc3MsXG4gICAgc2Nyb2xsU25hcExpc3QsXG4gICAgc2Nyb2xsVG8sXG4gICAgc2VsZWN0ZWRTY3JvbGxTbmFwLFxuICAgIHNsaWRlTm9kZXMsXG4gICAgc2xpZGVzSW5WaWV3LFxuICAgIHNsaWRlc05vdEluVmlld1xuICB9O1xuICBhY3RpdmF0ZSh1c2VyT3B0aW9ucywgdXNlclBsdWdpbnMpO1xuICBzZXRUaW1lb3V0KCgpID0+IGV2ZW50SGFuZGxlci5lbWl0KCdpbml0JyksIDApO1xuICByZXR1cm4gc2VsZjtcbn1cbkVtYmxhQ2Fyb3VzZWwuZ2xvYmFsT3B0aW9ucyA9IHVuZGVmaW5lZDtcblxuZXhwb3J0IHsgRW1ibGFDYXJvdXNlbCBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1lbWJsYS1jYXJvdXNlbC5lc20uanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/embla-carousel/esm/embla-carousel.esm.js\n");

/***/ })

};
;
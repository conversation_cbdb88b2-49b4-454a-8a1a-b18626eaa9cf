/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-compiler-runtime";
exports.ids = ["vendor-chunks/react-compiler-runtime"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-compiler-runtime/dist/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-compiler-runtime/dist/index.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @lightSyntaxTransform\n * @noflow\n * @nolint\n * @preventMunge\n * @preserve-invariant-messages\n */\n\n\"use no memo\";\n\"use strict\";\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar index_exports = {};\n__export(index_exports, {\n  $dispatcherGuard: () => $dispatcherGuard,\n  $makeReadOnly: () => $makeReadOnly,\n  $reset: () => $reset,\n  $structuralCheck: () => $structuralCheck,\n  c: () => c,\n  clearRenderCounterRegistry: () => clearRenderCounterRegistry,\n  renderCounterRegistry: () => renderCounterRegistry,\n  useRenderCounter: () => useRenderCounter\n});\nmodule.exports = __toCommonJS(index_exports);\nvar React = __toESM(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar { useRef, useEffect, isValidElement } = React;\nvar _a;\nvar ReactSecretInternals = (\n  //@ts-ignore\n  (_a = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE) != null ? _a : React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\n);\nvar $empty = Symbol.for(\"react.memo_cache_sentinel\");\nvar _a2;\nvar c = (\n  // @ts-expect-error\n  typeof ((_a2 = React.__COMPILER_RUNTIME) == null ? void 0 : _a2.c) === \"function\" ? (\n    // @ts-expect-error\n    React.__COMPILER_RUNTIME.c\n  ) : function c2(size) {\n    return React.useMemo(() => {\n      const $ = new Array(size);\n      for (let ii = 0; ii < size; ii++) {\n        $[ii] = $empty;\n      }\n      $[$empty] = true;\n      return $;\n    }, []);\n  }\n);\nvar LazyGuardDispatcher = {};\n[\n  \"readContext\",\n  \"useCallback\",\n  \"useContext\",\n  \"useEffect\",\n  \"useImperativeHandle\",\n  \"useInsertionEffect\",\n  \"useLayoutEffect\",\n  \"useMemo\",\n  \"useReducer\",\n  \"useRef\",\n  \"useState\",\n  \"useDebugValue\",\n  \"useDeferredValue\",\n  \"useTransition\",\n  \"useMutableSource\",\n  \"useSyncExternalStore\",\n  \"useId\",\n  \"unstable_isNewReconciler\",\n  \"getCacheSignal\",\n  \"getCacheForType\",\n  \"useCacheRefresh\"\n].forEach((name) => {\n  LazyGuardDispatcher[name] = () => {\n    throw new Error(\n      `[React] Unexpected React hook call (${name}) from a React compiled function. Check that all hooks are called directly and named according to convention ('use[A-Z]') `\n    );\n  };\n});\nvar originalDispatcher = null;\nLazyGuardDispatcher[\"useMemoCache\"] = (count) => {\n  if (originalDispatcher == null) {\n    throw new Error(\n      \"React Compiler internal invariant violation: unexpected null dispatcher\"\n    );\n  } else {\n    return originalDispatcher.useMemoCache(count);\n  }\n};\nfunction setCurrent(newDispatcher) {\n  ReactSecretInternals.ReactCurrentDispatcher.current = newDispatcher;\n  return ReactSecretInternals.ReactCurrentDispatcher.current;\n}\nvar guardFrames = [];\nfunction $dispatcherGuard(kind) {\n  const curr = ReactSecretInternals.ReactCurrentDispatcher.current;\n  if (kind === 0 /* PushGuardContext */) {\n    guardFrames.push(curr);\n    if (guardFrames.length === 1) {\n      originalDispatcher = curr;\n    }\n    if (curr === LazyGuardDispatcher) {\n      throw new Error(\n        `[React] Unexpected call to custom hook or component from a React compiled function. Check that (1) all hooks are called directly and named according to convention ('use[A-Z]') and (2) components are returned as JSX instead of being directly invoked.`\n      );\n    }\n    setCurrent(LazyGuardDispatcher);\n  } else if (kind === 1 /* PopGuardContext */) {\n    const lastFrame = guardFrames.pop();\n    if (lastFrame == null) {\n      throw new Error(\n        \"React Compiler internal error: unexpected null in guard stack\"\n      );\n    }\n    if (guardFrames.length === 0) {\n      originalDispatcher = null;\n    }\n    setCurrent(lastFrame);\n  } else if (kind === 2 /* PushExpectHook */) {\n    guardFrames.push(curr);\n    setCurrent(originalDispatcher);\n  } else if (kind === 3 /* PopExpectHook */) {\n    const lastFrame = guardFrames.pop();\n    if (lastFrame == null) {\n      throw new Error(\n        \"React Compiler internal error: unexpected null in guard stack\"\n      );\n    }\n    setCurrent(lastFrame);\n  } else {\n    throw new Error(\"React Compiler internal error: unreachable block\" + kind);\n  }\n}\nfunction $reset($) {\n  for (let ii = 0; ii < $.length; ii++) {\n    $[ii] = $empty;\n  }\n}\nfunction $makeReadOnly() {\n  throw new Error(\"TODO: implement $makeReadOnly in react-compiler-runtime\");\n}\nvar renderCounterRegistry = /* @__PURE__ */ new Map();\nfunction clearRenderCounterRegistry() {\n  for (const counters of renderCounterRegistry.values()) {\n    counters.forEach((counter) => {\n      counter.count = 0;\n    });\n  }\n}\nfunction registerRenderCounter(name, val) {\n  let counters = renderCounterRegistry.get(name);\n  if (counters == null) {\n    counters = /* @__PURE__ */ new Set();\n    renderCounterRegistry.set(name, counters);\n  }\n  counters.add(val);\n}\nfunction removeRenderCounter(name, val) {\n  const counters = renderCounterRegistry.get(name);\n  if (counters == null) {\n    return;\n  }\n  counters.delete(val);\n}\nfunction useRenderCounter(name) {\n  const val = useRef(null);\n  if (val.current != null) {\n    val.current.count += 1;\n  }\n  useEffect(() => {\n    if (val.current == null) {\n      const counter = { count: 0 };\n      registerRenderCounter(name, counter);\n      val.current = counter;\n    }\n    return () => {\n      if (val.current !== null) {\n        removeRenderCounter(name, val.current);\n      }\n    };\n  });\n}\nvar seenErrors = /* @__PURE__ */ new Set();\nfunction $structuralCheck(oldValue, newValue, variableName, fnName, kind, loc) {\n  function error(l, r, path, depth) {\n    const str = `${fnName}:${loc} [${kind}] ${variableName}${path} changed from ${l} to ${r} at depth ${depth}`;\n    if (seenErrors.has(str)) {\n      return;\n    }\n    seenErrors.add(str);\n    console.error(str);\n  }\n  const depthLimit = 2;\n  function recur(oldValue2, newValue2, path, depth) {\n    if (depth > depthLimit) {\n      return;\n    } else if (oldValue2 === newValue2) {\n      return;\n    } else if (typeof oldValue2 !== typeof newValue2) {\n      error(`type ${typeof oldValue2}`, `type ${typeof newValue2}`, path, depth);\n    } else if (typeof oldValue2 === \"object\") {\n      const oldArray = Array.isArray(oldValue2);\n      const newArray = Array.isArray(newValue2);\n      if (oldValue2 === null && newValue2 !== null) {\n        error(\"null\", `type ${typeof newValue2}`, path, depth);\n      } else if (newValue2 === null) {\n        error(`type ${typeof oldValue2}`, \"null\", path, depth);\n      } else if (oldValue2 instanceof Map) {\n        if (!(newValue2 instanceof Map)) {\n          error(`Map instance`, `other value`, path, depth);\n        } else if (oldValue2.size !== newValue2.size) {\n          error(\n            `Map instance with size ${oldValue2.size}`,\n            `Map instance with size ${newValue2.size}`,\n            path,\n            depth\n          );\n        } else {\n          for (const [k, v] of oldValue2) {\n            if (!newValue2.has(k)) {\n              error(\n                `Map instance with key ${k}`,\n                `Map instance without key ${k}`,\n                path,\n                depth\n              );\n            } else {\n              recur(v, newValue2.get(k), `${path}.get(${k})`, depth + 1);\n            }\n          }\n        }\n      } else if (newValue2 instanceof Map) {\n        error(\"other value\", `Map instance`, path, depth);\n      } else if (oldValue2 instanceof Set) {\n        if (!(newValue2 instanceof Set)) {\n          error(`Set instance`, `other value`, path, depth);\n        } else if (oldValue2.size !== newValue2.size) {\n          error(\n            `Set instance with size ${oldValue2.size}`,\n            `Set instance with size ${newValue2.size}`,\n            path,\n            depth\n          );\n        } else {\n          for (const v of newValue2) {\n            if (!oldValue2.has(v)) {\n              error(\n                `Set instance without element ${v}`,\n                `Set instance with element ${v}`,\n                path,\n                depth\n              );\n            }\n          }\n        }\n      } else if (newValue2 instanceof Set) {\n        error(\"other value\", `Set instance`, path, depth);\n      } else if (oldArray || newArray) {\n        if (oldArray !== newArray) {\n          error(\n            `type ${oldArray ? \"array\" : \"object\"}`,\n            `type ${newArray ? \"array\" : \"object\"}`,\n            path,\n            depth\n          );\n        } else if (oldValue2.length !== newValue2.length) {\n          error(\n            `array with length ${oldValue2.length}`,\n            `array with length ${newValue2.length}`,\n            path,\n            depth\n          );\n        } else {\n          for (let ii = 0; ii < oldValue2.length; ii++) {\n            recur(oldValue2[ii], newValue2[ii], `${path}[${ii}]`, depth + 1);\n          }\n        }\n      } else if (isValidElement(oldValue2) || isValidElement(newValue2)) {\n        if (isValidElement(oldValue2) !== isValidElement(newValue2)) {\n          error(\n            `type ${isValidElement(oldValue2) ? \"React element\" : \"object\"}`,\n            `type ${isValidElement(newValue2) ? \"React element\" : \"object\"}`,\n            path,\n            depth\n          );\n        } else if (oldValue2.type !== newValue2.type) {\n          error(\n            `React element of type ${oldValue2.type}`,\n            `React element of type ${newValue2.type}`,\n            path,\n            depth\n          );\n        } else {\n          recur(\n            oldValue2.props,\n            newValue2.props,\n            `[props of ${path}]`,\n            depth + 1\n          );\n        }\n      } else {\n        for (const key in newValue2) {\n          if (!(key in oldValue2)) {\n            error(\n              `object without key ${key}`,\n              `object with key ${key}`,\n              path,\n              depth\n            );\n          }\n        }\n        for (const key in oldValue2) {\n          if (!(key in newValue2)) {\n            error(\n              `object with key ${key}`,\n              `object without key ${key}`,\n              path,\n              depth\n            );\n          } else {\n            recur(oldValue2[key], newValue2[key], `${path}.${key}`, depth + 1);\n          }\n        }\n      }\n    } else if (typeof oldValue2 === \"function\") {\n      return;\n    } else if (isNaN(oldValue2) || isNaN(newValue2)) {\n      if (isNaN(oldValue2) !== isNaN(newValue2)) {\n        error(\n          `${isNaN(oldValue2) ? \"NaN\" : \"non-NaN value\"}`,\n          `${isNaN(newValue2) ? \"NaN\" : \"non-NaN value\"}`,\n          path,\n          depth\n        );\n      }\n    } else if (oldValue2 !== newValue2) {\n      error(oldValue2, newValue2, path, depth);\n    }\n  }\n  recur(oldValue, newValue, \"\", 0);\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-compiler-runtime/dist/index.js\n");

/***/ })

};
;
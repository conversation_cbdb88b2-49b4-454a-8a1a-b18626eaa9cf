"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-hook-form";
exports.ids = ["vendor-chunks/react-hook-form"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/react-hook-form/dist/index.esm.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   FormProvider: () => (/* binding */ FormProvider),\n/* harmony export */   appendErrors: () => (/* binding */ appendErrors),\n/* harmony export */   createFormControl: () => (/* binding */ createFormControl),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useFieldArray: () => (/* binding */ useFieldArray),\n/* harmony export */   useForm: () => (/* binding */ useForm),\n/* harmony export */   useFormContext: () => (/* binding */ useFormContext),\n/* harmony export */   useFormState: () => (/* binding */ useFormState),\n/* harmony export */   useWatch: () => (/* binding */ useWatch)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\nvar isCheckBoxInput = (element) => element.type === 'checkbox';\n\nvar isDateObject = (value) => value instanceof Date;\n\nvar isNullOrUndefined = (value) => value == null;\n\nconst isObjectType = (value) => typeof value === 'object';\nvar isObject = (value) => !isNullOrUndefined(value) &&\n    !Array.isArray(value) &&\n    isObjectType(value) &&\n    !isDateObject(value);\n\nvar getEventValue = (event) => isObject(event) && event.target\n    ? isCheckBoxInput(event.target)\n        ? event.target.checked\n        : event.target.value\n    : event;\n\nvar getNodeParentName = (name) => name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n\nvar isNameInFieldArray = (names, name) => names.has(getNodeParentName(name));\n\nvar isPlainObject = (tempObject) => {\n    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n    return (isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf'));\n};\n\nvar isWeb = typeof window !== 'undefined' &&\n    typeof window.HTMLElement !== 'undefined' &&\n    typeof document !== 'undefined';\n\nfunction cloneObject(data) {\n    let copy;\n    const isArray = Array.isArray(data);\n    const isFileListInstance = typeof FileList !== 'undefined' ? data instanceof FileList : false;\n    if (data instanceof Date) {\n        copy = new Date(data);\n    }\n    else if (!(isWeb && (data instanceof Blob || isFileListInstance)) &&\n        (isArray || isObject(data))) {\n        copy = isArray ? [] : {};\n        if (!isArray && !isPlainObject(data)) {\n            copy = data;\n        }\n        else {\n            for (const key in data) {\n                if (data.hasOwnProperty(key)) {\n                    copy[key] = cloneObject(data[key]);\n                }\n            }\n        }\n    }\n    else {\n        return data;\n    }\n    return copy;\n}\n\nvar isKey = (value) => /^\\w*$/.test(value);\n\nvar isUndefined = (val) => val === undefined;\n\nvar compact = (value) => Array.isArray(value) ? value.filter(Boolean) : [];\n\nvar stringToPath = (input) => compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n\nvar get = (object, path, defaultValue) => {\n    if (!path || !isObject(object)) {\n        return defaultValue;\n    }\n    const result = (isKey(path) ? [path] : stringToPath(path)).reduce((result, key) => isNullOrUndefined(result) ? result : result[key], object);\n    return isUndefined(result) || result === object\n        ? isUndefined(object[path])\n            ? defaultValue\n            : object[path]\n        : result;\n};\n\nvar isBoolean = (value) => typeof value === 'boolean';\n\nvar set = (object, path, value) => {\n    let index = -1;\n    const tempPath = isKey(path) ? [path] : stringToPath(path);\n    const length = tempPath.length;\n    const lastIndex = length - 1;\n    while (++index < length) {\n        const key = tempPath[index];\n        let newValue = value;\n        if (index !== lastIndex) {\n            const objValue = object[key];\n            newValue =\n                isObject(objValue) || Array.isArray(objValue)\n                    ? objValue\n                    : !isNaN(+tempPath[index + 1])\n                        ? []\n                        : {};\n        }\n        if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n            return;\n        }\n        object[key] = newValue;\n        object = object[key];\n    }\n};\n\nconst EVENTS = {\n    BLUR: 'blur',\n    FOCUS_OUT: 'focusout',\n    CHANGE: 'change',\n};\nconst VALIDATION_MODE = {\n    onBlur: 'onBlur',\n    onChange: 'onChange',\n    onSubmit: 'onSubmit',\n    onTouched: 'onTouched',\n    all: 'all',\n};\nconst INPUT_VALIDATION_RULES = {\n    max: 'max',\n    min: 'min',\n    maxLength: 'maxLength',\n    minLength: 'minLength',\n    pattern: 'pattern',\n    required: 'required',\n    validate: 'validate',\n};\n\nconst HookFormContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nHookFormContext.displayName = 'HookFormContext';\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst useFormContext = () => react__WEBPACK_IMPORTED_MODULE_0__.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst FormProvider = (props) => {\n    const { children, ...data } = props;\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(HookFormContext.Provider, { value: data }, children));\n};\n\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true) => {\n    const result = {\n        defaultValues: control._defaultValues,\n    };\n    for (const key in formState) {\n        Object.defineProperty(result, key, {\n            get: () => {\n                const _key = key;\n                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n                }\n                localProxyFormState && (localProxyFormState[_key] = true);\n                return formState[_key];\n            },\n        });\n    }\n    return result;\n};\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState(props) {\n    const methods = useFormContext();\n    const { control = methods.control, disabled, name, exact } = props || {};\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._formState);\n    const _localProxyFormState = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        isDirty: false,\n        isLoading: false,\n        dirtyFields: false,\n        touchedFields: false,\n        validatingFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false,\n    });\n    useIsomorphicLayoutEffect(() => control._subscribe({\n        name,\n        formState: _localProxyFormState.current,\n        exact,\n        callback: (formState) => {\n            !disabled &&\n                updateFormState({\n                    ...control._formState,\n                    ...formState,\n                });\n        },\n    }), [name, disabled, exact]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        _localProxyFormState.current.isValid && control._setValid(true);\n    }, [control]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => getProxyFormState(formState, control, _localProxyFormState.current, false), [formState, control]);\n}\n\nvar isString = (value) => typeof value === 'string';\n\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue) => {\n    if (isString(names)) {\n        isGlobal && _names.watch.add(names);\n        return get(formValues, names, defaultValue);\n    }\n    if (Array.isArray(names)) {\n        return names.map((fieldName) => (isGlobal && _names.watch.add(fieldName),\n            get(formValues, fieldName)));\n    }\n    isGlobal && (_names.watchAll = true);\n    return formValues;\n};\n\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nfunction useWatch(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, defaultValue, disabled, exact, } = props || {};\n    const _defaultValue = react__WEBPACK_IMPORTED_MODULE_0__.useRef(defaultValue);\n    const [value, updateValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getWatch(name, _defaultValue.current));\n    useIsomorphicLayoutEffect(() => control._subscribe({\n        name,\n        formState: {\n            values: true,\n        },\n        exact,\n        callback: (formState) => !disabled &&\n            updateValue(generateWatchOutput(name, control._names, formState.values || control._formValues, false, _defaultValue.current)),\n    }), [name, control, disabled, exact]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => control._removeUnmounted());\n    return value;\n}\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nfunction useController(props) {\n    const methods = useFormContext();\n    const { name, disabled, control = methods.control, shouldUnregister } = props;\n    const isArrayField = isNameInFieldArray(control._names.array, name);\n    const value = useWatch({\n        control,\n        name,\n        defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n        exact: true,\n    });\n    const formState = useFormState({\n        control,\n        name,\n        exact: true,\n    });\n    const _props = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n    const _registerProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control.register(name, {\n        ...props.rules,\n        value,\n        ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }));\n    const fieldState = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => Object.defineProperties({}, {\n        invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n        },\n        isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n        },\n        isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n        },\n        isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n        },\n        error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n        },\n    }), [formState, name]);\n    const onChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event) => _registerProps.current.onChange({\n        target: {\n            value: getEventValue(event),\n            name: name,\n        },\n        type: EVENTS.CHANGE,\n    }), [name]);\n    const onBlur = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => _registerProps.current.onBlur({\n        target: {\n            value: get(control._formValues, name),\n            name: name,\n        },\n        type: EVENTS.BLUR,\n    }), [name, control._formValues]);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((elm) => {\n        const field = get(control._fields, name);\n        if (field && elm) {\n            field._f.ref = {\n                focus: () => elm.focus && elm.focus(),\n                select: () => elm.select && elm.select(),\n                setCustomValidity: (message) => elm.setCustomValidity(message),\n                reportValidity: () => elm.reportValidity(),\n            };\n        }\n    }, [control._fields, name]);\n    const field = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n        name,\n        value,\n        ...(isBoolean(disabled) || formState.disabled\n            ? { disabled: formState.disabled || disabled }\n            : {}),\n        onChange,\n        onBlur,\n        ref,\n    }), [name, disabled, formState.disabled, onChange, onBlur, ref, value]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n        control.register(name, {\n            ..._props.current.rules,\n            ...(isBoolean(_props.current.disabled)\n                ? { disabled: _props.current.disabled }\n                : {}),\n        });\n        const updateMounted = (name, value) => {\n            const field = get(control._fields, name);\n            if (field && field._f) {\n                field._f.mount = value;\n            }\n        };\n        updateMounted(name, true);\n        if (_shouldUnregisterField) {\n            const value = cloneObject(get(control._options.defaultValues, name));\n            set(control._defaultValues, name, value);\n            if (isUndefined(get(control._formValues, name))) {\n                set(control._formValues, name, value);\n            }\n        }\n        !isArrayField && control.register(name);\n        return () => {\n            (isArrayField\n                ? _shouldUnregisterField && !control._state.action\n                : _shouldUnregisterField)\n                ? control.unregister(name)\n                : updateMounted(name, false);\n        };\n    }, [name, control, isArrayField, shouldUnregister]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        control._setDisabledField({\n            disabled,\n            name,\n        });\n    }, [disabled, name, control]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n        field,\n        formState,\n        fieldState,\n    }), [field, formState, fieldState]);\n}\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = (props) => props.render(useController(props));\n\nconst flatten = (obj) => {\n    const output = {};\n    for (const key of Object.keys(obj)) {\n        if (isObjectType(obj[key]) && obj[key] !== null) {\n            const nested = flatten(obj[key]);\n            for (const nestedKey of Object.keys(nested)) {\n                output[`${key}.${nestedKey}`] = nested[nestedKey];\n            }\n        }\n        else {\n            output[key] = obj[key];\n        }\n    }\n    return output;\n};\n\nconst POST_REQUEST = 'post';\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form(props) {\n    const methods = useFormContext();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;\n    const submit = async (event) => {\n        let hasError = false;\n        let type = '';\n        await control.handleSubmit(async (data) => {\n            const formData = new FormData();\n            let formDataJson = '';\n            try {\n                formDataJson = JSON.stringify(data);\n            }\n            catch (_a) { }\n            const flattenFormValues = flatten(control._formValues);\n            for (const key in flattenFormValues) {\n                formData.append(key, flattenFormValues[key]);\n            }\n            if (onSubmit) {\n                await onSubmit({\n                    data,\n                    event,\n                    method,\n                    formData,\n                    formDataJson,\n                });\n            }\n            if (action) {\n                try {\n                    const shouldStringifySubmissionData = [\n                        headers && headers['Content-Type'],\n                        encType,\n                    ].some((value) => value && value.includes('json'));\n                    const response = await fetch(String(action), {\n                        method,\n                        headers: {\n                            ...headers,\n                            ...(encType ? { 'Content-Type': encType } : {}),\n                        },\n                        body: shouldStringifySubmissionData ? formDataJson : formData,\n                    });\n                    if (response &&\n                        (validateStatus\n                            ? !validateStatus(response.status)\n                            : response.status < 200 || response.status >= 300)) {\n                        hasError = true;\n                        onError && onError({ response });\n                        type = String(response.status);\n                    }\n                    else {\n                        onSuccess && onSuccess({ response });\n                    }\n                }\n                catch (error) {\n                    hasError = true;\n                    onError && onError({ error });\n                }\n            }\n        })(event);\n        if (hasError && props.control) {\n            props.control._subjects.state.next({\n                isSubmitSuccessful: false,\n            });\n            props.control.setError('root.server', {\n                type,\n            });\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        setMounted(true);\n    }, []);\n    return render ? (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render({\n        submit,\n    }))) : (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"form\", { noValidate: mounted, action: action, method: method, encType: encType, onSubmit: submit, ...rest }, children));\n}\n\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message) => validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n            ...(errors[name] && errors[name].types ? errors[name].types : {}),\n            [type]: message || true,\n        },\n    }\n    : {};\n\nvar convertToArrayPayload = (value) => (Array.isArray(value) ? value : [value]);\n\nvar createSubject = () => {\n    let _observers = [];\n    const next = (value) => {\n        for (const observer of _observers) {\n            observer.next && observer.next(value);\n        }\n    };\n    const subscribe = (observer) => {\n        _observers.push(observer);\n        return {\n            unsubscribe: () => {\n                _observers = _observers.filter((o) => o !== observer);\n            },\n        };\n    };\n    const unsubscribe = () => {\n        _observers = [];\n    };\n    return {\n        get observers() {\n            return _observers;\n        },\n        next,\n        subscribe,\n        unsubscribe,\n    };\n};\n\nvar isPrimitive = (value) => isNullOrUndefined(value) || !isObjectType(value);\n\nfunction deepEqual(object1, object2, _internal_visited = new WeakSet()) {\n    if (isPrimitive(object1) || isPrimitive(object2)) {\n        return object1 === object2;\n    }\n    if (isDateObject(object1) && isDateObject(object2)) {\n        return object1.getTime() === object2.getTime();\n    }\n    const keys1 = Object.keys(object1);\n    const keys2 = Object.keys(object2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    if (_internal_visited.has(object1) || _internal_visited.has(object2)) {\n        return true;\n    }\n    _internal_visited.add(object1);\n    _internal_visited.add(object2);\n    for (const key of keys1) {\n        const val1 = object1[key];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (key !== 'ref') {\n            const val2 = object2[key];\n            if ((isDateObject(val1) && isDateObject(val2)) ||\n                (isObject(val1) && isObject(val2)) ||\n                (Array.isArray(val1) && Array.isArray(val2))\n                ? !deepEqual(val1, val2, _internal_visited)\n                : val1 !== val2) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\n\nvar isEmptyObject = (value) => isObject(value) && !Object.keys(value).length;\n\nvar isFileInput = (element) => element.type === 'file';\n\nvar isFunction = (value) => typeof value === 'function';\n\nvar isHTMLElement = (value) => {\n    if (!isWeb) {\n        return false;\n    }\n    const owner = value ? value.ownerDocument : 0;\n    return (value instanceof\n        (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement));\n};\n\nvar isMultipleSelect = (element) => element.type === `select-multiple`;\n\nvar isRadioInput = (element) => element.type === 'radio';\n\nvar isRadioOrCheckbox = (ref) => isRadioInput(ref) || isCheckBoxInput(ref);\n\nvar live = (ref) => isHTMLElement(ref) && ref.isConnected;\n\nfunction baseGet(object, updatePath) {\n    const length = updatePath.slice(0, -1).length;\n    let index = 0;\n    while (index < length) {\n        object = isUndefined(object) ? index++ : object[updatePath[index++]];\n    }\n    return object;\n}\nfunction isEmptyArray(obj) {\n    for (const key in obj) {\n        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction unset(object, path) {\n    const paths = Array.isArray(path)\n        ? path\n        : isKey(path)\n            ? [path]\n            : stringToPath(path);\n    const childObject = paths.length === 1 ? object : baseGet(object, paths);\n    const index = paths.length - 1;\n    const key = paths[index];\n    if (childObject) {\n        delete childObject[key];\n    }\n    if (index !== 0 &&\n        ((isObject(childObject) && isEmptyObject(childObject)) ||\n            (Array.isArray(childObject) && isEmptyArray(childObject)))) {\n        unset(object, paths.slice(0, -1));\n    }\n    return object;\n}\n\nvar objectHasFunction = (data) => {\n    for (const key in data) {\n        if (isFunction(data[key])) {\n            return true;\n        }\n    }\n    return false;\n};\n\nfunction markFieldsDirty(data, fields = {}) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for (const key in data) {\n            if (Array.isArray(data[key]) ||\n                (isObject(data[key]) && !objectHasFunction(data[key]))) {\n                fields[key] = Array.isArray(data[key]) ? [] : {};\n                markFieldsDirty(data[key], fields[key]);\n            }\n            else if (!isNullOrUndefined(data[key])) {\n                fields[key] = true;\n            }\n        }\n    }\n    return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for (const key in data) {\n            if (Array.isArray(data[key]) ||\n                (isObject(data[key]) && !objectHasFunction(data[key]))) {\n                if (isUndefined(formValues) ||\n                    isPrimitive(dirtyFieldsFromValues[key])) {\n                    dirtyFieldsFromValues[key] = Array.isArray(data[key])\n                        ? markFieldsDirty(data[key], [])\n                        : { ...markFieldsDirty(data[key]) };\n                }\n                else {\n                    getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n                }\n            }\n            else {\n                dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n            }\n        }\n    }\n    return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues) => getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\n\nconst defaultResult = {\n    value: false,\n    isValid: false,\n};\nconst validResult = { value: true, isValid: true };\nvar getCheckboxValue = (options) => {\n    if (Array.isArray(options)) {\n        if (options.length > 1) {\n            const values = options\n                .filter((option) => option && option.checked && !option.disabled)\n                .map((option) => option.value);\n            return { value: values, isValid: !!values.length };\n        }\n        return options[0].checked && !options[0].disabled\n            ? // @ts-expect-error expected to work in the browser\n                options[0].attributes && !isUndefined(options[0].attributes.value)\n                    ? isUndefined(options[0].value) || options[0].value === ''\n                        ? validResult\n                        : { value: options[0].value, isValid: true }\n                    : validResult\n            : defaultResult;\n    }\n    return defaultResult;\n};\n\nvar getFieldValueAs = (value, { valueAsNumber, valueAsDate, setValueAs }) => isUndefined(value)\n    ? value\n    : valueAsNumber\n        ? value === ''\n            ? NaN\n            : value\n                ? +value\n                : value\n        : valueAsDate && isString(value)\n            ? new Date(value)\n            : setValueAs\n                ? setValueAs(value)\n                : value;\n\nconst defaultReturn = {\n    isValid: false,\n    value: null,\n};\nvar getRadioValue = (options) => Array.isArray(options)\n    ? options.reduce((previous, option) => option && option.checked && !option.disabled\n        ? {\n            isValid: true,\n            value: option.value,\n        }\n        : previous, defaultReturn)\n    : defaultReturn;\n\nfunction getFieldValue(_f) {\n    const ref = _f.ref;\n    if (isFileInput(ref)) {\n        return ref.files;\n    }\n    if (isRadioInput(ref)) {\n        return getRadioValue(_f.refs).value;\n    }\n    if (isMultipleSelect(ref)) {\n        return [...ref.selectedOptions].map(({ value }) => value);\n    }\n    if (isCheckBoxInput(ref)) {\n        return getCheckboxValue(_f.refs).value;\n    }\n    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation) => {\n    const fields = {};\n    for (const name of fieldsNames) {\n        const field = get(_fields, name);\n        field && set(fields, name, field._f);\n    }\n    return {\n        criteriaMode,\n        names: [...fieldsNames],\n        fields,\n        shouldUseNativeValidation,\n    };\n};\n\nvar isRegex = (value) => value instanceof RegExp;\n\nvar getRuleValue = (rule) => isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n        ? rule.source\n        : isObject(rule)\n            ? isRegex(rule.value)\n                ? rule.value.source\n                : rule.value\n            : rule;\n\nvar getValidationModes = (mode) => ({\n    isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n    isOnBlur: mode === VALIDATION_MODE.onBlur,\n    isOnChange: mode === VALIDATION_MODE.onChange,\n    isOnAll: mode === VALIDATION_MODE.all,\n    isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\nvar hasPromiseValidation = (fieldReference) => !!fieldReference &&\n    !!fieldReference.validate &&\n    !!((isFunction(fieldReference.validate) &&\n        fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n        (isObject(fieldReference.validate) &&\n            Object.values(fieldReference.validate).find((validateFunction) => validateFunction.constructor.name === ASYNC_FUNCTION)));\n\nvar hasValidation = (options) => options.mount &&\n    (options.required ||\n        options.min ||\n        options.max ||\n        options.maxLength ||\n        options.minLength ||\n        options.pattern ||\n        options.validate);\n\nvar isWatched = (name, _names, isBlurEvent) => !isBlurEvent &&\n    (_names.watchAll ||\n        _names.watch.has(name) ||\n        [..._names.watch].some((watchName) => name.startsWith(watchName) &&\n            /^\\.\\w+/.test(name.slice(watchName.length))));\n\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly) => {\n    for (const key of fieldsNames || Object.keys(fields)) {\n        const field = get(fields, key);\n        if (field) {\n            const { _f, ...currentField } = field;\n            if (_f) {\n                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n                    return true;\n                }\n                else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n                    return true;\n                }\n                else {\n                    if (iterateFieldsByAction(currentField, action)) {\n                        break;\n                    }\n                }\n            }\n            else if (isObject(currentField)) {\n                if (iterateFieldsByAction(currentField, action)) {\n                    break;\n                }\n            }\n        }\n    }\n    return;\n};\n\nfunction schemaErrorLookup(errors, _fields, name) {\n    const error = get(errors, name);\n    if (error || isKey(name)) {\n        return {\n            error,\n            name,\n        };\n    }\n    const names = name.split('.');\n    while (names.length) {\n        const fieldName = names.join('.');\n        const field = get(_fields, fieldName);\n        const foundError = get(errors, fieldName);\n        if (field && !Array.isArray(field) && name !== fieldName) {\n            return { name };\n        }\n        if (foundError && foundError.type) {\n            return {\n                name: fieldName,\n                error: foundError,\n            };\n        }\n        if (foundError && foundError.root && foundError.root.type) {\n            return {\n                name: `${fieldName}.root`,\n                error: foundError.root,\n            };\n        }\n        names.pop();\n    }\n    return {\n        name,\n    };\n}\n\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot) => {\n    updateFormState(formStateData);\n    const { name, ...formState } = formStateData;\n    return (isEmptyObject(formState) ||\n        Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n        Object.keys(formState).find((key) => _proxyFormState[key] ===\n            (!isRoot || VALIDATION_MODE.all)));\n};\n\nvar shouldSubscribeByName = (name, signalName, exact) => !name ||\n    !signalName ||\n    name === signalName ||\n    convertToArrayPayload(name).some((currentName) => currentName &&\n        (exact\n            ? currentName === signalName\n            : currentName.startsWith(signalName) ||\n                signalName.startsWith(currentName)));\n\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode) => {\n    if (mode.isOnAll) {\n        return false;\n    }\n    else if (!isSubmitted && mode.isOnTouch) {\n        return !(isTouched || isBlurEvent);\n    }\n    else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n        return !isBlurEvent;\n    }\n    else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n        return isBlurEvent;\n    }\n    return true;\n};\n\nvar unsetEmptyArray = (ref, name) => !compact(get(ref, name)).length && unset(ref, name);\n\nvar updateFieldArrayRootError = (errors, error, name) => {\n    const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n    set(fieldArrayErrors, 'root', error[name]);\n    set(errors, name, fieldArrayErrors);\n    return errors;\n};\n\nvar isMessage = (value) => isString(value);\n\nfunction getValidateError(result, ref, type = 'validate') {\n    if (isMessage(result) ||\n        (Array.isArray(result) && result.every(isMessage)) ||\n        (isBoolean(result) && !result)) {\n        return {\n            type,\n            message: isMessage(result) ? result : '',\n            ref,\n        };\n    }\n}\n\nvar getValueAndMessage = (validationData) => isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n    };\n\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray) => {\n    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount, } = field._f;\n    const inputValue = get(formValues, name);\n    if (!mount || disabledFieldNames.has(name)) {\n        return {};\n    }\n    const inputRef = refs ? refs[0] : ref;\n    const setCustomValidity = (message) => {\n        if (shouldUseNativeValidation && inputRef.reportValidity) {\n            inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n            inputRef.reportValidity();\n        }\n    };\n    const error = {};\n    const isRadio = isRadioInput(ref);\n    const isCheckBox = isCheckBoxInput(ref);\n    const isRadioOrCheckbox = isRadio || isCheckBox;\n    const isEmpty = ((valueAsNumber || isFileInput(ref)) &&\n        isUndefined(ref.value) &&\n        isUndefined(inputValue)) ||\n        (isHTMLElement(ref) && ref.value === '') ||\n        inputValue === '' ||\n        (Array.isArray(inputValue) && !inputValue.length);\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength) => {\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\n        error[name] = {\n            type: exceedMax ? maxType : minType,\n            message,\n            ref,\n            ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n        };\n    };\n    if (isFieldArray\n        ? !Array.isArray(inputValue) || !inputValue.length\n        : required &&\n            ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n                (isBoolean(inputValue) && !inputValue) ||\n                (isCheckBox && !getCheckboxValue(refs).isValid) ||\n                (isRadio && !getRadioValue(refs).isValid))) {\n        const { value, message } = isMessage(required)\n            ? { value: !!required, message: required }\n            : getValueAndMessage(required);\n        if (value) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.required,\n                message,\n                ref: inputRef,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n        let exceedMax;\n        let exceedMin;\n        const maxOutput = getValueAndMessage(max);\n        const minOutput = getValueAndMessage(min);\n        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n            const valueNumber = ref.valueAsNumber ||\n                (inputValue ? +inputValue : inputValue);\n            if (!isNullOrUndefined(maxOutput.value)) {\n                exceedMax = valueNumber > maxOutput.value;\n            }\n            if (!isNullOrUndefined(minOutput.value)) {\n                exceedMin = valueNumber < minOutput.value;\n            }\n        }\n        else {\n            const valueDate = ref.valueAsDate || new Date(inputValue);\n            const convertTimeToDate = (time) => new Date(new Date().toDateString() + ' ' + time);\n            const isTime = ref.type == 'time';\n            const isWeek = ref.type == 'week';\n            if (isString(maxOutput.value) && inputValue) {\n                exceedMax = isTime\n                    ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n                    : isWeek\n                        ? inputValue > maxOutput.value\n                        : valueDate > new Date(maxOutput.value);\n            }\n            if (isString(minOutput.value) && inputValue) {\n                exceedMin = isTime\n                    ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n                    : isWeek\n                        ? inputValue < minOutput.value\n                        : valueDate < new Date(minOutput.value);\n            }\n        }\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if ((maxLength || minLength) &&\n        !isEmpty &&\n        (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))) {\n        const maxLengthOutput = getValueAndMessage(maxLength);\n        const minLengthOutput = getValueAndMessage(minLength);\n        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) &&\n            inputValue.length > +maxLengthOutput.value;\n        const exceedMin = !isNullOrUndefined(minLengthOutput.value) &&\n            inputValue.length < +minLengthOutput.value;\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if (pattern && !isEmpty && isString(inputValue)) {\n        const { value: patternValue, message } = getValueAndMessage(pattern);\n        if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.pattern,\n                message,\n                ref,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (validate) {\n        if (isFunction(validate)) {\n            const result = await validate(inputValue, formValues);\n            const validateError = getValidateError(result, inputRef);\n            if (validateError) {\n                error[name] = {\n                    ...validateError,\n                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message),\n                };\n                if (!validateAllFieldCriteria) {\n                    setCustomValidity(validateError.message);\n                    return error;\n                }\n            }\n        }\n        else if (isObject(validate)) {\n            let validationResult = {};\n            for (const key in validate) {\n                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n                    break;\n                }\n                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n                if (validateError) {\n                    validationResult = {\n                        ...validateError,\n                        ...appendErrorsCurry(key, validateError.message),\n                    };\n                    setCustomValidity(validateError.message);\n                    if (validateAllFieldCriteria) {\n                        error[name] = validationResult;\n                    }\n                }\n            }\n            if (!isEmptyObject(validationResult)) {\n                error[name] = {\n                    ref: inputRef,\n                    ...validationResult,\n                };\n                if (!validateAllFieldCriteria) {\n                    return error;\n                }\n            }\n        }\n    }\n    setCustomValidity(true);\n    return error;\n};\n\nconst defaultOptions = {\n    mode: VALIDATION_MODE.onSubmit,\n    reValidateMode: VALIDATION_MODE.onChange,\n    shouldFocusError: true,\n};\nfunction createFormControl(props = {}) {\n    let _options = {\n        ...defaultOptions,\n        ...props,\n    };\n    let _formState = {\n        submitCount: 0,\n        isDirty: false,\n        isReady: false,\n        isLoading: isFunction(_options.defaultValues),\n        isValidating: false,\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        touchedFields: {},\n        dirtyFields: {},\n        validatingFields: {},\n        errors: _options.errors || {},\n        disabled: _options.disabled || false,\n    };\n    let _fields = {};\n    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values)\n        ? cloneObject(_options.defaultValues || _options.values) || {}\n        : {};\n    let _formValues = _options.shouldUnregister\n        ? {}\n        : cloneObject(_defaultValues);\n    let _state = {\n        action: false,\n        mount: false,\n        watch: false,\n    };\n    let _names = {\n        mount: new Set(),\n        disabled: new Set(),\n        unMount: new Set(),\n        array: new Set(),\n        watch: new Set(),\n    };\n    let delayErrorCallback;\n    let timer = 0;\n    const _proxyFormState = {\n        isDirty: false,\n        dirtyFields: false,\n        validatingFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false,\n    };\n    let _proxySubscribeFormState = {\n        ..._proxyFormState,\n    };\n    const _subjects = {\n        array: createSubject(),\n        state: createSubject(),\n    };\n    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n    const debounce = (callback) => (wait) => {\n        clearTimeout(timer);\n        timer = setTimeout(callback, wait);\n    };\n    const _setValid = async (shouldUpdateValid) => {\n        if (!_options.disabled &&\n            (_proxyFormState.isValid ||\n                _proxySubscribeFormState.isValid ||\n                shouldUpdateValid)) {\n            const isValid = _options.resolver\n                ? isEmptyObject((await _runSchema()).errors)\n                : await executeBuiltInValidation(_fields, true);\n            if (isValid !== _formState.isValid) {\n                _subjects.state.next({\n                    isValid,\n                });\n            }\n        }\n    };\n    const _updateIsValidating = (names, isValidating) => {\n        if (!_options.disabled &&\n            (_proxyFormState.isValidating ||\n                _proxyFormState.validatingFields ||\n                _proxySubscribeFormState.isValidating ||\n                _proxySubscribeFormState.validatingFields)) {\n            (names || Array.from(_names.mount)).forEach((name) => {\n                if (name) {\n                    isValidating\n                        ? set(_formState.validatingFields, name, isValidating)\n                        : unset(_formState.validatingFields, name);\n                }\n            });\n            _subjects.state.next({\n                validatingFields: _formState.validatingFields,\n                isValidating: !isEmptyObject(_formState.validatingFields),\n            });\n        }\n    };\n    const _setFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true) => {\n        if (args && method && !_options.disabled) {\n            _state.action = true;\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n                const fieldValues = method(get(_fields, name), args.argA, args.argB);\n                shouldSetValues && set(_fields, name, fieldValues);\n            }\n            if (shouldUpdateFieldsAndState &&\n                Array.isArray(get(_formState.errors, name))) {\n                const errors = method(get(_formState.errors, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.errors, name, errors);\n                unsetEmptyArray(_formState.errors, name);\n            }\n            if ((_proxyFormState.touchedFields ||\n                _proxySubscribeFormState.touchedFields) &&\n                shouldUpdateFieldsAndState &&\n                Array.isArray(get(_formState.touchedFields, name))) {\n                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n            }\n            if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n            }\n            _subjects.state.next({\n                name,\n                isDirty: _getDirty(name, values),\n                dirtyFields: _formState.dirtyFields,\n                errors: _formState.errors,\n                isValid: _formState.isValid,\n            });\n        }\n        else {\n            set(_formValues, name, values);\n        }\n    };\n    const updateErrors = (name, error) => {\n        set(_formState.errors, name, error);\n        _subjects.state.next({\n            errors: _formState.errors,\n        });\n    };\n    const _setErrors = (errors) => {\n        _formState.errors = errors;\n        _subjects.state.next({\n            errors: _formState.errors,\n            isValid: false,\n        });\n    };\n    const updateValidAndValue = (name, shouldSkipSetValueAs, value, ref) => {\n        const field = get(_fields, name);\n        if (field) {\n            const defaultValue = get(_formValues, name, isUndefined(value) ? get(_defaultValues, name) : value);\n            isUndefined(defaultValue) ||\n                (ref && ref.defaultChecked) ||\n                shouldSkipSetValueAs\n                ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f))\n                : setFieldValue(name, defaultValue);\n            _state.mount && _setValid();\n        }\n    };\n    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender) => {\n        let shouldUpdateField = false;\n        let isPreviousDirty = false;\n        const output = {\n            name,\n        };\n        if (!_options.disabled) {\n            if (!isBlurEvent || shouldDirty) {\n                if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n                    isPreviousDirty = _formState.isDirty;\n                    _formState.isDirty = output.isDirty = _getDirty();\n                    shouldUpdateField = isPreviousDirty !== output.isDirty;\n                }\n                const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n                isPreviousDirty = !!get(_formState.dirtyFields, name);\n                isCurrentFieldPristine\n                    ? unset(_formState.dirtyFields, name)\n                    : set(_formState.dirtyFields, name, true);\n                output.dirtyFields = _formState.dirtyFields;\n                shouldUpdateField =\n                    shouldUpdateField ||\n                        ((_proxyFormState.dirtyFields ||\n                            _proxySubscribeFormState.dirtyFields) &&\n                            isPreviousDirty !== !isCurrentFieldPristine);\n            }\n            if (isBlurEvent) {\n                const isPreviousFieldTouched = get(_formState.touchedFields, name);\n                if (!isPreviousFieldTouched) {\n                    set(_formState.touchedFields, name, isBlurEvent);\n                    output.touchedFields = _formState.touchedFields;\n                    shouldUpdateField =\n                        shouldUpdateField ||\n                            ((_proxyFormState.touchedFields ||\n                                _proxySubscribeFormState.touchedFields) &&\n                                isPreviousFieldTouched !== isBlurEvent);\n                }\n            }\n            shouldUpdateField && shouldRender && _subjects.state.next(output);\n        }\n        return shouldUpdateField ? output : {};\n    };\n    const shouldRenderByError = (name, isValid, error, fieldState) => {\n        const previousFieldError = get(_formState.errors, name);\n        const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n            isBoolean(isValid) &&\n            _formState.isValid !== isValid;\n        if (_options.delayError && error) {\n            delayErrorCallback = debounce(() => updateErrors(name, error));\n            delayErrorCallback(_options.delayError);\n        }\n        else {\n            clearTimeout(timer);\n            delayErrorCallback = null;\n            error\n                ? set(_formState.errors, name, error)\n                : unset(_formState.errors, name);\n        }\n        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n            !isEmptyObject(fieldState) ||\n            shouldUpdateValid) {\n            const updatedFormState = {\n                ...fieldState,\n                ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n                errors: _formState.errors,\n                name,\n            };\n            _formState = {\n                ..._formState,\n                ...updatedFormState,\n            };\n            _subjects.state.next(updatedFormState);\n        }\n    };\n    const _runSchema = async (name) => {\n        _updateIsValidating(name, true);\n        const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n        _updateIsValidating(name);\n        return result;\n    };\n    const executeSchemaAndUpdateState = async (names) => {\n        const { errors } = await _runSchema(names);\n        if (names) {\n            for (const name of names) {\n                const error = get(errors, name);\n                error\n                    ? set(_formState.errors, name, error)\n                    : unset(_formState.errors, name);\n            }\n        }\n        else {\n            _formState.errors = errors;\n        }\n        return errors;\n    };\n    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n        valid: true,\n    }) => {\n        for (const name in fields) {\n            const field = fields[name];\n            if (field) {\n                const { _f, ...fieldValue } = field;\n                if (_f) {\n                    const isFieldArrayRoot = _names.array.has(_f.name);\n                    const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([name], true);\n                    }\n                    const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([name]);\n                    }\n                    if (fieldError[_f.name]) {\n                        context.valid = false;\n                        if (shouldOnlyCheckValid) {\n                            break;\n                        }\n                    }\n                    !shouldOnlyCheckValid &&\n                        (get(fieldError, _f.name)\n                            ? isFieldArrayRoot\n                                ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name)\n                                : set(_formState.errors, _f.name, fieldError[_f.name])\n                            : unset(_formState.errors, _f.name));\n                }\n                !isEmptyObject(fieldValue) &&\n                    (await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context));\n            }\n        }\n        return context.valid;\n    };\n    const _removeUnmounted = () => {\n        for (const name of _names.unMount) {\n            const field = get(_fields, name);\n            field &&\n                (field._f.refs\n                    ? field._f.refs.every((ref) => !live(ref))\n                    : !live(field._f.ref)) &&\n                unregister(name);\n        }\n        _names.unMount = new Set();\n    };\n    const _getDirty = (name, data) => !_options.disabled &&\n        (name && data && set(_formValues, name, data),\n            !deepEqual(getValues(), _defaultValues));\n    const _getWatch = (names, defaultValue, isGlobal) => generateWatchOutput(names, _names, {\n        ...(_state.mount\n            ? _formValues\n            : isUndefined(defaultValue)\n                ? _defaultValues\n                : isString(names)\n                    ? { [names]: defaultValue }\n                    : defaultValue),\n    }, isGlobal, defaultValue);\n    const _getFieldArray = (name) => compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n    const setFieldValue = (name, value, options = {}) => {\n        const field = get(_fields, name);\n        let fieldValue = value;\n        if (field) {\n            const fieldReference = field._f;\n            if (fieldReference) {\n                !fieldReference.disabled &&\n                    set(_formValues, name, getFieldValueAs(value, fieldReference));\n                fieldValue =\n                    isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n                        ? ''\n                        : value;\n                if (isMultipleSelect(fieldReference.ref)) {\n                    [...fieldReference.ref.options].forEach((optionRef) => (optionRef.selected = fieldValue.includes(optionRef.value)));\n                }\n                else if (fieldReference.refs) {\n                    if (isCheckBoxInput(fieldReference.ref)) {\n                        fieldReference.refs.forEach((checkboxRef) => {\n                            if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                                if (Array.isArray(fieldValue)) {\n                                    checkboxRef.checked = !!fieldValue.find((data) => data === checkboxRef.value);\n                                }\n                                else {\n                                    checkboxRef.checked =\n                                        fieldValue === checkboxRef.value || !!fieldValue;\n                                }\n                            }\n                        });\n                    }\n                    else {\n                        fieldReference.refs.forEach((radioRef) => (radioRef.checked = radioRef.value === fieldValue));\n                    }\n                }\n                else if (isFileInput(fieldReference.ref)) {\n                    fieldReference.ref.value = '';\n                }\n                else {\n                    fieldReference.ref.value = fieldValue;\n                    if (!fieldReference.ref.type) {\n                        _subjects.state.next({\n                            name,\n                            values: cloneObject(_formValues),\n                        });\n                    }\n                }\n            }\n        }\n        (options.shouldDirty || options.shouldTouch) &&\n            updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n        options.shouldValidate && trigger(name);\n    };\n    const setValues = (name, value, options) => {\n        for (const fieldKey in value) {\n            if (!value.hasOwnProperty(fieldKey)) {\n                return;\n            }\n            const fieldValue = value[fieldKey];\n            const fieldName = name + '.' + fieldKey;\n            const field = get(_fields, fieldName);\n            (_names.array.has(name) ||\n                isObject(fieldValue) ||\n                (field && !field._f)) &&\n                !isDateObject(fieldValue)\n                ? setValues(fieldName, fieldValue, options)\n                : setFieldValue(fieldName, fieldValue, options);\n        }\n    };\n    const setValue = (name, value, options = {}) => {\n        const field = get(_fields, name);\n        const isFieldArray = _names.array.has(name);\n        const cloneValue = cloneObject(value);\n        set(_formValues, name, cloneValue);\n        if (isFieldArray) {\n            _subjects.array.next({\n                name,\n                values: cloneObject(_formValues),\n            });\n            if ((_proxyFormState.isDirty ||\n                _proxyFormState.dirtyFields ||\n                _proxySubscribeFormState.isDirty ||\n                _proxySubscribeFormState.dirtyFields) &&\n                options.shouldDirty) {\n                _subjects.state.next({\n                    name,\n                    dirtyFields: getDirtyFields(_defaultValues, _formValues),\n                    isDirty: _getDirty(name, cloneValue),\n                });\n            }\n        }\n        else {\n            field && !field._f && !isNullOrUndefined(cloneValue)\n                ? setValues(name, cloneValue, options)\n                : setFieldValue(name, cloneValue, options);\n        }\n        isWatched(name, _names) && _subjects.state.next({ ..._formState });\n        _subjects.state.next({\n            name: _state.mount ? name : undefined,\n            values: cloneObject(_formValues),\n        });\n    };\n    const onChange = async (event) => {\n        _state.mount = true;\n        const target = event.target;\n        let name = target.name;\n        let isFieldValueUpdated = true;\n        const field = get(_fields, name);\n        const _updateIsFieldValueUpdated = (fieldValue) => {\n            isFieldValueUpdated =\n                Number.isNaN(fieldValue) ||\n                    (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n                    deepEqual(fieldValue, get(_formValues, name, fieldValue));\n        };\n        const validationModeBeforeSubmit = getValidationModes(_options.mode);\n        const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n        if (field) {\n            let error;\n            let isValid;\n            const fieldValue = target.type\n                ? getFieldValue(field._f)\n                : getEventValue(event);\n            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n            const shouldSkipValidation = (!hasValidation(field._f) &&\n                !_options.resolver &&\n                !get(_formState.errors, name) &&\n                !field._f.deps) ||\n                skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n            const watched = isWatched(name, _names, isBlurEvent);\n            set(_formValues, name, fieldValue);\n            if (isBlurEvent) {\n                field._f.onBlur && field._f.onBlur(event);\n                delayErrorCallback && delayErrorCallback(0);\n            }\n            else if (field._f.onChange) {\n                field._f.onChange(event);\n            }\n            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n            const shouldRender = !isEmptyObject(fieldState) || watched;\n            !isBlurEvent &&\n                _subjects.state.next({\n                    name,\n                    type: event.type,\n                    values: cloneObject(_formValues),\n                });\n            if (shouldSkipValidation) {\n                if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                    if (_options.mode === 'onBlur') {\n                        if (isBlurEvent) {\n                            _setValid();\n                        }\n                    }\n                    else if (!isBlurEvent) {\n                        _setValid();\n                    }\n                }\n                return (shouldRender &&\n                    _subjects.state.next({ name, ...(watched ? {} : fieldState) }));\n            }\n            !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n            if (_options.resolver) {\n                const { errors } = await _runSchema([name]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n                    error = errorLookupResult.error;\n                    name = errorLookupResult.name;\n                    isValid = isEmptyObject(errors);\n                }\n            }\n            else {\n                _updateIsValidating([name], true);\n                error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n                _updateIsValidating([name]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    if (error) {\n                        isValid = false;\n                    }\n                    else if (_proxyFormState.isValid ||\n                        _proxySubscribeFormState.isValid) {\n                        isValid = await executeBuiltInValidation(_fields, true);\n                    }\n                }\n            }\n            if (isFieldValueUpdated) {\n                field._f.deps &&\n                    trigger(field._f.deps);\n                shouldRenderByError(name, isValid, error, fieldState);\n            }\n        }\n    };\n    const _focusInput = (ref, key) => {\n        if (get(_formState.errors, key) && ref.focus) {\n            ref.focus();\n            return 1;\n        }\n        return;\n    };\n    const trigger = async (name, options = {}) => {\n        let isValid;\n        let validationResult;\n        const fieldNames = convertToArrayPayload(name);\n        if (_options.resolver) {\n            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n            isValid = isEmptyObject(errors);\n            validationResult = name\n                ? !fieldNames.some((name) => get(errors, name))\n                : isValid;\n        }\n        else if (name) {\n            validationResult = (await Promise.all(fieldNames.map(async (fieldName) => {\n                const field = get(_fields, fieldName);\n                return await executeBuiltInValidation(field && field._f ? { [fieldName]: field } : field);\n            }))).every(Boolean);\n            !(!validationResult && !_formState.isValid) && _setValid();\n        }\n        else {\n            validationResult = isValid = await executeBuiltInValidation(_fields);\n        }\n        _subjects.state.next({\n            ...(!isString(name) ||\n                ((_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n                    isValid !== _formState.isValid)\n                ? {}\n                : { name }),\n            ...(_options.resolver || !name ? { isValid } : {}),\n            errors: _formState.errors,\n        });\n        options.shouldFocus &&\n            !validationResult &&\n            iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n        return validationResult;\n    };\n    const getValues = (fieldNames) => {\n        const values = {\n            ...(_state.mount ? _formValues : _defaultValues),\n        };\n        return isUndefined(fieldNames)\n            ? values\n            : isString(fieldNames)\n                ? get(values, fieldNames)\n                : fieldNames.map((name) => get(values, name));\n    };\n    const getFieldState = (name, formState) => ({\n        invalid: !!get((formState || _formState).errors, name),\n        isDirty: !!get((formState || _formState).dirtyFields, name),\n        error: get((formState || _formState).errors, name),\n        isValidating: !!get(_formState.validatingFields, name),\n        isTouched: !!get((formState || _formState).touchedFields, name),\n    });\n    const clearErrors = (name) => {\n        name &&\n            convertToArrayPayload(name).forEach((inputName) => unset(_formState.errors, inputName));\n        _subjects.state.next({\n            errors: name ? _formState.errors : {},\n        });\n    };\n    const setError = (name, error, options) => {\n        const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n        const currentError = get(_formState.errors, name) || {};\n        // Don't override existing error messages elsewhere in the object tree.\n        const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n        set(_formState.errors, name, {\n            ...restOfErrorTree,\n            ...error,\n            ref,\n        });\n        _subjects.state.next({\n            name,\n            errors: _formState.errors,\n            isValid: false,\n        });\n        options && options.shouldFocus && ref && ref.focus && ref.focus();\n    };\n    const watch = (name, defaultValue) => isFunction(name)\n        ? _subjects.state.subscribe({\n            next: (payload) => name(_getWatch(undefined, defaultValue), payload),\n        })\n        : _getWatch(name, defaultValue, true);\n    const _subscribe = (props) => _subjects.state.subscribe({\n        next: (formState) => {\n            if (shouldSubscribeByName(props.name, formState.name, props.exact) &&\n                shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {\n                props.callback({\n                    values: { ..._formValues },\n                    ..._formState,\n                    ...formState,\n                });\n            }\n        },\n    }).unsubscribe;\n    const subscribe = (props) => {\n        _state.mount = true;\n        _proxySubscribeFormState = {\n            ..._proxySubscribeFormState,\n            ...props.formState,\n        };\n        return _subscribe({\n            ...props,\n            formState: _proxySubscribeFormState,\n        });\n    };\n    const unregister = (name, options = {}) => {\n        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n            _names.mount.delete(fieldName);\n            _names.array.delete(fieldName);\n            if (!options.keepValue) {\n                unset(_fields, fieldName);\n                unset(_formValues, fieldName);\n            }\n            !options.keepError && unset(_formState.errors, fieldName);\n            !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n            !options.keepTouched && unset(_formState.touchedFields, fieldName);\n            !options.keepIsValidating &&\n                unset(_formState.validatingFields, fieldName);\n            !_options.shouldUnregister &&\n                !options.keepDefaultValue &&\n                unset(_defaultValues, fieldName);\n        }\n        _subjects.state.next({\n            values: cloneObject(_formValues),\n        });\n        _subjects.state.next({\n            ..._formState,\n            ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n        });\n        !options.keepIsValid && _setValid();\n    };\n    const _setDisabledField = ({ disabled, name, }) => {\n        if ((isBoolean(disabled) && _state.mount) ||\n            !!disabled ||\n            _names.disabled.has(name)) {\n            disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n        }\n    };\n    const register = (name, options = {}) => {\n        let field = get(_fields, name);\n        const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n        set(_fields, name, {\n            ...(field || {}),\n            _f: {\n                ...(field && field._f ? field._f : { ref: { name } }),\n                name,\n                mount: true,\n                ...options,\n            },\n        });\n        _names.mount.add(name);\n        if (field) {\n            _setDisabledField({\n                disabled: isBoolean(options.disabled)\n                    ? options.disabled\n                    : _options.disabled,\n                name,\n            });\n        }\n        else {\n            updateValidAndValue(name, true, options.value);\n        }\n        return {\n            ...(disabledIsDefined\n                ? { disabled: options.disabled || _options.disabled }\n                : {}),\n            ...(_options.progressive\n                ? {\n                    required: !!options.required,\n                    min: getRuleValue(options.min),\n                    max: getRuleValue(options.max),\n                    minLength: getRuleValue(options.minLength),\n                    maxLength: getRuleValue(options.maxLength),\n                    pattern: getRuleValue(options.pattern),\n                }\n                : {}),\n            name,\n            onChange,\n            onBlur: onChange,\n            ref: (ref) => {\n                if (ref) {\n                    register(name, options);\n                    field = get(_fields, name);\n                    const fieldRef = isUndefined(ref.value)\n                        ? ref.querySelectorAll\n                            ? ref.querySelectorAll('input,select,textarea')[0] || ref\n                            : ref\n                        : ref;\n                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n                    const refs = field._f.refs || [];\n                    if (radioOrCheckbox\n                        ? refs.find((option) => option === fieldRef)\n                        : fieldRef === field._f.ref) {\n                        return;\n                    }\n                    set(_fields, name, {\n                        _f: {\n                            ...field._f,\n                            ...(radioOrCheckbox\n                                ? {\n                                    refs: [\n                                        ...refs.filter(live),\n                                        fieldRef,\n                                        ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                                    ],\n                                    ref: { type: fieldRef.type, name },\n                                }\n                                : { ref: fieldRef }),\n                        },\n                    });\n                    updateValidAndValue(name, false, undefined, fieldRef);\n                }\n                else {\n                    field = get(_fields, name, {});\n                    if (field._f) {\n                        field._f.mount = false;\n                    }\n                    (_options.shouldUnregister || options.shouldUnregister) &&\n                        !(isNameInFieldArray(_names.array, name) && _state.action) &&\n                        _names.unMount.add(name);\n                }\n            },\n        };\n    };\n    const _focusError = () => _options.shouldFocusError &&\n        iterateFieldsByAction(_fields, _focusInput, _names.mount);\n    const _disableForm = (disabled) => {\n        if (isBoolean(disabled)) {\n            _subjects.state.next({ disabled });\n            iterateFieldsByAction(_fields, (ref, name) => {\n                const currentField = get(_fields, name);\n                if (currentField) {\n                    ref.disabled = currentField._f.disabled || disabled;\n                    if (Array.isArray(currentField._f.refs)) {\n                        currentField._f.refs.forEach((inputRef) => {\n                            inputRef.disabled = currentField._f.disabled || disabled;\n                        });\n                    }\n                }\n            }, 0, false);\n        }\n    };\n    const handleSubmit = (onValid, onInvalid) => async (e) => {\n        let onValidError = undefined;\n        if (e) {\n            e.preventDefault && e.preventDefault();\n            e.persist &&\n                e.persist();\n        }\n        let fieldValues = cloneObject(_formValues);\n        _subjects.state.next({\n            isSubmitting: true,\n        });\n        if (_options.resolver) {\n            const { errors, values } = await _runSchema();\n            _formState.errors = errors;\n            fieldValues = cloneObject(values);\n        }\n        else {\n            await executeBuiltInValidation(_fields);\n        }\n        if (_names.disabled.size) {\n            for (const name of _names.disabled) {\n                unset(fieldValues, name);\n            }\n        }\n        unset(_formState.errors, 'root');\n        if (isEmptyObject(_formState.errors)) {\n            _subjects.state.next({\n                errors: {},\n            });\n            try {\n                await onValid(fieldValues, e);\n            }\n            catch (error) {\n                onValidError = error;\n            }\n        }\n        else {\n            if (onInvalid) {\n                await onInvalid({ ..._formState.errors }, e);\n            }\n            _focusError();\n            setTimeout(_focusError);\n        }\n        _subjects.state.next({\n            isSubmitted: true,\n            isSubmitting: false,\n            isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n            submitCount: _formState.submitCount + 1,\n            errors: _formState.errors,\n        });\n        if (onValidError) {\n            throw onValidError;\n        }\n    };\n    const resetField = (name, options = {}) => {\n        if (get(_fields, name)) {\n            if (isUndefined(options.defaultValue)) {\n                setValue(name, cloneObject(get(_defaultValues, name)));\n            }\n            else {\n                setValue(name, options.defaultValue);\n                set(_defaultValues, name, cloneObject(options.defaultValue));\n            }\n            if (!options.keepTouched) {\n                unset(_formState.touchedFields, name);\n            }\n            if (!options.keepDirty) {\n                unset(_formState.dirtyFields, name);\n                _formState.isDirty = options.defaultValue\n                    ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n                    : _getDirty();\n            }\n            if (!options.keepError) {\n                unset(_formState.errors, name);\n                _proxyFormState.isValid && _setValid();\n            }\n            _subjects.state.next({ ..._formState });\n        }\n    };\n    const _reset = (formValues, keepStateOptions = {}) => {\n        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n        const cloneUpdatedValues = cloneObject(updatedValues);\n        const isEmptyResetValues = isEmptyObject(formValues);\n        const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n        if (!keepStateOptions.keepDefaultValues) {\n            _defaultValues = updatedValues;\n        }\n        if (!keepStateOptions.keepValues) {\n            if (keepStateOptions.keepDirtyValues) {\n                const fieldsToCheck = new Set([\n                    ..._names.mount,\n                    ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n                ]);\n                for (const fieldName of Array.from(fieldsToCheck)) {\n                    get(_formState.dirtyFields, fieldName)\n                        ? set(values, fieldName, get(_formValues, fieldName))\n                        : setValue(fieldName, get(values, fieldName));\n                }\n            }\n            else {\n                if (isWeb && isUndefined(formValues)) {\n                    for (const name of _names.mount) {\n                        const field = get(_fields, name);\n                        if (field && field._f) {\n                            const fieldReference = Array.isArray(field._f.refs)\n                                ? field._f.refs[0]\n                                : field._f.ref;\n                            if (isHTMLElement(fieldReference)) {\n                                const form = fieldReference.closest('form');\n                                if (form) {\n                                    form.reset();\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n                if (keepStateOptions.keepFieldsRef) {\n                    for (const fieldName of _names.mount) {\n                        setValue(fieldName, get(values, fieldName));\n                    }\n                }\n                else {\n                    _fields = {};\n                }\n            }\n            _formValues = _options.shouldUnregister\n                ? keepStateOptions.keepDefaultValues\n                    ? cloneObject(_defaultValues)\n                    : {}\n                : cloneObject(values);\n            _subjects.array.next({\n                values: { ...values },\n            });\n            _subjects.state.next({\n                values: { ...values },\n            });\n        }\n        _names = {\n            mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n            unMount: new Set(),\n            array: new Set(),\n            disabled: new Set(),\n            watch: new Set(),\n            watchAll: false,\n            focus: '',\n        };\n        _state.mount =\n            !_proxyFormState.isValid ||\n                !!keepStateOptions.keepIsValid ||\n                !!keepStateOptions.keepDirtyValues;\n        _state.watch = !!_options.shouldUnregister;\n        _subjects.state.next({\n            submitCount: keepStateOptions.keepSubmitCount\n                ? _formState.submitCount\n                : 0,\n            isDirty: isEmptyResetValues\n                ? false\n                : keepStateOptions.keepDirty\n                    ? _formState.isDirty\n                    : !!(keepStateOptions.keepDefaultValues &&\n                        !deepEqual(formValues, _defaultValues)),\n            isSubmitted: keepStateOptions.keepIsSubmitted\n                ? _formState.isSubmitted\n                : false,\n            dirtyFields: isEmptyResetValues\n                ? {}\n                : keepStateOptions.keepDirtyValues\n                    ? keepStateOptions.keepDefaultValues && _formValues\n                        ? getDirtyFields(_defaultValues, _formValues)\n                        : _formState.dirtyFields\n                    : keepStateOptions.keepDefaultValues && formValues\n                        ? getDirtyFields(_defaultValues, formValues)\n                        : keepStateOptions.keepDirty\n                            ? _formState.dirtyFields\n                            : {},\n            touchedFields: keepStateOptions.keepTouched\n                ? _formState.touchedFields\n                : {},\n            errors: keepStateOptions.keepErrors ? _formState.errors : {},\n            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n                ? _formState.isSubmitSuccessful\n                : false,\n            isSubmitting: false,\n        });\n    };\n    const reset = (formValues, keepStateOptions) => _reset(isFunction(formValues)\n        ? formValues(_formValues)\n        : formValues, keepStateOptions);\n    const setFocus = (name, options = {}) => {\n        const field = get(_fields, name);\n        const fieldReference = field && field._f;\n        if (fieldReference) {\n            const fieldRef = fieldReference.refs\n                ? fieldReference.refs[0]\n                : fieldReference.ref;\n            if (fieldRef.focus) {\n                fieldRef.focus();\n                options.shouldSelect &&\n                    isFunction(fieldRef.select) &&\n                    fieldRef.select();\n            }\n        }\n    };\n    const _setFormState = (updatedFormState) => {\n        _formState = {\n            ..._formState,\n            ...updatedFormState,\n        };\n    };\n    const _resetDefaultValues = () => isFunction(_options.defaultValues) &&\n        _options.defaultValues().then((values) => {\n            reset(values, _options.resetOptions);\n            _subjects.state.next({\n                isLoading: false,\n            });\n        });\n    const methods = {\n        control: {\n            register,\n            unregister,\n            getFieldState,\n            handleSubmit,\n            setError,\n            _subscribe,\n            _runSchema,\n            _focusError,\n            _getWatch,\n            _getDirty,\n            _setValid,\n            _setFieldArray,\n            _setDisabledField,\n            _setErrors,\n            _getFieldArray,\n            _reset,\n            _resetDefaultValues,\n            _removeUnmounted,\n            _disableForm,\n            _subjects,\n            _proxyFormState,\n            get _fields() {\n                return _fields;\n            },\n            get _formValues() {\n                return _formValues;\n            },\n            get _state() {\n                return _state;\n            },\n            set _state(value) {\n                _state = value;\n            },\n            get _defaultValues() {\n                return _defaultValues;\n            },\n            get _names() {\n                return _names;\n            },\n            set _names(value) {\n                _names = value;\n            },\n            get _formState() {\n                return _formState;\n            },\n            get _options() {\n                return _options;\n            },\n            set _options(value) {\n                _options = {\n                    ..._options,\n                    ...value,\n                };\n            },\n        },\n        subscribe,\n        trigger,\n        register,\n        handleSubmit,\n        watch,\n        setValue,\n        getValues,\n        reset,\n        resetField,\n        clearErrors,\n        unregister,\n        setError,\n        setFocus,\n        getFieldState,\n    };\n    return {\n        ...methods,\n        formControl: methods,\n    };\n}\n\nvar generateId = () => {\n    if (typeof crypto !== 'undefined' && crypto.randomUUID) {\n        return crypto.randomUUID();\n    }\n    const d = typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n        const r = (Math.random() * 16 + d) % 16 | 0;\n        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n    });\n};\n\nvar getFocusFieldName = (name, index, options = {}) => options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n        `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n\nvar appendAt = (data, value) => [\n    ...data,\n    ...convertToArrayPayload(value),\n];\n\nvar fillEmptyArray = (value) => Array.isArray(value) ? value.map(() => undefined) : undefined;\n\nfunction insert(data, index, value) {\n    return [\n        ...data.slice(0, index),\n        ...convertToArrayPayload(value),\n        ...data.slice(index),\n    ];\n}\n\nvar moveArrayAt = (data, from, to) => {\n    if (!Array.isArray(data)) {\n        return [];\n    }\n    if (isUndefined(data[to])) {\n        data[to] = undefined;\n    }\n    data.splice(to, 0, data.splice(from, 1)[0]);\n    return data;\n};\n\nvar prependAt = (data, value) => [\n    ...convertToArrayPayload(value),\n    ...convertToArrayPayload(data),\n];\n\nfunction removeAtIndexes(data, indexes) {\n    let i = 0;\n    const temp = [...data];\n    for (const index of indexes) {\n        temp.splice(index - i, 1);\n        i++;\n    }\n    return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index) => isUndefined(index)\n    ? []\n    : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b) => a - b));\n\nvar swapArrayAt = (data, indexA, indexB) => {\n    [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n\nvar updateAt = (fieldValues, index, value) => {\n    fieldValues[index] = value;\n    return fieldValues;\n};\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFieldArray(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, keyName = 'id', shouldUnregister, rules, } = props;\n    const [fields, setFields] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getFieldArray(name));\n    const ids = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control._getFieldArray(name).map(generateId));\n    const _fieldIds = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fields);\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    const _actioned = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    _name.current = name;\n    _fieldIds.current = fields;\n    control._names.array.add(name);\n    rules &&\n        control.register(name, rules);\n    useIsomorphicLayoutEffect(() => control._subjects.array.subscribe({\n        next: ({ values, name: fieldArrayName, }) => {\n            if (fieldArrayName === _name.current || !fieldArrayName) {\n                const fieldValues = get(values, _name.current);\n                if (Array.isArray(fieldValues)) {\n                    setFields(fieldValues);\n                    ids.current = fieldValues.map(generateId);\n                }\n            }\n        },\n    }).unsubscribe, [control]);\n    const updateValues = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((updatedFieldArrayValues) => {\n        _actioned.current = true;\n        control._setFieldArray(name, updatedFieldArrayValues);\n    }, [control, name]);\n    const append = (value, options) => {\n        const appendValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n        ids.current = appendAt(ids.current, appendValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n            argA: fillEmptyArray(value),\n        });\n    };\n    const prepend = (value, options) => {\n        const prependValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n        control._names.focus = getFocusFieldName(name, 0, options);\n        ids.current = prependAt(ids.current, prependValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n            argA: fillEmptyArray(value),\n        });\n    };\n    const remove = (index) => {\n        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n        ids.current = removeArrayAt(ids.current, index);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        !Array.isArray(get(control._fields, name)) &&\n            set(control._fields, name, undefined);\n        control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n            argA: index,\n        });\n    };\n    const insert$1 = (index, value, options) => {\n        const insertValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n        control._names.focus = getFocusFieldName(name, index, options);\n        ids.current = insert(ids.current, index, insertValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, insert, {\n            argA: index,\n            argB: fillEmptyArray(value),\n        });\n    };\n    const swap = (indexA, indexB) => {\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n        swapArrayAt(ids.current, indexA, indexB);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n            argA: indexA,\n            argB: indexB,\n        }, false);\n    };\n    const move = (from, to) => {\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        moveArrayAt(updatedFieldArrayValues, from, to);\n        moveArrayAt(ids.current, from, to);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n            argA: from,\n            argB: to,\n        }, false);\n    };\n    const update = (index, value) => {\n        const updateValue = cloneObject(value);\n        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n        ids.current = [...updatedFieldArrayValues].map((item, i) => !item || i === index ? generateId() : ids.current[i]);\n        updateValues(updatedFieldArrayValues);\n        setFields([...updatedFieldArrayValues]);\n        control._setFieldArray(name, updatedFieldArrayValues, updateAt, {\n            argA: index,\n            argB: updateValue,\n        }, true, false);\n    };\n    const replace = (value) => {\n        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n        ids.current = updatedFieldArrayValues.map(generateId);\n        updateValues([...updatedFieldArrayValues]);\n        setFields([...updatedFieldArrayValues]);\n        control._setFieldArray(name, [...updatedFieldArrayValues], (data) => data, {}, true, false);\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        control._state.action = false;\n        isWatched(name, control._names) &&\n            control._subjects.state.next({\n                ...control._formState,\n            });\n        if (_actioned.current &&\n            (!getValidationModes(control._options.mode).isOnSubmit ||\n                control._formState.isSubmitted) &&\n            !getValidationModes(control._options.reValidateMode).isOnSubmit) {\n            if (control._options.resolver) {\n                control._runSchema([name]).then((result) => {\n                    const error = get(result.errors, name);\n                    const existingError = get(control._formState.errors, name);\n                    if (existingError\n                        ? (!error && existingError.type) ||\n                            (error &&\n                                (existingError.type !== error.type ||\n                                    existingError.message !== error.message))\n                        : error && error.type) {\n                        error\n                            ? set(control._formState.errors, name, error)\n                            : unset(control._formState.errors, name);\n                        control._subjects.state.next({\n                            errors: control._formState.errors,\n                        });\n                    }\n                });\n            }\n            else {\n                const field = get(control._fields, name);\n                if (field &&\n                    field._f &&\n                    !(getValidationModes(control._options.reValidateMode).isOnSubmit &&\n                        getValidationModes(control._options.mode).isOnSubmit)) {\n                    validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error) => !isEmptyObject(error) &&\n                        control._subjects.state.next({\n                            errors: updateFieldArrayRootError(control._formState.errors, error, name),\n                        }));\n                }\n            }\n        }\n        control._subjects.state.next({\n            name,\n            values: cloneObject(control._formValues),\n        });\n        control._names.focus &&\n            iterateFieldsByAction(control._fields, (ref, key) => {\n                if (control._names.focus &&\n                    key.startsWith(control._names.focus) &&\n                    ref.focus) {\n                    ref.focus();\n                    return 1;\n                }\n                return;\n            });\n        control._names.focus = '';\n        control._setValid();\n        _actioned.current = false;\n    }, [fields, name, control]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        !get(control._formValues, name) && control._setFieldArray(name);\n        return () => {\n            const updateMounted = (name, value) => {\n                const field = get(control._fields, name);\n                if (field && field._f) {\n                    field._f.mount = value;\n                }\n            };\n            control._options.shouldUnregister || shouldUnregister\n                ? control.unregister(name)\n                : updateMounted(name, false);\n        };\n    }, [name, control, keyName, shouldUnregister]);\n    return {\n        swap: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(swap, [updateValues, name, control]),\n        move: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(move, [updateValues, name, control]),\n        prepend: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(prepend, [updateValues, name, control]),\n        append: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(append, [updateValues, name, control]),\n        remove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(remove, [updateValues, name, control]),\n        insert: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(insert$1, [updateValues, name, control]),\n        update: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(update, [updateValues, name, control]),\n        replace: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(replace, [updateValues, name, control]),\n        fields: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => fields.map((field, index) => ({\n            ...field,\n            [keyName]: ids.current[index] || generateId(),\n        })), [fields, keyName]),\n    };\n}\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useForm(props = {}) {\n    const _formControl = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const _values = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        isDirty: false,\n        isValidating: false,\n        isLoading: isFunction(props.defaultValues),\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        submitCount: 0,\n        dirtyFields: {},\n        touchedFields: {},\n        validatingFields: {},\n        errors: props.errors || {},\n        disabled: props.disabled || false,\n        isReady: false,\n        defaultValues: isFunction(props.defaultValues)\n            ? undefined\n            : props.defaultValues,\n    });\n    if (!_formControl.current) {\n        if (props.formControl) {\n            _formControl.current = {\n                ...props.formControl,\n                formState,\n            };\n            if (props.defaultValues && !isFunction(props.defaultValues)) {\n                props.formControl.reset(props.defaultValues, props.resetOptions);\n            }\n        }\n        else {\n            const { formControl, ...rest } = createFormControl(props);\n            _formControl.current = {\n                ...rest,\n                formState,\n            };\n        }\n    }\n    const control = _formControl.current.control;\n    control._options = props;\n    useIsomorphicLayoutEffect(() => {\n        const sub = control._subscribe({\n            formState: control._proxyFormState,\n            callback: () => updateFormState({ ...control._formState }),\n            reRenderRoot: true,\n        });\n        updateFormState((data) => ({\n            ...data,\n            isReady: true,\n        }));\n        control._formState.isReady = true;\n        return sub;\n    }, [control]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => control._disableForm(props.disabled), [control, props.disabled]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (props.mode) {\n            control._options.mode = props.mode;\n        }\n        if (props.reValidateMode) {\n            control._options.reValidateMode = props.reValidateMode;\n        }\n    }, [control, props.mode, props.reValidateMode]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (props.errors) {\n            control._setErrors(props.errors);\n            control._focusError();\n        }\n    }, [control, props.errors]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        props.shouldUnregister &&\n            control._subjects.state.next({\n                values: control._getWatch(),\n            });\n    }, [control, props.shouldUnregister]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (control._proxyFormState.isDirty) {\n            const isDirty = control._getDirty();\n            if (isDirty !== formState.isDirty) {\n                control._subjects.state.next({\n                    isDirty,\n                });\n            }\n        }\n    }, [control, formState.isDirty]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (props.values && !deepEqual(props.values, _values.current)) {\n            control._reset(props.values, {\n                keepFieldsRef: true,\n                ...control._options.resetOptions,\n            });\n            _values.current = props.values;\n            updateFormState((state) => ({ ...state }));\n        }\n        else {\n            control._resetDefaultValues();\n        }\n    }, [control, props.values]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (!control._state.mount) {\n            control._setValid();\n            control._state.mount = true;\n        }\n        if (control._state.watch) {\n            control._state.watch = false;\n            control._subjects.state.next({ ...control._formState });\n        }\n        control._removeUnmounted();\n    });\n    _formControl.current.formState = getProxyFormState(formState, control);\n    return _formControl.current;\n}\n\n\n//# sourceMappingURL=index.esm.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\n");

/***/ })

};
;
{"version": 3, "file": "extractSchema.js", "sources": ["../../../../src/_internal/cli/threads/extractSchema.ts"], "sourcesContent": ["import {isMainThread, parentPort, workerData as _workerData} from 'node:worker_threads'\n\nimport {extractSchema} from '@sanity/schema/_internal'\nimport {type Workspace} from 'sanity'\n\nimport {getStudioWorkspaces} from '../util/getStudioWorkspaces'\nimport {mockBrowserEnvironment} from '../util/mockBrowserEnvironment'\n\n/** @internal */\nexport interface ExtractSchemaWorkerData {\n  workDir: string\n  workspaceName?: string\n  enforceRequiredFields?: boolean\n  format: 'groq-type-nodes' | string\n}\n\n/** @internal */\nexport interface ExtractSchemaWorkerResult {\n  schema: ReturnType<typeof extractSchema>\n}\n\nif (isMainThread || !parentPort) {\n  throw new Error('This module must be run as a worker thread')\n}\n\nconst opts = _workerData as ExtractSchemaWorkerData\nconst cleanup = mockBrowserEnvironment(opts.workDir)\n\nasync function main() {\n  try {\n    if (opts.format !== 'groq-type-nodes') {\n      throw new Error(`Unsupported format: \"${opts.format}\"`)\n    }\n\n    const workspaces = await getStudioWorkspaces({basePath: opts.workDir})\n\n    const workspace = getWorkspace({workspaces, workspaceName: opts.workspaceName})\n\n    const schema = extractSchema(workspace.schema, {\n      enforceRequiredFields: opts.enforceRequiredFields,\n    })\n\n    parentPort?.postMessage({\n      schema,\n    } satisfies ExtractSchemaWorkerResult)\n  } finally {\n    cleanup()\n  }\n}\n\nmain()\n\nfunction getWorkspace({\n  workspaces,\n  workspaceName,\n}: {\n  workspaces: Workspace[]\n  workspaceName?: string\n}): Workspace {\n  if (workspaces.length === 0) {\n    throw new Error('No studio configuration found')\n  }\n\n  if (workspaces.length === 1) {\n    return workspaces[0]\n  }\n\n  if (workspaceName === undefined) {\n    throw new Error(\n      `Multiple workspaces found. Please specify which workspace to use with '--workspace'.`,\n    )\n  }\n  const workspace = workspaces.find((w) => w.name === workspaceName)\n  if (!workspace) {\n    throw new Error(`Could not find workspace \"${workspaceName}\"`)\n  }\n  return workspace\n}\n"], "names": ["isMainThread", "parentPort", "Error", "opts", "_workerData", "cleanup", "mockBrowserEnvironment", "workDir", "main", "format", "workspaces", "getStudioWorkspaces", "basePath", "workspace", "getWorkspace", "workspaceName", "schema", "extractSchema", "enforceRequiredFields", "postMessage", "length", "undefined", "find", "w", "name"], "mappings": ";;AAqBA,IAAIA,oBAAAA,gBAAgB,CAACC,oBAAAA;AACb,QAAA,IAAIC,MAAM,4CAA4C;AAG9D,MAAMC,OAAOC,oBAAAA,YACPC,UAAUC,uBAAAA,uBAAuBH,KAAKI,OAAO;AAEnD,eAAeC,OAAO;AAChB,MAAA;AACF,QAAIL,KAAKM,WAAW;AAClB,YAAM,IAAIP,MAAM,wBAAwBC,KAAKM,MAAM,GAAG;AAGlDC,UAAAA,aAAa,MAAMC,wCAAoB;AAAA,MAACC,UAAUT,KAAKI;AAAAA,IAAAA,CAAQ,GAE/DM,YAAYC,aAAa;AAAA,MAACJ;AAAAA,MAAYK,eAAeZ,KAAKY;AAAAA,IAAc,CAAA,GAExEC,SAASC,wBAAcJ,UAAUG,QAAQ;AAAA,MAC7CE,uBAAuBf,KAAKe;AAAAA,IAAAA,CAC7B;AAEDjB,wBAAAA,YAAYkB,YAAY;AAAA,MACtBH;AAAAA,IAAAA,CACmC;AAAA,EAAA,UAC7B;AACA,YAAA;AAAA,EAAA;AAEZ;AAEAR,KAAK;AAEL,SAASM,aAAa;AAAA,EACpBJ;AAAAA,EACAK;AAIF,GAAc;AACZ,MAAIL,WAAWU,WAAW;AAClB,UAAA,IAAIlB,MAAM,+BAA+B;AAGjD,MAAIQ,WAAWU,WAAW;AACxB,WAAOV,WAAW,CAAC;AAGrB,MAAIK,kBAAkBM;AACd,UAAA,IAAInB,MACR,sFACF;AAEF,QAAMW,YAAYH,WAAWY,KAAMC,CAAMA,MAAAA,EAAEC,SAAST,aAAa;AACjE,MAAI,CAACF;AACH,UAAM,IAAIX,MAAM,6BAA6Ba,aAAa,GAAG;AAExDF,SAAAA;AACT;"}
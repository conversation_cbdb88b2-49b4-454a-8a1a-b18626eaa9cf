{"name": "@optimize-lodash/rollup-plugin", "version": "5.0.0", "description": "Rewrite lodash imports with Rollup for improved tree-shaking.", "keywords": ["lodash", "rollup", "rollup-plugin", "vite-plugin", "optimize", "minify"], "homepage": "https://github.com/kyle-johnson/rollup-plugin-optimize-lodash-imports/tree/main/packages/rollup-plugin", "repository": {"type": "git", "url": "https://github.com/kyle-johnson/rollup-plugin-optimize-lodash-imports.git"}, "exports": {"require": "./dist/index.js", "import": "./dist/index.mjs", "types": "./dist/index.d.ts"}, "types": "dist/index.d.ts", "files": ["dist"], "author": "<PERSON>", "license": "MIT", "engines": {"node": ">= 18"}, "publishConfig": {"access": "public"}, "peerDependencies": {"rollup": ">= 4.x"}, "devDependencies": {"@rollup/plugin-commonjs": "26.0.1", "@rollup/plugin-node-resolve": "15.2.3", "@rollup/plugin-terser": "0.4.4", "@tsconfig/node18": "18.2.4", "@types/estree": "1.0.1", "@types/jest": "29.5.4", "@types/lodash": "4.14.199", "@types/node": "18.15.3", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "depcheck": "1.4.6", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-jest": "28.6.0", "eslint-plugin-unicorn": "55.0.0", "gen-esm-wrapper": "1.1.3", "jest": "29.7.0", "lodash": "4.17.21", "prettier": "3.3.3", "prettier-2": "npm:prettier@2.8.8", "rollup": "4.19.1", "ts-jest": "29.1.1", "ts-node": "10.9.2", "typescript": "5.5.4"}, "dependencies": {"@rollup/pluginutils": "^5.1.0", "@optimize-lodash/transform": "3.0.4"}, "scripts": {"type-check": "tsc --noEmit", "lint": "eslint .", "test": "jest", "test:ci": "jest --coverage --ci", "build": "rm -rf dist && tsc -p tsconfig.dist.json && gen-esm-wrapper ./dist ./dist/index.mjs", "format": "prettier --write .", "format:check": "prettier --check .", "depcheck": "depcheck"}}
{"name": "@optimize-lodash/transform", "version": "3.0.4", "description": "Rewrites lodash imports in a given source file to be specific.", "keywords": ["lodash", "optimize"], "homepage": "https://github.com/kyle-johnson/rollup-plugin-optimize-lodash-imports/tree/main/packages/transform", "repository": {"type": "git", "url": "https://github.com/kyle-johnson/rollup-plugin-optimize-lodash-imports.git"}, "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "author": "<PERSON>", "license": "MIT", "engines": {"node": ">= 12"}, "publishConfig": {"access": "public"}, "jest": {"coverageDirectory": "coverage", "testEnvironment": "node", "preset": "ts-jest"}, "devDependencies": {"@tsconfig/node12": "1.0.11", "@types/estree": "1.0.1", "@types/jest": "29.5.4", "@types/lodash": "4.14.199", "@types/node": "12.20.55", "@typescript-eslint/eslint-plugin": "6.7.0", "@typescript-eslint/parser": "6.7.0", "acorn": "8.12.1", "depcheck": "1.4.6", "eslint": "8.51.0", "eslint-config-prettier": "9.0.0", "eslint-plugin-jest": "27.4.0", "eslint-plugin-unicorn": "44.0.2", "jest": "29.7.0", "prettier": "3.0.3", "ts-jest": "29.1.1", "typescript": "5.2.2"}, "dependencies": {"estree-walker": "^2.0.2", "magic-string": "~0.30.11"}, "scripts": {"type-check": "tsc --noEmit", "lint": "eslint .", "test": "jest", "test:ci": "jest --coverage --ci", "build": "rm -rf dist && tsc -p tsconfig.dist.json", "format": "prettier --write .", "format:check": "prettier --check .", "depcheck": "depcheck"}}
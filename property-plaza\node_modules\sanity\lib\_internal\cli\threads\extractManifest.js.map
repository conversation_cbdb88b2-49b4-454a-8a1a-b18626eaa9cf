{"version": 3, "file": "extractManifest.js", "sources": ["../../../../src/_internal/manifest/Icon.tsx", "../../../../src/_internal/manifest/manifestTypeHelpers.ts", "../../../../src/_internal/manifest/purifyConfig.ts", "../../../../src/_internal/manifest/extractWorkspaceManifest.tsx", "../../../../src/_internal/cli/threads/extractManifest.ts"], "sourcesContent": ["import {ThemeProvider} from '@sanity/ui'\nimport {buildTheme} from '@sanity/ui/theme'\nimport {type ComponentType, isValidElement, type ReactNode} from 'react'\nimport {isValidElementType} from 'react-is'\nimport {createDefaultIcon} from 'sanity'\n\nconst theme = buildTheme()\n\ninterface SchemaIconProps {\n  icon?: ComponentType | ReactNode\n  title: string\n  subtitle?: string\n}\n\nconst SchemaIcon = ({icon, title, subtitle}: SchemaIconProps): JSX.Element => {\n  return <ThemeProvider theme={theme}>{normalizeIcon(icon, title, subtitle)}</ThemeProvider>\n}\n\nfunction normalizeIcon(\n  Icon: ComponentType | ReactNode | undefined,\n  title: string,\n  subtitle = '',\n): JSX.Element {\n  if (isValidElementType(Icon)) return <Icon />\n  if (isValidElement(Icon)) return Icon\n  return createDefaultIcon(title, subtitle)\n}\n\nexport {SchemaIcon}\nexport type {SchemaIconProps}\n", "import {\n  type CrossDatasetReferenceSchemaType,\n  type ObjectField,\n  type ObjectSchemaType,\n  type ReferenceSchemaType,\n  type SchemaType,\n} from '@sanity/types'\n\nconst DEFAULT_IMAGE_FIELDS = ['asset', 'hotspot', 'crop']\nconst DEFAULT_FILE_FIELDS = ['asset']\nconst DEFAULT_GEOPOINT_FIELDS = ['lat', 'lng', 'alt']\nconst DEFAULT_SLUG_FIELDS = ['current', 'source']\n\nexport function getCustomFields(type: ObjectSchemaType): (ObjectField & {fieldset?: string})[] {\n  const fields = type.fieldsets\n    ? type.fieldsets.flatMap((fs) => {\n        if (fs.single) {\n          return fs.field\n        }\n        return fs.fields.map((field) => ({\n          ...field,\n          fieldset: fs.name,\n        }))\n      })\n    : type.fields\n\n  if (isType(type, 'block')) {\n    return []\n  }\n  if (isType(type, 'slug')) {\n    return fields.filter((f) => !DEFAULT_SLUG_FIELDS.includes(f.name))\n  }\n  if (isType(type, 'geopoint')) {\n    return fields.filter((f) => !DEFAULT_GEOPOINT_FIELDS.includes(f.name))\n  }\n  if (isType(type, 'image')) {\n    return fields.filter((f) => !DEFAULT_IMAGE_FIELDS.includes(f.name))\n  }\n  if (isType(type, 'file')) {\n    return fields.filter((f) => !DEFAULT_FILE_FIELDS.includes(f.name))\n  }\n  return fields\n}\n\nexport function isReference(type: SchemaType): type is ReferenceSchemaType {\n  return isType(type, 'reference')\n}\n\nexport function isCrossDatasetReference(type: SchemaType): type is CrossDatasetReferenceSchemaType {\n  return isType(type, 'crossDatasetReference')\n}\n\nexport function isObjectField(maybeOjectField: unknown): boolean {\n  return (\n    typeof maybeOjectField === 'object' && maybeOjectField !== null && 'name' in maybeOjectField\n  )\n}\n\nexport function isCustomized(maybeCustomized: SchemaType): boolean {\n  const hasFieldsArray =\n    isObjectField(maybeCustomized) &&\n    !isType(maybeCustomized, 'reference') &&\n    !isType(maybeCustomized, 'crossDatasetReference') &&\n    'fields' in maybeCustomized &&\n    Array.isArray(maybeCustomized.fields)\n\n  if (!hasFieldsArray) {\n    return false\n  }\n\n  const fields = getCustomFields(maybeCustomized)\n  return !!fields.length\n}\n\nexport function isType(schemaType: SchemaType, typeName: string): boolean {\n  if (schemaType.name === typeName) {\n    return true\n  }\n  if (!schemaType.type) {\n    return false\n  }\n  return isType(schemaType.type, typeName)\n}\n\nexport function isDefined<T>(value: T | null | undefined): value is T {\n  return value !== null && value !== undefined\n}\n\nexport function isRecord(value: unknown): value is Record<string, unknown> {\n  return !!value && typeof value === 'object'\n}\n\nexport function isPrimitive(value: unknown): value is string | boolean | number {\n  return isString(value) || isBoolean(value) || isNumber(value)\n}\n\nexport function isString(value: unknown): value is string {\n  return typeof value === 'string'\n}\n\nfunction isNumber(value: unknown): value is number {\n  return typeof value === 'boolean'\n}\n\nfunction isBoolean(value: unknown): value is boolean {\n  return typeof value === 'number'\n}\n", "import {type Config} from 'isomorphic-dompurify'\n\n/**\n * This file maintains our sanitization configuration for DOMPurify.\n * We use an allowlist for tags and attributes to ensure that only safe\n * elements and attributes are allowed.\n *\n * This is easier to loosen as specs develop & use-cases are discovered.\n */\n\n///////// Tags\n\nconst HTML_TAGS = ['img', 'style']\n\nconst SVG_TAGS = [\n  'svg',\n  'a',\n  'altglyph',\n  'altglyphdef',\n  'altglyphitem',\n  'animatecolor',\n  'animatemotion',\n  'animatetransform',\n  'circle',\n  'clippath',\n  'defs',\n  'desc',\n  'ellipse',\n  'filter',\n  'font',\n  'g',\n  'glyph',\n  'glyphref',\n  'hkern',\n  'image',\n  'line',\n  'lineargradient',\n  'marker',\n  'mask',\n  'metadata',\n  'mpath',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialgradient',\n  'rect',\n  'stop',\n  'style',\n  'switch',\n  'symbol',\n  'text',\n  'textpath',\n  'title',\n  'tref',\n  'tspan',\n  'view',\n  'vkern',\n] as const\n\nconst SVG_FILTER_TAGS = [\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feDropShadow',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n] as const\n\nconst ALLOWED_TAGS: Config['ALLOWED_TAGS'] = [...SVG_TAGS, ...HTML_TAGS, ...SVG_FILTER_TAGS]\n\n///////// Attributes\n\nconst HTML_ATTRIBUTES = [\n  'alt',\n  'class',\n  'crossorigin',\n  'decoding',\n  'elementtiming',\n  'fetchpriority',\n  'height',\n  'loading',\n  'src',\n  'srcset',\n  'style',\n  'width',\n]\n\nconst SVG_ATTRIBUTES = [\n  'accent-height',\n  'accumulate',\n  'additive',\n  'alignment-baseline',\n  'amplitude',\n  'ascent',\n  'attributename',\n  'attributetype',\n  'azimuth',\n  'basefrequency',\n  'baseline-shift',\n  'begin',\n  'bias',\n  'by',\n  'class',\n  'clip',\n  'clippathunits',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'cx',\n  'cy',\n  'd',\n  'dx',\n  'dy',\n  'diffuseconstant',\n  'direction',\n  'display',\n  'divisor',\n  'dur',\n  'edgemode',\n  'elevation',\n  'end',\n  'exponent',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'filterunits',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'fx',\n  'fy',\n  'g1',\n  'g2',\n  'glyph-name',\n  'glyphref',\n  'gradientunits',\n  'gradienttransform',\n  'height',\n  'href',\n  'id',\n  'image-rendering',\n  'in',\n  'in2',\n  'intercept',\n  'k',\n  'k1',\n  'k2',\n  'k3',\n  'k4',\n  'kerning',\n  'keypoints',\n  'keysplines',\n  'keytimes',\n  'lang',\n  'lengthadjust',\n  'letter-spacing',\n  'kernelmatrix',\n  'kernelunitlength',\n  'lighting-color',\n  'local',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'markerheight',\n  'markerunits',\n  'markerwidth',\n  'maskcontentunits',\n  'maskunits',\n  'max',\n  'mask',\n  'media',\n  'method',\n  'mode',\n  'min',\n  'name',\n  'numoctaves',\n  'offset',\n  'operator',\n  'opacity',\n  'order',\n  'orient',\n  'orientation',\n  'origin',\n  'overflow',\n  'paint-order',\n  'path',\n  'pathlength',\n  'patterncontentunits',\n  'patterntransform',\n  'patternunits',\n  'points',\n  'preservealpha',\n  'preserveaspectratio',\n  'primitiveunits',\n  'r',\n  'rx',\n  'ry',\n  'radius',\n  'refx',\n  'refy',\n  'repeatcount',\n  'repeatdur',\n  'restart',\n  'result',\n  'rotate',\n  'scale',\n  'seed',\n  'shape-rendering',\n  'slope',\n  'specularconstant',\n  'specularexponent',\n  'spreadmethod',\n  'startoffset',\n  'stddeviation',\n  'stitchtiles',\n  'stop-color',\n  'stop-opacity',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke',\n  'stroke-width',\n  'style',\n  'surfacescale',\n  'systemlanguage',\n  'tabindex',\n  'tablevalues',\n  'targetx',\n  'targety',\n  'transform',\n  'transform-origin',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'textlength',\n  'type',\n  'u1',\n  'u2',\n  'unicode',\n  'values',\n  'viewbox',\n  'visibility',\n  'version',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'width',\n  'word-spacing',\n  'wrap',\n  'writing-mode',\n  'xchannelselector',\n  'ychannelselector',\n  'x',\n  'x1',\n  'x2',\n  'xmlns',\n  'y',\n  'y1',\n  'y2',\n  'z',\n  'zoomandpan',\n] as const\n\nconst ALLOWED_ATTR: Config['ALLOWED_ATTR'] = [...SVG_ATTRIBUTES, ...HTML_ATTRIBUTES]\n\nconst config = {\n  ALLOWED_ATTR,\n  ALLOWED_TAGS,\n  /**\n   * Required to allow for the use of `style` tags,\n   * namely rendering the style tags from `styled-components`\n   */\n  FORCE_BODY: true,\n} satisfies Config\n\nexport {config}\n", "import DOMPurify from 'isomorphic-dompurify'\nimport startCase from 'lodash/startCase'\nimport {renderToString} from 'react-dom/server'\nimport {\n  type ArraySchemaType,\n  type BlockDefinition,\n  type BooleanSchemaType,\n  ConcreteRuleClass,\n  createSchema,\n  type CrossDatasetReferenceSchemaType,\n  type FileSchemaType,\n  type MultiFieldSet,\n  type NumberSchemaType,\n  type ObjectField,\n  type ObjectSchemaType,\n  type ReferenceSchemaType,\n  type Rule,\n  type RuleSpec,\n  type Schema,\n  type SchemaType,\n  type SchemaValidationValue,\n  type SpanSchemaType,\n  type StringSchemaType,\n  type Workspace,\n} from 'sanity'\nimport {ServerStyleSheet} from 'styled-components'\n\nimport {SchemaIcon, type SchemaIconProps} from './Icon'\nimport {\n  getCustomFields,\n  isCrossDatasetReference,\n  isCustomized,\n  isDefined,\n  isPrimitive,\n  isRecord,\n  isReference,\n  isString,\n  isType,\n} from './manifestTypeHelpers'\nimport {\n  type CreateWorkspaceManifest,\n  type ManifestField,\n  type ManifestFieldset,\n  type ManifestSchemaType,\n  type ManifestSerializable,\n  type ManifestTitledValue,\n  type ManifestTool,\n  type ManifestValidationGroup,\n  type ManifestValidationRule,\n} from './manifestTypes'\nimport {config} from './purifyConfig'\n\ninterface Context {\n  schema: Schema\n}\n\ntype SchemaTypeKey =\n  | keyof ArraySchemaType\n  | keyof BooleanSchemaType\n  | keyof FileSchemaType\n  | keyof NumberSchemaType\n  | keyof ObjectSchemaType\n  | keyof StringSchemaType\n  | keyof ReferenceSchemaType\n  | keyof BlockDefinition\n  | 'group' // we strip this from fields\n\ntype Validation = {validation: ManifestValidationGroup[]} | Record<string, never>\ntype ObjectFields = {fields: ManifestField[]} | Record<string, never>\ntype SerializableProp = ManifestSerializable | ManifestSerializable[] | undefined\ntype ManifestValidationFlag = ManifestValidationRule['flag']\ntype ValidationRuleTransformer = (rule: RuleSpec) => ManifestValidationRule | undefined\n\nconst MAX_CUSTOM_PROPERTY_DEPTH = 5\nconst INLINE_TYPES = ['document', 'object', 'image', 'file']\n\nexport function extractCreateWorkspaceManifest(workspace: Workspace): CreateWorkspaceManifest {\n  const serializedSchema = extractManifestSchemaTypes(workspace.schema)\n  const serializedTools = extractManifestTools(workspace.tools)\n\n  return {\n    name: workspace.name,\n    title: workspace.title,\n    subtitle: workspace.subtitle,\n    basePath: workspace.basePath,\n    projectId: workspace.projectId,\n    dataset: workspace.dataset,\n    icon: resolveIcon({\n      icon: workspace.icon,\n      title: workspace.title,\n      subtitle: workspace.subtitle,\n    }),\n    schema: serializedSchema,\n    tools: serializedTools,\n  }\n}\n\n/**\n * Extracts all serializable properties from userland schema types,\n * so they best-effort can be used as definitions for Schema.compile\n. */\nexport function extractManifestSchemaTypes(schema: Schema): ManifestSchemaType[] {\n  const typeNames = schema.getTypeNames()\n  const context = {schema}\n\n  const studioDefaultTypeNames = createSchema({name: 'default', types: []}).getTypeNames()\n\n  return typeNames\n    .filter((typeName) => !studioDefaultTypeNames.includes(typeName))\n    .map((typeName) => schema.get(typeName))\n    .filter((type): type is SchemaType => typeof type !== 'undefined')\n    .map((type) => transformType(type, context))\n}\n\nfunction transformCommonTypeFields(\n  type: SchemaType & {fieldset?: string},\n  typeName: string,\n  context: Context,\n): Omit<ManifestSchemaType, 'name' | 'title' | 'type'> {\n  const arrayProps =\n    typeName === 'array' && type.jsonType === 'array' ? transformArrayMember(type, context) : {}\n\n  const referenceProps = isReference(type) ? transformReference(type) : {}\n  const crossDatasetRefProps = isCrossDatasetReference(type)\n    ? transformCrossDatasetReference(type)\n    : {}\n\n  const objectFields: ObjectFields =\n    type.jsonType === 'object' && type.type && INLINE_TYPES.includes(typeName) && isCustomized(type)\n      ? {\n          fields: getCustomFields(type).map((objectField) => transformField(objectField, context)),\n        }\n      : {}\n\n  return {\n    ...retainCustomTypeProps(type),\n    ...transformValidation(type.validation),\n    ...ensureString('description', type.description),\n    ...objectFields,\n    ...arrayProps,\n    ...referenceProps,\n    ...crossDatasetRefProps,\n    ...ensureConditional('readOnly', type.readOnly),\n    ...ensureConditional('hidden', type.hidden),\n    ...transformFieldsets(type),\n    // fieldset prop gets instrumented via getCustomFields\n    ...ensureString('fieldset', type.fieldset),\n    ...transformBlockType(type, context),\n  }\n}\n\nfunction transformFieldsets(\n  type: SchemaType,\n): {fieldsets: ManifestFieldset[]} | Record<string, never> {\n  if (type.jsonType !== 'object') {\n    return {}\n  }\n  const fieldsets = type.fieldsets\n    ?.filter((fs): fs is MultiFieldSet => !fs.single)\n    .map((fs) => {\n      const options = isRecord(fs.options) ? {options: retainSerializableProps(fs.options)} : {}\n      return {\n        name: fs.name,\n        ...ensureCustomTitle(fs.name, fs.title),\n        ...ensureString('description', fs.description),\n        ...ensureConditional('readOnly', fs.readOnly),\n        ...ensureConditional('hidden', fs.hidden),\n        ...options,\n      }\n    })\n\n  return fieldsets?.length ? {fieldsets} : {}\n}\n\nfunction transformType(type: SchemaType, context: Context): ManifestSchemaType {\n  const typeName = type.type ? type.type.name : type.jsonType\n\n  return {\n    ...transformCommonTypeFields(type, typeName, context),\n    name: type.name,\n    type: typeName,\n    ...ensureCustomTitle(type.name, type.title),\n  }\n}\n\nfunction retainCustomTypeProps(type: SchemaType): Record<string, SerializableProp> {\n  const manuallySerializedFields: SchemaTypeKey[] = [\n    //explicitly added\n    'name',\n    'title',\n    'description',\n    'readOnly',\n    'hidden',\n    'validation',\n    'fieldsets',\n    'fields',\n    'to',\n    'of',\n    // not serialized\n    'type',\n    'jsonType',\n    '__experimental_actions',\n    '__experimental_formPreviewTitle',\n    '__experimental_omnisearch_visibility',\n    '__experimental_search',\n    'components',\n    'icon',\n    'orderings',\n    'preview',\n    'groups',\n    //only exists on fields\n    'group',\n    // we know about these, but let them be generically handled\n    // deprecated\n    // rows (from text)\n    // initialValue\n    // options\n    // crossDatasetReference props\n  ]\n  const typeWithoutManuallyHandledFields = Object.fromEntries(\n    Object.entries(type).filter(\n      ([key]) => !manuallySerializedFields.includes(key as unknown as SchemaTypeKey),\n    ),\n  )\n  return retainSerializableProps(typeWithoutManuallyHandledFields) as Record<\n    string,\n    SerializableProp\n  >\n}\n\nfunction retainSerializableProps(maybeSerializable: unknown, depth = 0): SerializableProp {\n  if (depth > MAX_CUSTOM_PROPERTY_DEPTH) {\n    return undefined\n  }\n\n  if (!isDefined(maybeSerializable)) {\n    return undefined\n  }\n\n  if (isPrimitive(maybeSerializable)) {\n    // cull empty strings\n    if (maybeSerializable === '') {\n      return undefined\n    }\n    return maybeSerializable\n  }\n\n  // url-schemes ect..\n  if (maybeSerializable instanceof RegExp) {\n    return maybeSerializable.toString()\n  }\n\n  if (Array.isArray(maybeSerializable)) {\n    const arrayItems = maybeSerializable\n      .map((item) => retainSerializableProps(item, depth + 1))\n      .filter((item): item is ManifestSerializable => isDefined(item))\n    return arrayItems.length ? arrayItems : undefined\n  }\n\n  if (isRecord(maybeSerializable)) {\n    const serializableEntries = Object.entries(maybeSerializable)\n      .map(([key, value]) => {\n        return [key, retainSerializableProps(value, depth + 1)]\n      })\n      .filter(([, value]) => isDefined(value))\n    return serializableEntries.length ? Object.fromEntries(serializableEntries) : undefined\n  }\n\n  return undefined\n}\n\nfunction transformField(field: ObjectField & {fieldset?: string}, context: Context): ManifestField {\n  const fieldType = field.type\n  const typeNameExists = !!context.schema.get(fieldType.name)\n  const typeName = typeNameExists ? fieldType.name : (fieldType.type?.name ?? fieldType.name)\n  return {\n    ...transformCommonTypeFields(fieldType, typeName, context),\n    name: field.name,\n    type: typeName,\n    ...ensureCustomTitle(field.name, fieldType.title),\n    // this prop gets added synthetically via getCustomFields\n    ...ensureString('fieldset', field.fieldset),\n  }\n}\n\nfunction transformArrayMember(\n  arrayMember: ArraySchemaType,\n  context: Context,\n): Pick<ManifestField, 'of'> {\n  return {\n    of: arrayMember.of.map((type) => {\n      const typeNameExists = !!context.schema.get(type.name)\n      const typeName = typeNameExists ? type.name : (type.type?.name ?? type.name)\n      return {\n        ...transformCommonTypeFields(type, typeName, context),\n        type: typeName,\n        ...(typeName === type.name ? {} : {name: type.name}),\n        ...ensureCustomTitle(type.name, type.title),\n      }\n    }),\n  }\n}\n\nfunction transformReference(reference: ReferenceSchemaType): Pick<ManifestSchemaType, 'to'> {\n  return {\n    to: (reference.to ?? []).map((type) => {\n      return {\n        ...retainCustomTypeProps(type),\n        type: type.name,\n      }\n    }),\n  }\n}\n\nfunction transformCrossDatasetReference(\n  reference: CrossDatasetReferenceSchemaType,\n): Pick<ManifestSchemaType, 'to' | 'preview'> {\n  return {\n    to: (reference.to ?? []).map((crossDataset) => {\n      const preview = crossDataset.preview?.select\n        ? {preview: {select: crossDataset.preview.select}}\n        : {}\n      return {\n        type: crossDataset.type,\n        ...ensureCustomTitle(crossDataset.type, crossDataset.title),\n        ...preview,\n      }\n    }),\n  }\n}\n\nconst transformTypeValidationRule: ValidationRuleTransformer = (rule) => {\n  return {\n    ...rule,\n    constraint:\n      'constraint' in rule &&\n      (typeof rule.constraint === 'string'\n        ? rule.constraint.toLowerCase()\n        : retainSerializableProps(rule.constraint)),\n  }\n}\n\nconst validationRuleTransformers: Partial<\n  Record<ManifestValidationFlag, ValidationRuleTransformer>\n> = {\n  type: transformTypeValidationRule,\n}\n\nfunction transformValidation(validation: SchemaValidationValue): Validation {\n  const validationArray = (Array.isArray(validation) ? validation : [validation]).filter(\n    (value): value is Rule => typeof value === 'object' && '_type' in value,\n  )\n\n  // we dont want type in the output as that is implicitly given by the typedef itself an will only bloat the payload\n  const disallowedFlags = ['type']\n\n  // Validation rules that refer to other fields use symbols, which cannot be serialized. It would\n  // be possible to transform these to a serializable type, but we haven't implemented that for now.\n  const disallowedConstraintTypes: (symbol | unknown)[] = [ConcreteRuleClass.FIELD_REF]\n\n  const serializedValidation = validationArray\n    .map(({_rules, _message, _level}) => {\n      const message: Partial<Pick<ManifestValidationGroup, 'message'>> =\n        typeof _message === 'string' ? {message: _message} : {}\n\n      const serializedRules = _rules\n        .filter((rule) => {\n          if (!('constraint' in rule)) {\n            return false\n          }\n\n          const {flag, constraint} = rule\n\n          if (disallowedFlags.includes(flag)) {\n            return false\n          }\n\n          return !(\n            typeof constraint === 'object' &&\n            'type' in constraint &&\n            disallowedConstraintTypes.includes(constraint.type)\n          )\n        })\n        .reduce<ManifestValidationRule[]>((rules, rule) => {\n          const transformer: ValidationRuleTransformer =\n            validationRuleTransformers[rule.flag] ??\n            ((spec) => retainSerializableProps(spec) as ManifestValidationRule)\n\n          const transformedRule = transformer(rule)\n          if (!transformedRule) {\n            return rules\n          }\n          return [...rules, transformedRule]\n        }, [])\n\n      return {\n        rules: serializedRules,\n        level: _level,\n        ...message,\n      }\n    })\n    .filter((group) => !!group.rules.length)\n\n  return serializedValidation.length ? {validation: serializedValidation} : {}\n}\n\nfunction ensureCustomTitle(typeName: string, value: unknown) {\n  const titleObject = ensureString('title', value)\n\n  const defaultTitle = startCase(typeName)\n  // omit title if its the same as default, to reduce payload\n  if (titleObject.title === defaultTitle) {\n    return {}\n  }\n  return titleObject\n}\n\nfunction ensureString<Key extends string>(key: Key, value: unknown) {\n  if (typeof value === 'string') {\n    return {\n      [key]: value,\n    }\n  }\n\n  return {}\n}\n\nfunction ensureConditional<const Key extends string>(key: Key, value: unknown) {\n  if (typeof value === 'boolean') {\n    return {\n      [key]: value,\n    }\n  }\n\n  if (typeof value === 'function') {\n    return {\n      [key]: 'conditional',\n    }\n  }\n\n  return {}\n}\n\nexport function transformBlockType(\n  blockType: SchemaType,\n  context: Context,\n): Pick<ManifestSchemaType, 'marks' | 'lists' | 'styles' | 'of'> | Record<string, never> {\n  if (blockType.jsonType !== 'object' || !isType(blockType, 'block')) {\n    return {}\n  }\n\n  const childrenField = blockType.fields?.find((field) => field.name === 'children') as\n    | {type: ArraySchemaType}\n    | undefined\n\n  if (!childrenField) {\n    return {}\n  }\n  const ofType = childrenField.type.of\n  if (!ofType) {\n    return {}\n  }\n  const spanType = ofType.find((memberType) => memberType.name === 'span') as\n    | ObjectSchemaType\n    | undefined\n  if (!spanType) {\n    return {}\n  }\n  const inlineObjectTypes = (ofType.filter((memberType) => memberType.name !== 'span') ||\n    []) as ObjectSchemaType[]\n\n  return {\n    marks: {\n      annotations: (spanType as SpanSchemaType).annotations.map((t) => transformType(t, context)),\n      decorators: resolveEnabledDecorators(spanType),\n    },\n    lists: resolveEnabledListItems(blockType),\n    styles: resolveEnabledStyles(blockType),\n    of: inlineObjectTypes.map((t) => transformType(t, context)),\n  }\n}\n\nfunction resolveEnabledStyles(blockType: ObjectSchemaType): ManifestTitledValue[] | undefined {\n  const styleField = blockType.fields?.find((btField) => btField.name === 'style')\n  return resolveTitleValueArray(styleField?.type?.options?.list)\n}\n\nfunction resolveEnabledDecorators(spanType: ObjectSchemaType): ManifestTitledValue[] | undefined {\n  return 'decorators' in spanType ? resolveTitleValueArray(spanType.decorators) : undefined\n}\n\nfunction resolveEnabledListItems(blockType: ObjectSchemaType): ManifestTitledValue[] | undefined {\n  const listField = blockType.fields?.find((btField) => btField.name === 'listItem')\n  return resolveTitleValueArray(listField?.type?.options?.list)\n}\n\nfunction resolveTitleValueArray(possibleArray: unknown): ManifestTitledValue[] | undefined {\n  if (!possibleArray || !Array.isArray(possibleArray)) {\n    return undefined\n  }\n  const titledValues = possibleArray\n    .filter(\n      (d): d is {value: string; title?: string} => isRecord(d) && !!d.value && isString(d.value),\n    )\n    .map((item) => {\n      return {\n        value: item.value,\n        ...ensureString('title', item.title),\n      } satisfies ManifestTitledValue\n    })\n  if (!titledValues?.length) {\n    return undefined\n  }\n\n  return titledValues\n}\n\nconst extractManifestTools = (tools: Workspace['tools']): ManifestTool[] =>\n  tools.map((tool) => {\n    const {\n      title,\n      name,\n      icon,\n      __internalApplicationType: type,\n    } = tool as Workspace['tools'][number] & {__internalApplicationType: string}\n    return {\n      title,\n      name,\n      type: type || null,\n      icon: resolveIcon({\n        icon,\n        title,\n      }),\n    } satisfies ManifestTool\n  })\n\nconst resolveIcon = (props: SchemaIconProps): string | null => {\n  const sheet = new ServerStyleSheet()\n\n  try {\n    /**\n     * You must render the element first so\n     * the style-sheet above can be populated\n     */\n    const element = renderToString(sheet.collectStyles(<SchemaIcon {...props} />))\n    const styleTags = sheet.getStyleTags()\n\n    /**\n     * We can then create a single string\n     * of HTML combining our styles and element\n     * before purifying below.\n     */\n    const html = `${styleTags}${element}`.trim()\n\n    return DOMPurify.sanitize(html, config)\n  } catch (error) {\n    return null\n  } finally {\n    sheet.seal()\n  }\n}\n", "import {isMainThread, parentPort, workerData as _workerData} from 'node:worker_threads'\n\nimport {extractCreateWorkspaceManifest} from '../../manifest/extractWorkspaceManifest'\nimport {getStudioWorkspaces} from '../util/getStudioWorkspaces'\nimport {mockBrowserEnvironment} from '../util/mockBrowserEnvironment'\n\n/** @internal */\nexport interface ExtractManifestWorkerData {\n  workDir: string\n}\n\nif (isMainThread || !parentPort) {\n  throw new Error('This module must be run as a worker thread')\n}\n\nconst opts = _workerData as ExtractManifestWorkerData\n\nconst cleanup = mockBrowserEnvironment(opts.workDir)\n\nasync function main() {\n  try {\n    const workspaces = await getStudioWorkspaces({basePath: opts.workDir})\n\n    for (const workspace of workspaces) {\n      parentPort?.postMessage(extractCreateWorkspaceManifest(workspace))\n    }\n  } finally {\n    parentPort?.close()\n    cleanup()\n  }\n}\n\nmain()\n"], "names": ["theme", "buildTheme", "SchemaIcon", "t0", "$", "_c", "icon", "title", "subtitle", "t1", "normalizeIcon", "t2", "jsx", "ThemeProvider", "Icon", "isValidElementType", "isValidElement", "createDefaultIcon", "DEFAULT_IMAGE_FIELDS", "DEFAULT_FILE_FIELDS", "DEFAULT_GEOPOINT_FIELDS", "DEFAULT_SLUG_FIELDS", "get<PERSON>ustom<PERSON>ields", "type", "fields", "fieldsets", "flatMap", "fs", "single", "field", "map", "fieldset", "name", "isType", "filter", "f", "includes", "isReference", "isCrossDatasetReference", "isObjectField", "maybeOjectField", "isCustomized", "maybeCustomized", "Array", "isArray", "length", "schemaType", "typeName", "isDefined", "value", "isRecord", "isPrimitive", "isString", "isBoolean", "isNumber", "HTML_TAGS", "SVG_TAGS", "SVG_FILTER_TAGS", "ALLOWED_TAGS", "HTML_ATTRIBUTES", "SVG_ATTRIBUTES", "ALLOWED_ATTR", "config", "FORCE_BODY", "MAX_CUSTOM_PROPERTY_DEPTH", "INLINE_TYPES", "extractCreateWorkspaceManifest", "workspace", "serializedSchema", "extractManifestSchemaTypes", "schema", "serializedTools", "extractManifestTools", "tools", "basePath", "projectId", "dataset", "resolveIcon", "typeNames", "getTypeNames", "context", "studioDefaultTypeNames", "createSchema", "types", "get", "transformType", "transformCommonTypeFields", "arrayProps", "jsonType", "transformArrayMember", "referenceProps", "transformReference", "crossDatasetRefProps", "transformCrossDatasetReference", "objectFields", "objectField", "transformField", "retainCustomTypeProps", "transformValidation", "validation", "ensureString", "description", "ensureConditional", "readOnly", "hidden", "transformFieldsets", "transformBlockType", "options", "retainSerializableProps", "ensureCustomTitle", "manuallySerializedFields", "typeWithoutManuallyHandledFields", "Object", "fromEntries", "entries", "key", "maybeSerializable", "depth", "RegExp", "toString", "arrayItems", "item", "undefined", "serializableEntries", "fieldType", "arrayMember", "of", "reference", "to", "crossDataset", "preview", "select", "transformTypeValidationRule", "rule", "constraint", "toLowerCase", "validationRuleTransformers", "validationArray", "disallowedFlags", "disallowedConstraintTypes", "ConcreteRuleClass", "FIELD_REF", "serializedValidation", "_rules", "_message", "_level", "message", "rules", "flag", "reduce", "transformedRule", "spec", "level", "group", "titleObject", "defaultTitle", "startCase", "blockType", "childrenField", "find", "ofType", "spanType", "memberType", "inlineObjectTypes", "marks", "annotations", "t", "decorators", "resolveEnabledDecorators", "lists", "resolveEnabledListItems", "styles", "resolveEnabledStyles", "styleField", "btField", "resolveTitleValueArray", "list", "listField", "possible<PERSON><PERSON>y", "titledValues", "d", "tool", "__internalApplicationType", "props", "sheet", "ServerStyleSheet", "element", "renderToString", "collectStyles", "html", "getStyleTags", "trim", "DOMPurify", "sanitize", "seal", "isMainThread", "parentPort", "Error", "opts", "_workerData", "cleanup", "mockBrowserEnvironment", "workDir", "main", "workspaces", "getStudioWorkspaces", "postMessage", "close"], "mappings": ";;;;;;AAMA,MAAMA,QAAQC,QAAAA,WAAAA,GAQRC,aAAaC,CAAA,OAAA;AAAAC,QAAAA,IAAAC,uBAAA,CAAA,GAAC;AAAA,IAAAC;AAAAA,IAAAC;AAAAA,IAAAC;AAAAA,EAAAA,IAAAL;AAAwCM,MAAAA;AAAAL,IAAAE,CAAAA,MAAAA,QAAAF,SAAAI,YAAAJ,EAAA,CAAA,MAAAG,SACrBE,KAAAC,cAAcJ,MAAMC,OAAOC,QAAQ,GAACJ,OAAAE,MAAAF,OAAAI,UAAAJ,OAAAG,OAAAH,OAAAK,MAAAA,KAAAL,EAAA,CAAA;AAAAO,MAAAA;AAAAP,SAAAA,SAAAK,MAAlEE,KAACC,+BAAAC,GAAAA,eAAA,EAAqBb,OAAQS,UAAqC,GAAA,CAAA,GAAgBL,OAAAK,IAAAL,OAAAO,MAAAA,KAAAP,EAAA,CAAA,GAAnFO;AAAmF;AAG5F,SAASD,cACPI,MACAP,OACAC,WAAW,IACE;AACb,SAAIO,QAAmBD,mBAAAA,IAAI,IAAUF,2BAAA,IAAC,MAAO,CAAA,CAAA,IACzCI,MAAeF,eAAAA,IAAI,IAAUA,OAC1BG,OAAAA,kBAAkBV,OAAOC,QAAQ;AAC1C;AClBA,MAAMU,uBAAuB,CAAC,SAAS,WAAW,MAAM,GAClDC,sBAAsB,CAAC,OAAO,GAC9BC,0BAA0B,CAAC,OAAO,OAAO,KAAK,GAC9CC,sBAAsB,CAAC,WAAW,QAAQ;AAEzC,SAASC,gBAAgBC,MAA+D;AAC7F,QAAMC,SAASD,KAAKE,YAChBF,KAAKE,UAAUC,QAASC,CAAAA,OAClBA,GAAGC,SACED,GAAGE,QAELF,GAAGH,OAAOM,IAAKD,CAAW,WAAA;AAAA,IAC/B,GAAGA;AAAAA,IACHE,UAAUJ,GAAGK;AAAAA,EAAAA,EACb,CACH,IACDT,KAAKC;AAET,SAAIS,OAAOV,MAAM,OAAO,IACf,CAAE,IAEPU,OAAOV,MAAM,MAAM,IACdC,OAAOU,OAAQC,CAAM,MAAA,CAACd,oBAAoBe,SAASD,EAAEH,IAAI,CAAC,IAE/DC,OAAOV,MAAM,UAAU,IAClBC,OAAOU,OAAQC,CAAM,MAAA,CAACf,wBAAwBgB,SAASD,EAAEH,IAAI,CAAC,IAEnEC,OAAOV,MAAM,OAAO,IACfC,OAAOU,OAAQC,CAAM,MAAA,CAACjB,qBAAqBkB,SAASD,EAAEH,IAAI,CAAC,IAEhEC,OAAOV,MAAM,MAAM,IACdC,OAAOU,OAAQC,CAAM,MAAA,CAAChB,oBAAoBiB,SAASD,EAAEH,IAAI,CAAC,IAE5DR;AACT;AAEO,SAASa,YAAYd,MAA+C;AAClEU,SAAAA,OAAOV,MAAM,WAAW;AACjC;AAEO,SAASe,wBAAwBf,MAA2D;AAC1FU,SAAAA,OAAOV,MAAM,uBAAuB;AAC7C;AAEO,SAASgB,cAAcC,iBAAmC;AAC/D,SACE,OAAOA,mBAAoB,YAAYA,oBAAoB,QAAQ,UAAUA;AAEjF;AAEO,SAASC,aAAaC,iBAAsC;AAE/DH,SAAAA,cAAcG,eAAe,KAC7B,CAACT,OAAOS,iBAAiB,WAAW,KACpC,CAACT,OAAOS,iBAAiB,uBAAuB,KAChD,YAAYA,mBACZC,MAAMC,QAAQF,gBAAgBlB,MAAM,IAO/B,CAAC,CADOF,gBAAgBoB,eAAe,EAC9BG,SAJP;AAKX;AAEgBZ,SAAAA,OAAOa,YAAwBC,UAA2B;AACpED,SAAAA,WAAWd,SAASe,WACf,KAEJD,WAAWvB,OAGTU,OAAOa,WAAWvB,MAAMwB,QAAQ,IAF9B;AAGX;AAEO,SAASC,UAAaC,OAAyC;AACpE,SAAOA,SAAU;AACnB;AAEO,SAASC,SAASD,OAAkD;AACzE,SAAO,CAAC,CAACA,SAAS,OAAOA,SAAU;AACrC;AAEO,SAASE,YAAYF,OAAoD;AAC9E,SAAOG,SAASH,KAAK,KAAKI,UAAUJ,KAAK,KAAKK,SAASL,KAAK;AAC9D;AAEO,SAASG,SAASH,OAAiC;AACxD,SAAO,OAAOA,SAAU;AAC1B;AAEA,SAASK,SAASL,OAAiC;AACjD,SAAO,OAAOA,SAAU;AAC1B;AAEA,SAASI,UAAUJ,OAAkC;AACnD,SAAO,OAAOA,SAAU;AAC1B;AC9FA,MAAMM,YAAY,CAAC,OAAO,OAAO,GAE3BC,WAAW,CACf,OACA,KACA,YACA,eACA,gBACA,gBACA,iBACA,oBACA,UACA,YACA,QACA,QACA,WACA,UACA,QACA,KACA,SACA,YACA,SACA,SACA,QACA,kBACA,UACA,QACA,YACA,SACA,QACA,WACA,WACA,YACA,kBACA,QACA,QACA,SACA,UACA,UACA,QACA,YACA,SACA,QACA,SACA,QACA,OAAO,GAGHC,kBAAkB,CACtB,WACA,iBACA,uBACA,eACA,oBACA,qBACA,qBACA,kBACA,gBACA,WACA,WACA,WACA,WACA,WACA,kBACA,WACA,WACA,eACA,gBACA,YACA,gBACA,sBACA,eACA,UACA,cAAc,GAGVC,eAAuC,CAAC,GAAGF,UAAU,GAAGD,WAAW,GAAGE,eAAe,GAIrFE,kBAAkB,CACtB,OACA,SACA,eACA,YACA,iBACA,iBACA,UACA,WACA,OACA,UACA,SACA,OAAO,GAGHC,iBAAiB,CACrB,iBACA,cACA,YACA,sBACA,aACA,UACA,iBACA,iBACA,WACA,iBACA,kBACA,SACA,QACA,MACA,SACA,QACA,iBACA,aACA,aACA,SACA,uBACA,+BACA,iBACA,mBACA,MACA,MACA,KACA,MACA,MACA,mBACA,aACA,WACA,WACA,OACA,YACA,aACA,OACA,YACA,QACA,gBACA,aACA,UACA,eACA,eACA,iBACA,eACA,aACA,oBACA,gBACA,cACA,gBACA,eACA,MACA,MACA,MACA,MACA,cACA,YACA,iBACA,qBACA,UACA,QACA,MACA,mBACA,MACA,OACA,aACA,KACA,MACA,MACA,MACA,MACA,WACA,aACA,cACA,YACA,QACA,gBACA,kBACA,gBACA,oBACA,kBACA,SACA,cACA,cACA,gBACA,gBACA,eACA,eACA,oBACA,aACA,OACA,QACA,SACA,UACA,QACA,OACA,QACA,cACA,UACA,YACA,WACA,SACA,UACA,eACA,UACA,YACA,eACA,QACA,cACA,uBACA,oBACA,gBACA,UACA,iBACA,uBACA,kBACA,KACA,MACA,MACA,UACA,QACA,QACA,eACA,aACA,WACA,UACA,UACA,SACA,QACA,mBACA,SACA,oBACA,oBACA,gBACA,eACA,gBACA,eACA,cACA,gBACA,oBACA,qBACA,kBACA,mBACA,qBACA,kBACA,UACA,gBACA,SACA,gBACA,kBACA,YACA,eACA,WACA,WACA,aACA,oBACA,eACA,mBACA,kBACA,cACA,QACA,MACA,MACA,WACA,UACA,WACA,cACA,WACA,cACA,iBACA,iBACA,SACA,gBACA,QACA,gBACA,oBACA,oBACA,KACA,MACA,MACA,SACA,KACA,MACA,MACA,KACA,YAAY,GAGRC,eAAuC,CAAC,GAAGD,gBAAgB,GAAGD,eAAe,GAE7EG,SAAS;AAAA,EACbD;AAAAA,EACAH;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,EAKAK,YAAY;AACd,GC1OMC,4BAA4B,GAC5BC,eAAe,CAAC,YAAY,UAAU,SAAS,MAAM;AAEpD,SAASC,+BAA+BC,WAA+C;AACtFC,QAAAA,mBAAmBC,2BAA2BF,UAAUG,MAAM,GAC9DC,kBAAkBC,qBAAqBL,UAAUM,KAAK;AAErD,SAAA;AAAA,IACLzC,MAAMmC,UAAUnC;AAAAA,IAChBzB,OAAO4D,UAAU5D;AAAAA,IACjBC,UAAU2D,UAAU3D;AAAAA,IACpBkE,UAAUP,UAAUO;AAAAA,IACpBC,WAAWR,UAAUQ;AAAAA,IACrBC,SAAST,UAAUS;AAAAA,IACnBtE,MAAMuE,YAAY;AAAA,MAChBvE,MAAM6D,UAAU7D;AAAAA,MAChBC,OAAO4D,UAAU5D;AAAAA,MACjBC,UAAU2D,UAAU3D;AAAAA,IAAAA,CACrB;AAAA,IACD8D,QAAQF;AAAAA,IACRK,OAAOF;AAAAA,EACT;AACF;AAMO,SAASF,2BAA2BC,QAAsC;AAC/E,QAAMQ,YAAYR,OAAOS,aAAa,GAChCC,UAAU;AAAA,IAACV;AAAAA,EAAAA,GAEXW,yBAAyBC,OAAAA,aAAa;AAAA,IAAClD,MAAM;AAAA,IAAWmD,OAAO,CAAA;AAAA,EAAG,CAAA,EAAEJ,aAAa;AAEhFD,SAAAA,UACJ5C,OAAQa,CAAAA,aAAa,CAACkC,uBAAuB7C,SAASW,QAAQ,CAAC,EAC/DjB,IAAKiB,CAAAA,aAAauB,OAAOc,IAAIrC,QAAQ,CAAC,EACtCb,OAAQX,CAA6B,SAAA,OAAOA,OAAS,GAAW,EAChEO,IAAKP,CAAS8D,SAAAA,cAAc9D,MAAMyD,OAAO,CAAC;AAC/C;AAEA,SAASM,0BACP/D,MACAwB,UACAiC,SACqD;AACrD,QAAMO,aACJxC,aAAa,WAAWxB,KAAKiE,aAAa,UAAUC,qBAAqBlE,MAAMyD,OAAO,IAAI,CAAA,GAEtFU,iBAAiBrD,YAAYd,IAAI,IAAIoE,mBAAmBpE,IAAI,IAAI,CAAC,GACjEqE,uBAAuBtD,wBAAwBf,IAAI,IACrDsE,+BAA+BtE,IAAI,IACnC,IAEEuE,eACJvE,KAAKiE,aAAa,YAAYjE,KAAKA,QAAQ0C,aAAa7B,SAASW,QAAQ,KAAKN,aAAalB,IAAI,IAC3F;AAAA,IACEC,QAAQF,gBAAgBC,IAAI,EAAEO,IAAKiE,CAAgBC,gBAAAA,eAAeD,aAAaf,OAAO,CAAC;AAAA,EAAA,IAEzF,CAAC;AAEA,SAAA;AAAA,IACL,GAAGiB,sBAAsB1E,IAAI;AAAA,IAC7B,GAAG2E,oBAAoB3E,KAAK4E,UAAU;AAAA,IACtC,GAAGC,aAAa,eAAe7E,KAAK8E,WAAW;AAAA,IAC/C,GAAGP;AAAAA,IACH,GAAGP;AAAAA,IACH,GAAGG;AAAAA,IACH,GAAGE;AAAAA,IACH,GAAGU,kBAAkB,YAAY/E,KAAKgF,QAAQ;AAAA,IAC9C,GAAGD,kBAAkB,UAAU/E,KAAKiF,MAAM;AAAA,IAC1C,GAAGC,mBAAmBlF,IAAI;AAAA;AAAA,IAE1B,GAAG6E,aAAa,YAAY7E,KAAKQ,QAAQ;AAAA,IACzC,GAAG2E,mBAAmBnF,MAAMyD,OAAO;AAAA,EACrC;AACF;AAEA,SAASyB,mBACPlF,MACyD;AACzD,MAAIA,KAAKiE,aAAa;AACpB,WAAO,CAAC;AAEJ/D,QAAAA,YAAYF,KAAKE,WACnBS,OAAQP,CAAAA,OAA4B,CAACA,GAAGC,MAAM,EAC/CE,IAAKH,CAAO,OAAA;AACX,UAAMgF,UAAUzD,SAASvB,GAAGgF,OAAO,IAAI;AAAA,MAACA,SAASC,wBAAwBjF,GAAGgF,OAAO;AAAA,IAAA,IAAK,CAAC;AAClF,WAAA;AAAA,MACL3E,MAAML,GAAGK;AAAAA,MACT,GAAG6E,kBAAkBlF,GAAGK,MAAML,GAAGpB,KAAK;AAAA,MACtC,GAAG6F,aAAa,eAAezE,GAAG0E,WAAW;AAAA,MAC7C,GAAGC,kBAAkB,YAAY3E,GAAG4E,QAAQ;AAAA,MAC5C,GAAGD,kBAAkB,UAAU3E,GAAG6E,MAAM;AAAA,MACxC,GAAGG;AAAAA,IACL;AAAA,EAAA,CACD;AAEH,SAAOlF,WAAWoB,SAAS;AAAA,IAACpB;AAAAA,EAAAA,IAAa,CAAC;AAC5C;AAEA,SAAS4D,cAAc9D,MAAkByD,SAAsC;AAC7E,QAAMjC,WAAWxB,KAAKA,OAAOA,KAAKA,KAAKS,OAAOT,KAAKiE;AAE5C,SAAA;AAAA,IACL,GAAGF,0BAA0B/D,MAAMwB,UAAUiC,OAAO;AAAA,IACpDhD,MAAMT,KAAKS;AAAAA,IACXT,MAAMwB;AAAAA,IACN,GAAG8D,kBAAkBtF,KAAKS,MAAMT,KAAKhB,KAAK;AAAA,EAC5C;AACF;AAEA,SAAS0F,sBAAsB1E,MAAoD;AACjF,QAAMuF,2BAA4C;AAAA;AAAA,IAEhD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAAA,GAQIC,mCAAmCC,OAAOC,YAC9CD,OAAOE,QAAQ3F,IAAI,EAAEW,OACnB,CAAC,CAACiF,GAAG,MAAM,CAACL,yBAAyB1E,SAAS+E,GAA+B,CAC/E,CACF;AACA,SAAOP,wBAAwBG,gCAAgC;AAIjE;AAEA,SAASH,wBAAwBQ,mBAA4BC,QAAQ,GAAqB;AACxF,MAAIA,EAAQrD,QAAAA,8BAIPhB,UAAUoE,iBAAiB,GAIhC;AAAA,QAAIjE,YAAYiE,iBAAiB;AAE3BA,aAAAA,sBAAsB,KACxB,SAEKA;AAIT,QAAIA,6BAA6BE;AAC/B,aAAOF,kBAAkBG,SAAS;AAGhC5E,QAAAA,MAAMC,QAAQwE,iBAAiB,GAAG;AACpC,YAAMI,aAAaJ,kBAChBtF,IAAK2F,CAAAA,SAASb,wBAAwBa,MAAMJ,QAAQ,CAAC,CAAC,EACtDnF,OAAQuF,CAAuCzE,SAAAA,UAAUyE,IAAI,CAAC;AAC1DD,aAAAA,WAAW3E,SAAS2E,aAAaE;AAAAA,IAAAA;AAGtCxE,QAAAA,SAASkE,iBAAiB,GAAG;AAC/B,YAAMO,sBAAsBX,OAAOE,QAAQE,iBAAiB,EACzDtF,IAAI,CAAC,CAACqF,KAAKlE,KAAK,MACR,CAACkE,KAAKP,wBAAwB3D,OAAOoE,QAAQ,CAAC,CAAC,CACvD,EACAnF,OAAO,CAAC,CAAA,EAAGe,KAAK,MAAMD,UAAUC,KAAK,CAAC;AACzC,aAAO0E,oBAAoB9E,SAASmE,OAAOC,YAAYU,mBAAmB,IAAID;AAAAA,IAAAA;AAAAA,EAChF;AAGF;AAEA,SAAS1B,eAAenE,OAA0CmD,SAAiC;AACjG,QAAM4C,YAAY/F,MAAMN,MAElBwB,WADmBiC,QAAQV,OAAOc,IAAIwC,UAAU5F,IAAI,IACxB4F,UAAU5F,OAAQ4F,UAAUrG,MAAMS,QAAQ4F,UAAU5F;AAC/E,SAAA;AAAA,IACL,GAAGsD,0BAA0BsC,WAAW7E,UAAUiC,OAAO;AAAA,IACzDhD,MAAMH,MAAMG;AAAAA,IACZT,MAAMwB;AAAAA,IACN,GAAG8D,kBAAkBhF,MAAMG,MAAM4F,UAAUrH,KAAK;AAAA;AAAA,IAEhD,GAAG6F,aAAa,YAAYvE,MAAME,QAAQ;AAAA,EAC5C;AACF;AAEA,SAAS0D,qBACPoC,aACA7C,SAC2B;AACpB,SAAA;AAAA,IACL8C,IAAID,YAAYC,GAAGhG,IAAKP,CAAS,SAAA;AAE/B,YAAMwB,WADmBiC,QAAQV,OAAOc,IAAI7D,KAAKS,IAAI,IACnBT,KAAKS,OAAQT,KAAKA,MAAMS,QAAQT,KAAKS;AAChE,aAAA;AAAA,QACL,GAAGsD,0BAA0B/D,MAAMwB,UAAUiC,OAAO;AAAA,QACpDzD,MAAMwB;AAAAA,QACN,GAAIA,aAAaxB,KAAKS,OAAO,KAAK;AAAA,UAACA,MAAMT,KAAKS;AAAAA,QAAI;AAAA,QAClD,GAAG6E,kBAAkBtF,KAAKS,MAAMT,KAAKhB,KAAK;AAAA,MAC5C;AAAA,IACD,CAAA;AAAA,EACH;AACF;AAEA,SAASoF,mBAAmBoC,WAAgE;AACnF,SAAA;AAAA,IACLC,KAAKD,UAAUC,MAAM,CAAA,GAAIlG,IAAKP,CACrB,UAAA;AAAA,MACL,GAAG0E,sBAAsB1E,IAAI;AAAA,MAC7BA,MAAMA,KAAKS;AAAAA,IAAAA,EAEd;AAAA,EACH;AACF;AAEA,SAAS6D,+BACPkC,WAC4C;AACrC,SAAA;AAAA,IACLC,KAAKD,UAAUC,MAAM,CAAA,GAAIlG,IAAKmG,CAAiB,iBAAA;AACvCC,YAAAA,UAAUD,aAAaC,SAASC,SAClC;AAAA,QAACD,SAAS;AAAA,UAACC,QAAQF,aAAaC,QAAQC;AAAAA,QAAAA;AAAAA,MAAM,IAC9C,CAAC;AACE,aAAA;AAAA,QACL5G,MAAM0G,aAAa1G;AAAAA,QACnB,GAAGsF,kBAAkBoB,aAAa1G,MAAM0G,aAAa1H,KAAK;AAAA,QAC1D,GAAG2H;AAAAA,MACL;AAAA,IACD,CAAA;AAAA,EACH;AACF;AAEA,MAAME,8BAA0DC,CACvD,UAAA;AAAA,EACL,GAAGA;AAAAA,EACHC,YACE,gBAAgBD,SACf,OAAOA,KAAKC,cAAe,WACxBD,KAAKC,WAAWC,YAAAA,IAChB3B,wBAAwByB,KAAKC,UAAU;AAC/C,IAGIE,6BAEF;AAAA,EACFjH,MAAM6G;AACR;AAEA,SAASlC,oBAAoBC,YAA+C;AAC1E,QAAMsC,mBAAmB9F,MAAMC,QAAQuD,UAAU,IAAIA,aAAa,CAACA,UAAU,GAAGjE,OAC7Ee,CAAyB,UAAA,OAAOA,SAAU,YAAY,WAAWA,KACpE,GAGMyF,kBAAkB,CAAC,MAAM,GAIzBC,4BAAkD,CAACC,OAAAA,kBAAkBC,SAAS,GAE9EC,uBAAuBL,gBAC1B3G,IAAI,CAAC;AAAA,IAACiH;AAAAA,IAAQC;AAAAA,IAAUC;AAAAA,EAAAA,MAAY;AAC7BC,UAAAA,UACJ,OAAOF,YAAa,WAAW;AAAA,MAACE,SAASF;AAAAA,IAAAA,IAAY,CAAC;AAgCjD,WAAA;AAAA,MACLG,OA/BsBJ,OACrB7G,OAAQmG,CAAS,SAAA;AAChB,YAAI,EAAE,gBAAgBA;AACb,iBAAA;AAGH,cAAA;AAAA,UAACe;AAAAA,UAAMd;AAAAA,QAAAA,IAAcD;AAE3B,eAAIK,gBAAgBtG,SAASgH,IAAI,IACxB,KAGF,EACL,OAAOd,cAAe,YACtB,UAAUA,cACVK,0BAA0BvG,SAASkG,WAAW/G,IAAI;AAAA,MAErD,CAAA,EACA8H,OAAiC,CAACF,OAAOd,SAAS;AAK3CiB,cAAAA,mBAHJd,2BAA2BH,KAAKe,IAAI,MAClCG,CAAS3C,SAAAA,wBAAwB2C,IAAI,IAELlB,IAAI;AACxC,eAAKiB,kBAGE,CAAC,GAAGH,OAAOG,eAAe,IAFxBH;AAAAA,MAGX,GAAG,EAAE;AAAA,MAILK,OAAOP;AAAAA,MACP,GAAGC;AAAAA,IACL;AAAA,EAAA,CACD,EACAhH,OAAQuH,CAAAA,UAAU,CAAC,CAACA,MAAMN,MAAMtG,MAAM;AAEzC,SAAOiG,qBAAqBjG,SAAS;AAAA,IAACsD,YAAY2C;AAAAA,EAAAA,IAAwB,CAAC;AAC7E;AAEA,SAASjC,kBAAkB9D,UAAkBE,OAAgB;AAC3D,QAAMyG,cAActD,aAAa,SAASnD,KAAK,GAEzC0G,eAAeC,2BAAU7G,QAAQ;AAEvC,SAAI2G,YAAYnJ,UAAUoJ,eACjB,CAEFD,IAAAA;AACT;AAEA,SAAStD,aAAiCe,KAAUlE,OAAgB;AAC9D,SAAA,OAAOA,SAAU,WACZ;AAAA,IACL,CAACkE,GAAG,GAAGlE;AAAAA,EAAAA,IAIJ,CAAC;AACV;AAEA,SAASqD,kBAA4Ca,KAAUlE,OAAgB;AACzE,SAAA,OAAOA,SAAU,YACZ;AAAA,IACL,CAACkE,GAAG,GAAGlE;AAAAA,EAAAA,IAIP,OAAOA,SAAU,aACZ;AAAA,IACL,CAACkE,GAAG,GAAG;AAAA,EAAA,IAIJ,CAAC;AACV;AAEgBT,SAAAA,mBACdmD,WACA7E,SACuF;AACvF,MAAI6E,UAAUrE,aAAa,YAAY,CAACvD,OAAO4H,WAAW,OAAO;AAC/D,WAAO,CAAC;AAGV,QAAMC,gBAAgBD,UAAUrI,QAAQuI,KAAMlI,CAAUA,UAAAA,MAAMG,SAAS,UAAU;AAIjF,MAAI,CAAC8H;AACH,WAAO,CAAC;AAEJE,QAAAA,SAASF,cAAcvI,KAAKuG;AAClC,MAAI,CAACkC;AACH,WAAO,CAAC;AAEV,QAAMC,WAAWD,OAAOD,KAAMG,CAAeA,eAAAA,WAAWlI,SAAS,MAAM;AAGvE,MAAI,CAACiI;AACH,WAAO,CAAC;AAEJE,QAAAA,oBAAqBH,OAAO9H,OAAQgI,CAAAA,eAAeA,WAAWlI,SAAS,MAAM,KACjF,CAAyB;AAEpB,SAAA;AAAA,IACLoI,OAAO;AAAA,MACLC,aAAcJ,SAA4BI,YAAYvI,IAAKwI,OAAMjF,cAAciF,GAAGtF,OAAO,CAAC;AAAA,MAC1FuF,YAAYC,yBAAyBP,QAAQ;AAAA,IAC/C;AAAA,IACAQ,OAAOC,wBAAwBb,SAAS;AAAA,IACxCc,QAAQC,qBAAqBf,SAAS;AAAA,IACtC/B,IAAIqC,kBAAkBrI,IAAKwI,OAAMjF,cAAciF,GAAGtF,OAAO,CAAC;AAAA,EAC5D;AACF;AAEA,SAAS4F,qBAAqBf,WAAgE;AAC5F,QAAMgB,aAAahB,UAAUrI,QAAQuI,KAAMe,CAAYA,YAAAA,QAAQ9I,SAAS,OAAO;AAC/E,SAAO+I,uBAAuBF,YAAYtJ,MAAMoF,SAASqE,IAAI;AAC/D;AAEA,SAASR,yBAAyBP,UAA+D;AAC/F,SAAO,gBAAgBA,WAAWc,uBAAuBd,SAASM,UAAU,IAAI7C;AAClF;AAEA,SAASgD,wBAAwBb,WAAgE;AAC/F,QAAMoB,YAAYpB,UAAUrI,QAAQuI,KAAMe,CAAYA,YAAAA,QAAQ9I,SAAS,UAAU;AACjF,SAAO+I,uBAAuBE,WAAW1J,MAAMoF,SAASqE,IAAI;AAC9D;AAEA,SAASD,uBAAuBG,eAA2D;AACzF,MAAI,CAACA,iBAAiB,CAACvI,MAAMC,QAAQsI,aAAa;AAChD;AAEF,QAAMC,eAAeD,cAClBhJ,OACEkJ,CAA4ClI,MAAAA,SAASkI,CAAC,KAAK,CAAC,CAACA,EAAEnI,SAASG,SAASgI,EAAEnI,KAAK,CAC3F,EACCnB,IAAK2F,CACG,UAAA;AAAA,IACLxE,OAAOwE,KAAKxE;AAAAA,IACZ,GAAGmD,aAAa,SAASqB,KAAKlH,KAAK;AAAA,EAAA,EAEtC;AACH,MAAK4K,cAActI;AAIZsI,WAAAA;AACT;AAEA,MAAM3G,uBAAwBC,CAAAA,UAC5BA,MAAM3C,IAAKuJ,CAAS,SAAA;AACZ,QAAA;AAAA,IACJ9K;AAAAA,IACAyB;AAAAA,IACA1B;AAAAA,IACAgL,2BAA2B/J;AAAAA,EAAAA,IACzB8J;AACG,SAAA;AAAA,IACL9K;AAAAA,IACAyB;AAAAA,IACAT,MAAMA,QAAQ;AAAA,IACdjB,MAAMuE,YAAY;AAAA,MAChBvE;AAAAA,MACAC;AAAAA,IACD,CAAA;AAAA,EACH;AACF,CAAC,GAEGsE,cAAe0G,CAA0C,UAAA;AACvDC,QAAAA,QAAQ,IAAIC,kCAAiB;AAE/B,MAAA;AAKF,UAAMC,UAAUC,OAAeH,eAAAA,MAAMI,cAAehL,2BAAAA,IAAA,YAAA,EAAe2K,GAAAA,MAAM,CAAA,CAAG,CAAC,GAQvEM,OAAO,GAPKL,MAAMM,aAOC,CAAA,GAAGJ,OAAO,GAAGK,KAAK;AAEpCC,WAAAA,2BAAUC,SAASJ,MAAM/H,MAAM;AAAA,EAAA,QACxB;AACP,WAAA;AAAA,EAAA,UACC;AACR0H,UAAMU,KAAK;AAAA,EAAA;AAEf;ACriBA,IAAIC,oBAAAA,gBAAgB,CAACC,oBAAAA;AACb,QAAA,IAAIC,MAAM,4CAA4C;AAG9D,MAAMC,OAAOC,oBAAAA,YAEPC,UAAUC,uBAAAA,uBAAuBH,KAAKI,OAAO;AAEnD,eAAeC,OAAO;AAChB,MAAA;AACIC,UAAAA,aAAa,MAAMC,wCAAoB;AAAA,MAACnI,UAAU4H,KAAKI;AAAAA,IAAAA,CAAQ;AAErE,eAAWvI,aAAayI;AACVE,0BAAAA,YAAAA,YAAY5I,+BAA+BC,SAAS,CAAC;AAAA,EAAA,UAE3D;AACI4I,oCAAAA,SACZP,QAAQ;AAAA,EAAA;AAEZ;AAEAG,KAAK;"}
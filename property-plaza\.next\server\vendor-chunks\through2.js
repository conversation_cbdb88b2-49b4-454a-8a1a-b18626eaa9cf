/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/through2";
exports.ids = ["vendor-chunks/through2"];
exports.modules = {

/***/ "(ssr)/./node_modules/through2/through2.js":
/*!*******************************************!*\
  !*** ./node_modules/through2/through2.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { Transform } = __webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\")\n\nfunction inherits (fn, sup) {\n  fn.super_ = sup\n  fn.prototype = Object.create(sup.prototype, {\n    constructor: { value: fn, enumerable: false, writable: true, configurable: true }\n  })\n}\n\n// create a new export function, used by both the main export and\n// the .ctor export, contains common logic for dealing with arguments\nfunction through2 (construct) {\n  return (options, transform, flush) => {\n    if (typeof options === 'function') {\n      flush = transform\n      transform = options\n      options = {}\n    }\n\n    if (typeof transform !== 'function') {\n      // noop\n      transform = (chunk, enc, cb) => cb(null, chunk)\n    }\n\n    if (typeof flush !== 'function') {\n      flush = null\n    }\n\n    return construct(options, transform, flush)\n  }\n}\n\n// main export, just make me a transform stream!\nconst make = through2((options, transform, flush) => {\n  const t2 = new Transform(options)\n\n  t2._transform = transform\n\n  if (flush) {\n    t2._flush = flush\n  }\n\n  return t2\n})\n\n// make me a reusable prototype that I can `new`, or implicitly `new`\n// with a constructor call\nconst ctor = through2((options, transform, flush) => {\n  function Through2 (override) {\n    if (!(this instanceof Through2)) {\n      return new Through2(override)\n    }\n\n    this.options = Object.assign({}, options, override)\n\n    Transform.call(this, this.options)\n\n    this._transform = transform\n    if (flush) {\n      this._flush = flush\n    }\n  }\n\n  inherits(Through2, Transform)\n\n  return Through2\n})\n\nconst obj = through2(function (options, transform, flush) {\n  const t2 = new Transform(Object.assign({ objectMode: true, highWaterMark: 16 }, options))\n\n  t2._transform = transform\n\n  if (flush) {\n    t2._flush = flush\n  }\n\n  return t2\n})\n\nmodule.exports = make\nmodule.exports.ctor = ctor\nmodule.exports.obj = obj\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/through2/through2.js\n");

/***/ })

};
;
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/refractor";
exports.ids = ["vendor-chunks/refractor"];
exports.modules = {

/***/ "(ssr)/./node_modules/refractor/core.js":
/*!****************************************!*\
  !*** ./node_modules/refractor/core.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/* global window, self */\n\n// istanbul ignore next - Don't allow Prism to run on page load in browser or\n// to start messaging from workers.\nvar ctx =\n  typeof globalThis === 'object'\n    ? globalThis\n    : typeof self === 'object'\n    ? self\n    : typeof window === 'object'\n    ? window\n    : typeof global === 'object'\n    ? global\n    : {}\n\nvar restore = capture()\n\nctx.Prism = {manual: true, disableWorkerMessageHandler: true}\n\n// Load all stuff in `prism.js` itself, except for `prism-file-highlight.js`.\n// The wrapped non-leaky grammars are loaded instead of Prism’s originals.\nvar h = __webpack_require__(/*! hastscript */ \"(ssr)/./node_modules/hastscript/index.js\")\nvar decode = __webpack_require__(/*! parse-entities */ \"(ssr)/./node_modules/parse-entities/index.js\")\nvar Prism = __webpack_require__(/*! prismjs/components/prism-core */ \"(ssr)/./node_modules/prismjs/components/prism-core.js\")\nvar markup = __webpack_require__(/*! ./lang/markup */ \"(ssr)/./node_modules/refractor/lang/markup.js\")\nvar css = __webpack_require__(/*! ./lang/css */ \"(ssr)/./node_modules/refractor/lang/css.js\")\nvar clike = __webpack_require__(/*! ./lang/clike */ \"(ssr)/./node_modules/refractor/lang/clike.js\")\nvar js = __webpack_require__(/*! ./lang/javascript */ \"(ssr)/./node_modules/refractor/lang/javascript.js\")\n\nrestore()\n\nvar own = {}.hasOwnProperty\n\n// Inherit.\nfunction Refractor() {}\n\nRefractor.prototype = Prism\n\n// Construct.\nvar refract = new Refractor()\n\n// Expose.\nmodule.exports = refract\n\n// Create.\nrefract.highlight = highlight\nrefract.register = register\nrefract.alias = alias\nrefract.registered = registered\nrefract.listLanguages = listLanguages\n\n// Register bundled grammars.\nregister(markup)\nregister(css)\nregister(clike)\nregister(js)\n\nrefract.util.encode = encode\nrefract.Token.stringify = stringify\n\nfunction register(grammar) {\n  if (typeof grammar !== 'function' || !grammar.displayName) {\n    throw new Error('Expected `function` for `grammar`, got `' + grammar + '`')\n  }\n\n  // Do not duplicate registrations.\n  if (refract.languages[grammar.displayName] === undefined) {\n    grammar(refract)\n  }\n}\n\nfunction alias(name, alias) {\n  var languages = refract.languages\n  var map = name\n  var key\n  var list\n  var length\n  var index\n\n  if (alias) {\n    map = {}\n    map[name] = alias\n  }\n\n  for (key in map) {\n    list = map[key]\n    list = typeof list === 'string' ? [list] : list\n    length = list.length\n    index = -1\n\n    while (++index < length) {\n      languages[list[index]] = languages[key]\n    }\n  }\n}\n\nfunction highlight(value, name) {\n  var sup = Prism.highlight\n  var grammar\n\n  if (typeof value !== 'string') {\n    throw new Error('Expected `string` for `value`, got `' + value + '`')\n  }\n\n  // `name` is a grammar object.\n  if (refract.util.type(name) === 'Object') {\n    grammar = name\n    name = null\n  } else {\n    if (typeof name !== 'string') {\n      throw new Error('Expected `string` for `name`, got `' + name + '`')\n    }\n\n    if (own.call(refract.languages, name)) {\n      grammar = refract.languages[name]\n    } else {\n      throw new Error('Unknown language: `' + name + '` is not registered')\n    }\n  }\n\n  return sup.call(this, value, grammar, name)\n}\n\nfunction registered(language) {\n  if (typeof language !== 'string') {\n    throw new Error('Expected `string` for `language`, got `' + language + '`')\n  }\n\n  return own.call(refract.languages, language)\n}\n\nfunction listLanguages() {\n  var languages = refract.languages\n  var list = []\n  var language\n\n  for (language in languages) {\n    if (\n      own.call(languages, language) &&\n      typeof languages[language] === 'object'\n    ) {\n      list.push(language)\n    }\n  }\n\n  return list\n}\n\nfunction stringify(value, language, parent) {\n  var env\n\n  if (typeof value === 'string') {\n    return {type: 'text', value: value}\n  }\n\n  if (refract.util.type(value) === 'Array') {\n    return stringifyAll(value, language)\n  }\n\n  env = {\n    type: value.type,\n    content: refract.Token.stringify(value.content, language, parent),\n    tag: 'span',\n    classes: ['token', value.type],\n    attributes: {},\n    language: language,\n    parent: parent\n  }\n\n  if (value.alias) {\n    env.classes = env.classes.concat(value.alias)\n  }\n\n  refract.hooks.run('wrap', env)\n\n  return h(\n    env.tag + '.' + env.classes.join('.'),\n    attributes(env.attributes),\n    env.content\n  )\n}\n\nfunction stringifyAll(values, language) {\n  var result = []\n  var length = values.length\n  var index = -1\n  var value\n\n  while (++index < length) {\n    value = values[index]\n\n    if (value !== '' && value !== null && value !== undefined) {\n      result.push(value)\n    }\n  }\n\n  index = -1\n  length = result.length\n\n  while (++index < length) {\n    value = result[index]\n    result[index] = refract.Token.stringify(value, language, result)\n  }\n\n  return result\n}\n\nfunction encode(tokens) {\n  return tokens\n}\n\nfunction attributes(attrs) {\n  var key\n\n  for (key in attrs) {\n    attrs[key] = decode(attrs[key])\n  }\n\n  return attrs\n}\n\nfunction capture() {\n  var defined = 'Prism' in ctx\n  /* istanbul ignore next */\n  var current = defined ? ctx.Prism : undefined\n\n  return restore\n\n  function restore() {\n    /* istanbul ignore else - Clean leaks after Prism. */\n    if (defined) {\n      ctx.Prism = current\n    } else {\n      delete ctx.Prism\n    }\n\n    defined = undefined\n    current = undefined\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/refractor/core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/refractor/lang/clike.js":
/*!**********************************************!*\
  !*** ./node_modules/refractor/lang/clike.js ***!
  \**********************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = clike\nclike.displayName = 'clike'\nclike.aliases = []\nfunction clike(Prism) {\n  Prism.languages.clike = {\n    comment: [\n      {\n        pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n        lookbehind: true,\n        greedy: true\n      },\n      {\n        pattern: /(^|[^\\\\:])\\/\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    string: {\n      pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    'class-name': {\n      pattern:\n        /(\\b(?:class|extends|implements|instanceof|interface|new|trait)\\s+|\\bcatch\\s+\\()[\\w.\\\\]+/i,\n      lookbehind: true,\n      inside: {\n        punctuation: /[.\\\\]/\n      }\n    },\n    keyword:\n      /\\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    function: /\\b\\w+(?=\\()/,\n    number: /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n    operator: /[<>]=?|[!=]=?=?|--?|\\+\\+?|&&?|\\|\\|?|[?*/~^%]/,\n    punctuation: /[{}[\\];(),.:]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/refractor/lang/clike.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/refractor/lang/css.js":
/*!********************************************!*\
  !*** ./node_modules/refractor/lang/css.js ***!
  \********************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = css\ncss.displayName = 'css'\ncss.aliases = []\nfunction css(Prism) {\n  ;(function (Prism) {\n    var string =\n      /(?:\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n])*')/\n    Prism.languages.css = {\n      comment: /\\/\\*[\\s\\S]*?\\*\\//,\n      atrule: {\n        pattern: /@[\\w-](?:[^;{\\s]|\\s+(?![\\s{]))*(?:;|(?=\\s*\\{))/,\n        inside: {\n          rule: /^@[\\w-]+/,\n          'selector-function-argument': {\n            pattern:\n              /(\\bselector\\s*\\(\\s*(?![\\s)]))(?:[^()\\s]|\\s+(?![\\s)])|\\((?:[^()]|\\([^()]*\\))*\\))+(?=\\s*\\))/,\n            lookbehind: true,\n            alias: 'selector'\n          },\n          keyword: {\n            pattern: /(^|[^\\w-])(?:and|not|only|or)(?![\\w-])/,\n            lookbehind: true\n          } // See rest below\n        }\n      },\n      url: {\n        // https://drafts.csswg.org/css-values-3/#urls\n        pattern: RegExp(\n          '\\\\burl\\\\((?:' +\n            string.source +\n            '|' +\n            /(?:[^\\\\\\r\\n()\"']|\\\\[\\s\\S])*/.source +\n            ')\\\\)',\n          'i'\n        ),\n        greedy: true,\n        inside: {\n          function: /^url/i,\n          punctuation: /^\\(|\\)$/,\n          string: {\n            pattern: RegExp('^' + string.source + '$'),\n            alias: 'url'\n          }\n        }\n      },\n      selector: {\n        pattern: RegExp(\n          '(^|[{}\\\\s])[^{}\\\\s](?:[^{};\"\\'\\\\s]|\\\\s+(?![\\\\s{])|' +\n            string.source +\n            ')*(?=\\\\s*\\\\{)'\n        ),\n        lookbehind: true\n      },\n      string: {\n        pattern: string,\n        greedy: true\n      },\n      property: {\n        pattern:\n          /(^|[^-\\w\\xA0-\\uFFFF])(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*(?=\\s*:)/i,\n        lookbehind: true\n      },\n      important: /!important\\b/i,\n      function: {\n        pattern: /(^|[^-a-z0-9])[-a-z0-9]+(?=\\()/i,\n        lookbehind: true\n      },\n      punctuation: /[(){};:,]/\n    }\n    Prism.languages.css['atrule'].inside.rest = Prism.languages.css\n    var markup = Prism.languages.markup\n    if (markup) {\n      markup.tag.addInlined('style', 'css')\n      markup.tag.addAttribute('style', 'css')\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/refractor/lang/css.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/refractor/lang/javascript.js":
/*!***************************************************!*\
  !*** ./node_modules/refractor/lang/javascript.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = javascript\njavascript.displayName = 'javascript'\njavascript.aliases = ['js']\nfunction javascript(Prism) {\n  Prism.languages.javascript = Prism.languages.extend('clike', {\n    'class-name': [\n      Prism.languages.clike['class-name'],\n      {\n        pattern:\n          /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$A-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\.(?:constructor|prototype))/,\n        lookbehind: true\n      }\n    ],\n    keyword: [\n      {\n        pattern: /((?:^|\\})\\s*)catch\\b/,\n        lookbehind: true\n      },\n      {\n        pattern:\n          /(^|[^.]|\\.\\.\\.\\s*)\\b(?:as|assert(?=\\s*\\{)|async(?=\\s*(?:function\\b|\\(|[$\\w\\xA0-\\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\\s*(?:\\{|$))|for|from(?=\\s*(?:['\"]|$))|function|(?:get|set)(?=\\s*(?:[#\\[$\\w\\xA0-\\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\\b/,\n        lookbehind: true\n      }\n    ],\n    // Allow for all non-ASCII characters (See http://stackoverflow.com/a/2008444)\n    function:\n      /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*(?:\\.\\s*(?:apply|bind|call)\\s*)?\\()/,\n    number: {\n      pattern: RegExp(\n        /(^|[^\\w$])/.source +\n          '(?:' + // constant\n          (/NaN|Infinity/.source +\n            '|' + // binary integer\n            /0[bB][01]+(?:_[01]+)*n?/.source +\n            '|' + // octal integer\n            /0[oO][0-7]+(?:_[0-7]+)*n?/.source +\n            '|' + // hexadecimal integer\n            /0[xX][\\dA-Fa-f]+(?:_[\\dA-Fa-f]+)*n?/.source +\n            '|' + // decimal bigint\n            /\\d+(?:_\\d+)*n/.source +\n            '|' + // decimal number (integer or float) but no bigint\n            /(?:\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\.\\d+(?:_\\d+)*)(?:[Ee][+-]?\\d+(?:_\\d+)*)?/\n              .source) +\n          ')' +\n          /(?![\\w$])/.source\n      ),\n      lookbehind: true\n    },\n    operator:\n      /--|\\+\\+|\\*\\*=?|=>|&&=?|\\|\\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\\.{3}|\\?\\?=?|\\?\\.?|[~:]/\n  })\n  Prism.languages.javascript['class-name'][0].pattern =\n    /(\\b(?:class|extends|implements|instanceof|interface|new)\\s+)[\\w.\\\\]+/\n  Prism.languages.insertBefore('javascript', 'keyword', {\n    regex: {\n      // eslint-disable-next-line regexp/no-dupe-characters-character-class\n      pattern:\n        /((?:^|[^$\\w\\xA0-\\uFFFF.\"'\\])\\s]|\\b(?:return|yield))\\s*)\\/(?:\\[(?:[^\\]\\\\\\r\\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}(?=(?:\\s|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*(?:$|[\\r\\n,.;:})\\]]|\\/\\/))/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        'regex-source': {\n          pattern: /^(\\/)[\\s\\S]+(?=\\/[a-z]*$)/,\n          lookbehind: true,\n          alias: 'language-regex',\n          inside: Prism.languages.regex\n        },\n        'regex-delimiter': /^\\/|\\/$/,\n        'regex-flags': /^[a-z]+$/\n      }\n    },\n    // This must be declared before keyword because we use \"function\" inside the look-forward\n    'function-variable': {\n      pattern:\n        /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*[=:]\\s*(?:async\\s*)?(?:\\bfunction\\b|(?:\\((?:[^()]|\\([^()]*\\))*\\)|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/,\n      alias: 'function'\n    },\n    parameter: [\n      {\n        pattern:\n          /(function(?:\\s+(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)?\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\))/,\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      },\n      {\n        pattern:\n          /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=>)/i,\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      },\n      {\n        pattern:\n          /(\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*=>)/,\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      },\n      {\n        pattern:\n          /((?:\\b|\\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\\w\\xA0-\\uFFFF]))(?:(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*)\\(\\s*|\\]\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*\\{)/,\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      }\n    ],\n    constant: /\\b[A-Z](?:[A-Z_]|\\dx?)*\\b/\n  })\n  Prism.languages.insertBefore('javascript', 'string', {\n    hashbang: {\n      pattern: /^#!.*/,\n      greedy: true,\n      alias: 'comment'\n    },\n    'template-string': {\n      pattern:\n        /`(?:\\\\[\\s\\S]|\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}|(?!\\$\\{)[^\\\\`])*`/,\n      greedy: true,\n      inside: {\n        'template-punctuation': {\n          pattern: /^`|`$/,\n          alias: 'string'\n        },\n        interpolation: {\n          pattern:\n            /((?:^|[^\\\\])(?:\\\\{2})*)\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,\n          lookbehind: true,\n          inside: {\n            'interpolation-punctuation': {\n              pattern: /^\\$\\{|\\}$/,\n              alias: 'punctuation'\n            },\n            rest: Prism.languages.javascript\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    },\n    'string-property': {\n      pattern:\n        /((?:^|[,{])[ \\t]*)([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\2)[^\\\\\\r\\n])*\\2(?=\\s*:)/m,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property'\n    }\n  })\n  Prism.languages.insertBefore('javascript', 'operator', {\n    'literal-property': {\n      pattern:\n        /((?:^|[,{])[ \\t]*)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*:)/m,\n      lookbehind: true,\n      alias: 'property'\n    }\n  })\n  if (Prism.languages.markup) {\n    Prism.languages.markup.tag.addInlined('script', 'javascript') // add attribute support for all DOM events.\n    // https://developer.mozilla.org/en-US/docs/Web/Events#Standard_events\n    Prism.languages.markup.tag.addAttribute(\n      /on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/\n        .source,\n      'javascript'\n    )\n  }\n  Prism.languages.js = Prism.languages.javascript\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVmcmFjdG9yL2xhbmcvamF2YXNjcmlwdC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBLG1EQUFtRCwrSkFBK0o7QUFDbE47QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLDRFQUE0RSxFQUFFO0FBQzlFLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0SEFBNEgsSUFBSSxrREFBa0QsRUFBRTtBQUNwTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBLHFmQUFxZjtBQUNyZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLDBCQUEwQixPQUFPLElBQUksT0FBTyxJQUFJLEdBQUcsSUFBSSxJQUFJLElBQUksUUFBUTtBQUN2RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSwrQkFBK0IsRUFBRSxPQUFPLE9BQU8sSUFBSSxPQUFPLElBQUksR0FBRyxJQUFJLElBQUksSUFBSTtBQUM3RTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsR0FBRztBQUNoQztBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9yZWZyYWN0b3IvbGFuZy9qYXZhc2NyaXB0LmpzPzgyNmYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gamF2YXNjcmlwdFxuamF2YXNjcmlwdC5kaXNwbGF5TmFtZSA9ICdqYXZhc2NyaXB0J1xuamF2YXNjcmlwdC5hbGlhc2VzID0gWydqcyddXG5mdW5jdGlvbiBqYXZhc2NyaXB0KFByaXNtKSB7XG4gIFByaXNtLmxhbmd1YWdlcy5qYXZhc2NyaXB0ID0gUHJpc20ubGFuZ3VhZ2VzLmV4dGVuZCgnY2xpa2UnLCB7XG4gICAgJ2NsYXNzLW5hbWUnOiBbXG4gICAgICBQcmlzbS5sYW5ndWFnZXMuY2xpa2VbJ2NsYXNzLW5hbWUnXSxcbiAgICAgIHtcbiAgICAgICAgcGF0dGVybjpcbiAgICAgICAgICAvKF58W14kXFx3XFx4QTAtXFx1RkZGRl0pKD8hXFxzKVtfJEEtWlxceEEwLVxcdUZGRkZdKD86KD8hXFxzKVskXFx3XFx4QTAtXFx1RkZGRl0pKig/PVxcLig/OmNvbnN0cnVjdG9yfHByb3RvdHlwZSkpLyxcbiAgICAgICAgbG9va2JlaGluZDogdHJ1ZVxuICAgICAgfVxuICAgIF0sXG4gICAga2V5d29yZDogW1xuICAgICAge1xuICAgICAgICBwYXR0ZXJuOiAvKCg/Ol58XFx9KVxccyopY2F0Y2hcXGIvLFxuICAgICAgICBsb29rYmVoaW5kOiB0cnVlXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBwYXR0ZXJuOlxuICAgICAgICAgIC8oXnxbXi5dfFxcLlxcLlxcLlxccyopXFxiKD86YXN8YXNzZXJ0KD89XFxzKlxceyl8YXN5bmMoPz1cXHMqKD86ZnVuY3Rpb25cXGJ8XFwofFskXFx3XFx4QTAtXFx1RkZGRl18JCkpfGF3YWl0fGJyZWFrfGNhc2V8Y2xhc3N8Y29uc3R8Y29udGludWV8ZGVidWdnZXJ8ZGVmYXVsdHxkZWxldGV8ZG98ZWxzZXxlbnVtfGV4cG9ydHxleHRlbmRzfGZpbmFsbHkoPz1cXHMqKD86XFx7fCQpKXxmb3J8ZnJvbSg/PVxccyooPzpbJ1wiXXwkKSl8ZnVuY3Rpb258KD86Z2V0fHNldCkoPz1cXHMqKD86WyNcXFskXFx3XFx4QTAtXFx1RkZGRl18JCkpfGlmfGltcGxlbWVudHN8aW1wb3J0fGlufGluc3RhbmNlb2Z8aW50ZXJmYWNlfGxldHxuZXd8bnVsbHxvZnxwYWNrYWdlfHByaXZhdGV8cHJvdGVjdGVkfHB1YmxpY3xyZXR1cm58c3RhdGljfHN1cGVyfHN3aXRjaHx0aGlzfHRocm93fHRyeXx0eXBlb2Z8dW5kZWZpbmVkfHZhcnx2b2lkfHdoaWxlfHdpdGh8eWllbGQpXFxiLyxcbiAgICAgICAgbG9va2JlaGluZDogdHJ1ZVxuICAgICAgfVxuICAgIF0sXG4gICAgLy8gQWxsb3cgZm9yIGFsbCBub24tQVNDSUkgY2hhcmFjdGVycyAoU2VlIGh0dHA6Ly9zdGFja292ZXJmbG93LmNvbS9hLzIwMDg0NDQpXG4gICAgZnVuY3Rpb246XG4gICAgICAvIz8oPyFcXHMpW18kYS16QS1aXFx4QTAtXFx1RkZGRl0oPzooPyFcXHMpWyRcXHdcXHhBMC1cXHVGRkZGXSkqKD89XFxzKig/OlxcLlxccyooPzphcHBseXxiaW5kfGNhbGwpXFxzKik/XFwoKS8sXG4gICAgbnVtYmVyOiB7XG4gICAgICBwYXR0ZXJuOiBSZWdFeHAoXG4gICAgICAgIC8oXnxbXlxcdyRdKS8uc291cmNlICtcbiAgICAgICAgICAnKD86JyArIC8vIGNvbnN0YW50XG4gICAgICAgICAgKC9OYU58SW5maW5pdHkvLnNvdXJjZSArXG4gICAgICAgICAgICAnfCcgKyAvLyBiaW5hcnkgaW50ZWdlclxuICAgICAgICAgICAgLzBbYkJdWzAxXSsoPzpfWzAxXSspKm4/Ly5zb3VyY2UgK1xuICAgICAgICAgICAgJ3wnICsgLy8gb2N0YWwgaW50ZWdlclxuICAgICAgICAgICAgLzBbb09dWzAtN10rKD86X1swLTddKykqbj8vLnNvdXJjZSArXG4gICAgICAgICAgICAnfCcgKyAvLyBoZXhhZGVjaW1hbCBpbnRlZ2VyXG4gICAgICAgICAgICAvMFt4WF1bXFxkQS1GYS1mXSsoPzpfW1xcZEEtRmEtZl0rKSpuPy8uc291cmNlICtcbiAgICAgICAgICAgICd8JyArIC8vIGRlY2ltYWwgYmlnaW50XG4gICAgICAgICAgICAvXFxkKyg/Ol9cXGQrKSpuLy5zb3VyY2UgK1xuICAgICAgICAgICAgJ3wnICsgLy8gZGVjaW1hbCBudW1iZXIgKGludGVnZXIgb3IgZmxvYXQpIGJ1dCBubyBiaWdpbnRcbiAgICAgICAgICAgIC8oPzpcXGQrKD86X1xcZCspKig/OlxcLig/OlxcZCsoPzpfXFxkKykqKT8pP3xcXC5cXGQrKD86X1xcZCspKikoPzpbRWVdWystXT9cXGQrKD86X1xcZCspKik/L1xuICAgICAgICAgICAgICAuc291cmNlKSArXG4gICAgICAgICAgJyknICtcbiAgICAgICAgICAvKD8hW1xcdyRdKS8uc291cmNlXG4gICAgICApLFxuICAgICAgbG9va2JlaGluZDogdHJ1ZVxuICAgIH0sXG4gICAgb3BlcmF0b3I6XG4gICAgICAvLS18XFwrXFwrfFxcKlxcKj0/fD0+fCYmPT98XFx8XFx8PT98WyE9XT09fDw8PT98Pj4+Pz0/fFstKyovJSZ8XiE9PD5dPT98XFwuezN9fFxcP1xcPz0/fFxcP1xcLj98W346XS9cbiAgfSlcbiAgUHJpc20ubGFuZ3VhZ2VzLmphdmFzY3JpcHRbJ2NsYXNzLW5hbWUnXVswXS5wYXR0ZXJuID1cbiAgICAvKFxcYig/OmNsYXNzfGV4dGVuZHN8aW1wbGVtZW50c3xpbnN0YW5jZW9mfGludGVyZmFjZXxuZXcpXFxzKylbXFx3LlxcXFxdKy9cbiAgUHJpc20ubGFuZ3VhZ2VzLmluc2VydEJlZm9yZSgnamF2YXNjcmlwdCcsICdrZXl3b3JkJywge1xuICAgIHJlZ2V4OiB7XG4gICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVnZXhwL25vLWR1cGUtY2hhcmFjdGVycy1jaGFyYWN0ZXItY2xhc3NcbiAgICAgIHBhdHRlcm46XG4gICAgICAgIC8oKD86XnxbXiRcXHdcXHhBMC1cXHVGRkZGLlwiJ1xcXSlcXHNdfFxcYig/OnJldHVybnx5aWVsZCkpXFxzKilcXC8oPzpcXFsoPzpbXlxcXVxcXFxcXHJcXG5dfFxcXFwuKSpcXF18XFxcXC58W14vXFxcXFxcW1xcclxcbl0pK1xcL1tkZ2lteXVzXXswLDd9KD89KD86XFxzfFxcL1xcKig/OlteKl18XFwqKD8hXFwvKSkqXFwqXFwvKSooPzokfFtcXHJcXG4sLjs6fSlcXF1dfFxcL1xcLykpLyxcbiAgICAgIGxvb2tiZWhpbmQ6IHRydWUsXG4gICAgICBncmVlZHk6IHRydWUsXG4gICAgICBpbnNpZGU6IHtcbiAgICAgICAgJ3JlZ2V4LXNvdXJjZSc6IHtcbiAgICAgICAgICBwYXR0ZXJuOiAvXihcXC8pW1xcc1xcU10rKD89XFwvW2Etel0qJCkvLFxuICAgICAgICAgIGxvb2tiZWhpbmQ6IHRydWUsXG4gICAgICAgICAgYWxpYXM6ICdsYW5ndWFnZS1yZWdleCcsXG4gICAgICAgICAgaW5zaWRlOiBQcmlzbS5sYW5ndWFnZXMucmVnZXhcbiAgICAgICAgfSxcbiAgICAgICAgJ3JlZ2V4LWRlbGltaXRlcic6IC9eXFwvfFxcLyQvLFxuICAgICAgICAncmVnZXgtZmxhZ3MnOiAvXlthLXpdKyQvXG4gICAgICB9XG4gICAgfSxcbiAgICAvLyBUaGlzIG11c3QgYmUgZGVjbGFyZWQgYmVmb3JlIGtleXdvcmQgYmVjYXVzZSB3ZSB1c2UgXCJmdW5jdGlvblwiIGluc2lkZSB0aGUgbG9vay1mb3J3YXJkXG4gICAgJ2Z1bmN0aW9uLXZhcmlhYmxlJzoge1xuICAgICAgcGF0dGVybjpcbiAgICAgICAgLyM/KD8hXFxzKVtfJGEtekEtWlxceEEwLVxcdUZGRkZdKD86KD8hXFxzKVskXFx3XFx4QTAtXFx1RkZGRl0pKig/PVxccypbPTpdXFxzKig/OmFzeW5jXFxzKik/KD86XFxiZnVuY3Rpb25cXGJ8KD86XFwoKD86W14oKV18XFwoW14oKV0qXFwpKSpcXCl8KD8hXFxzKVtfJGEtekEtWlxceEEwLVxcdUZGRkZdKD86KD8hXFxzKVskXFx3XFx4QTAtXFx1RkZGRl0pKilcXHMqPT4pKS8sXG4gICAgICBhbGlhczogJ2Z1bmN0aW9uJ1xuICAgIH0sXG4gICAgcGFyYW1ldGVyOiBbXG4gICAgICB7XG4gICAgICAgIHBhdHRlcm46XG4gICAgICAgICAgLyhmdW5jdGlvbig/OlxccysoPyFcXHMpW18kYS16QS1aXFx4QTAtXFx1RkZGRl0oPzooPyFcXHMpWyRcXHdcXHhBMC1cXHVGRkZGXSkqKT9cXHMqXFwoXFxzKikoPyFcXHMpKD86W14oKVxcc118XFxzKyg/IVtcXHMpXSl8XFwoW14oKV0qXFwpKSsoPz1cXHMqXFwpKS8sXG4gICAgICAgIGxvb2tiZWhpbmQ6IHRydWUsXG4gICAgICAgIGluc2lkZTogUHJpc20ubGFuZ3VhZ2VzLmphdmFzY3JpcHRcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIHBhdHRlcm46XG4gICAgICAgICAgLyhefFteJFxcd1xceEEwLVxcdUZGRkZdKSg/IVxccylbXyRhLXpcXHhBMC1cXHVGRkZGXSg/Oig/IVxccylbJFxcd1xceEEwLVxcdUZGRkZdKSooPz1cXHMqPT4pL2ksXG4gICAgICAgIGxvb2tiZWhpbmQ6IHRydWUsXG4gICAgICAgIGluc2lkZTogUHJpc20ubGFuZ3VhZ2VzLmphdmFzY3JpcHRcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIHBhdHRlcm46XG4gICAgICAgICAgLyhcXChcXHMqKSg/IVxccykoPzpbXigpXFxzXXxcXHMrKD8hW1xccyldKXxcXChbXigpXSpcXCkpKyg/PVxccypcXClcXHMqPT4pLyxcbiAgICAgICAgbG9va2JlaGluZDogdHJ1ZSxcbiAgICAgICAgaW5zaWRlOiBQcmlzbS5sYW5ndWFnZXMuamF2YXNjcmlwdFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgcGF0dGVybjpcbiAgICAgICAgICAvKCg/OlxcYnxcXHN8XikoPyEoPzphc3xhc3luY3xhd2FpdHxicmVha3xjYXNlfGNhdGNofGNsYXNzfGNvbnN0fGNvbnRpbnVlfGRlYnVnZ2VyfGRlZmF1bHR8ZGVsZXRlfGRvfGVsc2V8ZW51bXxleHBvcnR8ZXh0ZW5kc3xmaW5hbGx5fGZvcnxmcm9tfGZ1bmN0aW9ufGdldHxpZnxpbXBsZW1lbnRzfGltcG9ydHxpbnxpbnN0YW5jZW9mfGludGVyZmFjZXxsZXR8bmV3fG51bGx8b2Z8cGFja2FnZXxwcml2YXRlfHByb3RlY3RlZHxwdWJsaWN8cmV0dXJufHNldHxzdGF0aWN8c3VwZXJ8c3dpdGNofHRoaXN8dGhyb3d8dHJ5fHR5cGVvZnx1bmRlZmluZWR8dmFyfHZvaWR8d2hpbGV8d2l0aHx5aWVsZCkoPyFbJFxcd1xceEEwLVxcdUZGRkZdKSkoPzooPyFcXHMpW18kYS16QS1aXFx4QTAtXFx1RkZGRl0oPzooPyFcXHMpWyRcXHdcXHhBMC1cXHVGRkZGXSkqXFxzKilcXChcXHMqfFxcXVxccypcXChcXHMqKSg/IVxccykoPzpbXigpXFxzXXxcXHMrKD8hW1xccyldKXxcXChbXigpXSpcXCkpKyg/PVxccypcXClcXHMqXFx7KS8sXG4gICAgICAgIGxvb2tiZWhpbmQ6IHRydWUsXG4gICAgICAgIGluc2lkZTogUHJpc20ubGFuZ3VhZ2VzLmphdmFzY3JpcHRcbiAgICAgIH1cbiAgICBdLFxuICAgIGNvbnN0YW50OiAvXFxiW0EtWl0oPzpbQS1aX118XFxkeD8pKlxcYi9cbiAgfSlcbiAgUHJpc20ubGFuZ3VhZ2VzLmluc2VydEJlZm9yZSgnamF2YXNjcmlwdCcsICdzdHJpbmcnLCB7XG4gICAgaGFzaGJhbmc6IHtcbiAgICAgIHBhdHRlcm46IC9eIyEuKi8sXG4gICAgICBncmVlZHk6IHRydWUsXG4gICAgICBhbGlhczogJ2NvbW1lbnQnXG4gICAgfSxcbiAgICAndGVtcGxhdGUtc3RyaW5nJzoge1xuICAgICAgcGF0dGVybjpcbiAgICAgICAgL2AoPzpcXFxcW1xcc1xcU118XFwkXFx7KD86W157fV18XFx7KD86W157fV18XFx7W159XSpcXH0pKlxcfSkrXFx9fCg/IVxcJFxceylbXlxcXFxgXSkqYC8sXG4gICAgICBncmVlZHk6IHRydWUsXG4gICAgICBpbnNpZGU6IHtcbiAgICAgICAgJ3RlbXBsYXRlLXB1bmN0dWF0aW9uJzoge1xuICAgICAgICAgIHBhdHRlcm46IC9eYHxgJC8sXG4gICAgICAgICAgYWxpYXM6ICdzdHJpbmcnXG4gICAgICAgIH0sXG4gICAgICAgIGludGVycG9sYXRpb246IHtcbiAgICAgICAgICBwYXR0ZXJuOlxuICAgICAgICAgICAgLygoPzpefFteXFxcXF0pKD86XFxcXHsyfSkqKVxcJFxceyg/Oltee31dfFxceyg/Oltee31dfFxce1tefV0qXFx9KSpcXH0pK1xcfS8sXG4gICAgICAgICAgbG9va2JlaGluZDogdHJ1ZSxcbiAgICAgICAgICBpbnNpZGU6IHtcbiAgICAgICAgICAgICdpbnRlcnBvbGF0aW9uLXB1bmN0dWF0aW9uJzoge1xuICAgICAgICAgICAgICBwYXR0ZXJuOiAvXlxcJFxce3xcXH0kLyxcbiAgICAgICAgICAgICAgYWxpYXM6ICdwdW5jdHVhdGlvbidcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICByZXN0OiBQcmlzbS5sYW5ndWFnZXMuamF2YXNjcmlwdFxuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgc3RyaW5nOiAvW1xcc1xcU10rL1xuICAgICAgfVxuICAgIH0sXG4gICAgJ3N0cmluZy1wcm9wZXJ0eSc6IHtcbiAgICAgIHBhdHRlcm46XG4gICAgICAgIC8oKD86XnxbLHtdKVsgXFx0XSopKFtcIiddKSg/OlxcXFwoPzpcXHJcXG58W1xcc1xcU10pfCg/IVxcMilbXlxcXFxcXHJcXG5dKSpcXDIoPz1cXHMqOikvbSxcbiAgICAgIGxvb2tiZWhpbmQ6IHRydWUsXG4gICAgICBncmVlZHk6IHRydWUsXG4gICAgICBhbGlhczogJ3Byb3BlcnR5J1xuICAgIH1cbiAgfSlcbiAgUHJpc20ubGFuZ3VhZ2VzLmluc2VydEJlZm9yZSgnamF2YXNjcmlwdCcsICdvcGVyYXRvcicsIHtcbiAgICAnbGl0ZXJhbC1wcm9wZXJ0eSc6IHtcbiAgICAgIHBhdHRlcm46XG4gICAgICAgIC8oKD86XnxbLHtdKVsgXFx0XSopKD8hXFxzKVtfJGEtekEtWlxceEEwLVxcdUZGRkZdKD86KD8hXFxzKVskXFx3XFx4QTAtXFx1RkZGRl0pKig/PVxccyo6KS9tLFxuICAgICAgbG9va2JlaGluZDogdHJ1ZSxcbiAgICAgIGFsaWFzOiAncHJvcGVydHknXG4gICAgfVxuICB9KVxuICBpZiAoUHJpc20ubGFuZ3VhZ2VzLm1hcmt1cCkge1xuICAgIFByaXNtLmxhbmd1YWdlcy5tYXJrdXAudGFnLmFkZElubGluZWQoJ3NjcmlwdCcsICdqYXZhc2NyaXB0JykgLy8gYWRkIGF0dHJpYnV0ZSBzdXBwb3J0IGZvciBhbGwgRE9NIGV2ZW50cy5cbiAgICAvLyBodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9FdmVudHMjU3RhbmRhcmRfZXZlbnRzXG4gICAgUHJpc20ubGFuZ3VhZ2VzLm1hcmt1cC50YWcuYWRkQXR0cmlidXRlKFxuICAgICAgL29uKD86YWJvcnR8Ymx1cnxjaGFuZ2V8Y2xpY2t8Y29tcG9zaXRpb24oPzplbmR8c3RhcnR8dXBkYXRlKXxkYmxjbGlja3xlcnJvcnxmb2N1cyg/OmlufG91dCk/fGtleSg/OmRvd258dXApfGxvYWR8bW91c2UoPzpkb3dufGVudGVyfGxlYXZlfG1vdmV8b3V0fG92ZXJ8dXApfHJlc2V0fHJlc2l6ZXxzY3JvbGx8c2VsZWN0fHNsb3RjaGFuZ2V8c3VibWl0fHVubG9hZHx3aGVlbCkvXG4gICAgICAgIC5zb3VyY2UsXG4gICAgICAnamF2YXNjcmlwdCdcbiAgICApXG4gIH1cbiAgUHJpc20ubGFuZ3VhZ2VzLmpzID0gUHJpc20ubGFuZ3VhZ2VzLmphdmFzY3JpcHRcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/refractor/lang/javascript.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/refractor/lang/markup.js":
/*!***********************************************!*\
  !*** ./node_modules/refractor/lang/markup.js ***!
  \***********************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = markup\nmarkup.displayName = 'markup'\nmarkup.aliases = ['html', 'mathml', 'svg', 'xml', 'ssml', 'atom', 'rss']\nfunction markup(Prism) {\n  Prism.languages.markup = {\n    comment: {\n      pattern: /<!--(?:(?!<!--)[\\s\\S])*?-->/,\n      greedy: true\n    },\n    prolog: {\n      pattern: /<\\?[\\s\\S]+?\\?>/,\n      greedy: true\n    },\n    doctype: {\n      // https://www.w3.org/TR/xml/#NT-doctypedecl\n      pattern:\n        /<!DOCTYPE(?:[^>\"'[\\]]|\"[^\"]*\"|'[^']*')+(?:\\[(?:[^<\"'\\]]|\"[^\"]*\"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\\]\\s*)?>/i,\n      greedy: true,\n      inside: {\n        'internal-subset': {\n          pattern: /(^[^\\[]*\\[)[\\s\\S]+(?=\\]>$)/,\n          lookbehind: true,\n          greedy: true,\n          inside: null // see below\n        },\n        string: {\n          pattern: /\"[^\"]*\"|'[^']*'/,\n          greedy: true\n        },\n        punctuation: /^<!|>$|[[\\]]/,\n        'doctype-tag': /^DOCTYPE/i,\n        name: /[^\\s<>'\"]+/\n      }\n    },\n    cdata: {\n      pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n      greedy: true\n    },\n    tag: {\n      pattern:\n        /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?\\s*\\/?>/,\n      greedy: true,\n      inside: {\n        tag: {\n          pattern: /^<\\/?[^\\s>\\/]+/,\n          inside: {\n            punctuation: /^<\\/?/,\n            namespace: /^[^\\s>\\/:]+:/\n          }\n        },\n        'special-attr': [],\n        'attr-value': {\n          pattern: /=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+)/,\n          inside: {\n            punctuation: [\n              {\n                pattern: /^=/,\n                alias: 'attr-equals'\n              },\n              /\"|'/\n            ]\n          }\n        },\n        punctuation: /\\/?>/,\n        'attr-name': {\n          pattern: /[^\\s>\\/]+/,\n          inside: {\n            namespace: /^[^\\s>\\/:]+:/\n          }\n        }\n      }\n    },\n    entity: [\n      {\n        pattern: /&[\\da-z]{1,8};/i,\n        alias: 'named-entity'\n      },\n      /&#x?[\\da-f]{1,8};/i\n    ]\n  }\n  Prism.languages.markup['tag'].inside['attr-value'].inside['entity'] =\n    Prism.languages.markup['entity']\n  Prism.languages.markup['doctype'].inside['internal-subset'].inside =\n    Prism.languages.markup // Plugin to make entity title show the real entity, idea by Roman Komarov\n  Prism.hooks.add('wrap', function (env) {\n    if (env.type === 'entity') {\n      env.attributes['title'] = env.content.value.replace(/&amp;/, '&')\n    }\n  })\n  Object.defineProperty(Prism.languages.markup.tag, 'addInlined', {\n    /**\n     * Adds an inlined language to markup.\n     *\n     * An example of an inlined language is CSS with `<style>` tags.\n     *\n     * @param {string} tagName The name of the tag that contains the inlined language. This name will be treated as\n     * case insensitive.\n     * @param {string} lang The language key.\n     * @example\n     * addInlined('style', 'css');\n     */\n    value: function addInlined(tagName, lang) {\n      var includedCdataInside = {}\n      includedCdataInside['language-' + lang] = {\n        pattern: /(^<!\\[CDATA\\[)[\\s\\S]+?(?=\\]\\]>$)/i,\n        lookbehind: true,\n        inside: Prism.languages[lang]\n      }\n      includedCdataInside['cdata'] = /^<!\\[CDATA\\[|\\]\\]>$/i\n      var inside = {\n        'included-cdata': {\n          pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n          inside: includedCdataInside\n        }\n      }\n      inside['language-' + lang] = {\n        pattern: /[\\s\\S]+/,\n        inside: Prism.languages[lang]\n      }\n      var def = {}\n      def[tagName] = {\n        pattern: RegExp(\n          /(<__[^>]*>)(?:<!\\[CDATA\\[(?:[^\\]]|\\](?!\\]>))*\\]\\]>|(?!<!\\[CDATA\\[)[\\s\\S])*?(?=<\\/__>)/.source.replace(\n            /__/g,\n            function () {\n              return tagName\n            }\n          ),\n          'i'\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: inside\n      }\n      Prism.languages.insertBefore('markup', 'cdata', def)\n    }\n  })\n  Object.defineProperty(Prism.languages.markup.tag, 'addAttribute', {\n    /**\n     * Adds an pattern to highlight languages embedded in HTML attributes.\n     *\n     * An example of an inlined language is CSS with `style` attributes.\n     *\n     * @param {string} attrName The name of the tag that contains the inlined language. This name will be treated as\n     * case insensitive.\n     * @param {string} lang The language key.\n     * @example\n     * addAttribute('style', 'css');\n     */\n    value: function (attrName, lang) {\n      Prism.languages.markup.tag.inside['special-attr'].push({\n        pattern: RegExp(\n          /(^|[\"'\\s])/.source +\n            '(?:' +\n            attrName +\n            ')' +\n            /\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))/.source,\n          'i'\n        ),\n        lookbehind: true,\n        inside: {\n          'attr-name': /^[^\\s=]+/,\n          'attr-value': {\n            pattern: /=[\\s\\S]+/,\n            inside: {\n              value: {\n                pattern: /(^=\\s*([\"']|(?![\"'])))\\S[\\s\\S]*(?=\\2$)/,\n                lookbehind: true,\n                alias: [lang, 'language-' + lang],\n                inside: Prism.languages[lang]\n              },\n              punctuation: [\n                {\n                  pattern: /^=/,\n                  alias: 'attr-equals'\n                },\n                /\"|'/\n              ]\n            }\n          }\n        }\n      })\n    }\n  })\n  Prism.languages.html = Prism.languages.markup\n  Prism.languages.mathml = Prism.languages.markup\n  Prism.languages.svg = Prism.languages.markup\n  Prism.languages.xml = Prism.languages.extend('markup', {})\n  Prism.languages.ssml = Prism.languages.xml\n  Prism.languages.atom = Prism.languages.xml\n  Prism.languages.rss = Prism.languages.xml\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/refractor/lang/markup.js\n");

/***/ })

};
;
{"version": 3, "file": "esbuild.js", "sources": ["../../../../src/_internal/cli/threads/esbuild.ts"], "sourcesContent": ["import {register} from 'esbuild-register/dist/node'\n\nif (!__DEV__) {\n  register({\n    target: `node${process.version.slice(1)}`,\n    supported: {'dynamic-import': true},\n  })\n}\n"], "names": ["register", "target", "process", "version", "slice", "supported"], "mappings": ";;AAGEA,KAAAA,SAAS;AAAA,EACPC,QAAQ,OAAOC,QAAQC,QAAQC,MAAM,CAAC,CAAC;AAAA,EACvCC,WAAW;AAAA,IAAC,kBAAkB;AAAA,EAAA;AAChC,CAAC;"}
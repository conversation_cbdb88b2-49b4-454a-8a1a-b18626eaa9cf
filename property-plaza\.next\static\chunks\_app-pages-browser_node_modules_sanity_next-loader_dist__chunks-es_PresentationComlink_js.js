"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_sanity_next-loader_dist__chunks-es_PresentationComlink_js"],{

/***/ "(app-pages-browser)/./node_modules/@sanity/next-loader/dist/_chunks-es/PresentationComlink.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@sanity/next-loader/dist/_chunks-es/PresentationComlink.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PresentationComlink; }\n/* harmony export */ });\n/* harmony import */ var _sanity_comlink__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @sanity/comlink */ \"(app-pages-browser)/./node_modules/@sanity/comlink/dist/index.js\");\n/* harmony import */ var _sanity_next_loader_server_actions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @sanity/next-loader/server-actions */ \"(app-pages-browser)/./node_modules/@sanity/next-loader/dist/server-actions.js\");\n/* harmony import */ var _sanity_presentation_comlink__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @sanity/presentation-comlink */ \"(app-pages-browser)/./node_modules/@sanity/next-loader/node_modules/@sanity/presentation-comlink/dist/index.js\");\n/* harmony import */ var next_navigation_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation.js */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var use_effect_event__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! use-effect-event */ \"(app-pages-browser)/./node_modules/use-effect-event/dist/index.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context.js */ \"(app-pages-browser)/./node_modules/@sanity/next-loader/dist/_chunks-es/context.js\");\n\n\n\n\n\n\n\nfunction PresentationComlink(props) {\n  const { projectId, dataset, draftModeEnabled, draftModePerspective } = props, router = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    (0,_context_js__WEBPACK_IMPORTED_MODULE_2__.setComlinkClientConfig)(projectId, dataset);\n  }, [dataset, projectId]);\n  const handlePerspectiveChange = (0,use_effect_event__WEBPACK_IMPORTED_MODULE_3__.useEffectEvent)(\n    (perspective, signal) => {\n      draftModeEnabled && perspective !== draftModePerspective && (0,_sanity_next_loader_server_actions__WEBPACK_IMPORTED_MODULE_4__.setPerspectiveCookie)(perspective).then(() => {\n        signal.aborted || router.refresh();\n      }).catch((reason) => console.error(\"Failed to set the preview perspective cookie\", reason));\n    }\n  );\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    const comlink = (0,_sanity_comlink__WEBPACK_IMPORTED_MODULE_5__.createNode)(\n      {\n        name: \"loaders\",\n        connectTo: \"presentation\"\n      },\n      (0,_sanity_comlink__WEBPACK_IMPORTED_MODULE_5__.createNodeMachine)().provide({\n        actors: (0,_sanity_presentation_comlink__WEBPACK_IMPORTED_MODULE_6__.createCompatibilityActors)()\n      })\n    );\n    let controller;\n    comlink.on(\"loader/perspective\", (data) => {\n      controller?.abort(), controller = new AbortController(), handlePerspectiveChange(data.perspective, controller.signal);\n    });\n    const stop = comlink.start();\n    return (0,_context_js__WEBPACK_IMPORTED_MODULE_2__.setComlink)(comlink), () => {\n      stop();\n    };\n  }, []), null;\n}\nPresentationComlink.displayName = \"PresentationComlink\";\n\n//# sourceMappingURL=PresentationComlink.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@sanity/next-loader/dist/_chunks-es/PresentationComlink.js\n"));

/***/ })

}]);
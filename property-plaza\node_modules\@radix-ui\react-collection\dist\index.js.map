{"version": 3, "sources": ["../src/index.ts", "../src/Collection.tsx"], "sourcesContent": ["'use client';\nexport { createCollection } from './Collection';\nexport type { CollectionProps } from './Collection';\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Slot } from '@radix-ui/react-slot';\n\ntype SlotProps = React.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\n// We have resorted to returning slots directly rather than exposing primitives that can then\n// be slotted like `<CollectionItem as={Slot}>…</CollectionItem>`.\n// This is because we encountered issues with generic types that cannot be statically analysed\n// due to creating them dynamically via createCollection.\n\nfunction createCollection<ItemElement extends HTMLElement, ItemData = {}>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionRef: React.RefObject<CollectionElement>;\n    itemMap: Map<React.RefObject<ItemElement>, { ref: React.RefObject<ItemElement> } & ItemData>;\n  };\n\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: new Map() }\n  );\n\n  const CollectionProvider: React.FC<{ children?: React.ReactNode; scope: any }> = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const itemMap = React.useRef<ContextValue['itemMap']>(new Map()).current;\n    return (\n      <CollectionProviderImpl scope={scope} itemMap={itemMap} collectionRef={ref}>\n        {children}\n      </CollectionProviderImpl>\n    );\n  };\n\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <Slot ref={composedRefs}>{children}</Slot>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...(itemData as unknown as ItemData) });\n        return () => void context.itemMap.delete(ref);\n      });\n\n      return (\n        <Slot {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs}>\n          {children}\n        </Slot>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const context = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current!) - orderedNodes.indexOf(b.ref.current!)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n\n    return getItems;\n  }\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,mBAAkB;AAClB,2BAAmC;AACnC,gCAAgC;AAChC,wBAAqB;AAoCf;AAvBN,SAAS,iBAAiE,MAAc;AAKtF,QAAM,gBAAgB,OAAO;AAC7B,QAAM,CAAC,yBAAyB,qBAAqB,QAAI,yCAAmB,aAAa;AAOzF,QAAM,CAAC,wBAAwB,oBAAoB,IAAI;AAAA,IACrD;AAAA,IACA,EAAE,eAAe,EAAE,SAAS,KAAK,GAAG,SAAS,oBAAI,IAAI,EAAE;AAAA,EACzD;AAEA,QAAM,qBAA2E,CAAC,UAAU;AAC1F,UAAM,EAAE,OAAO,SAAS,IAAI;AAC5B,UAAM,MAAM,aAAAA,QAAM,OAA0B,IAAI;AAChD,UAAM,UAAU,aAAAA,QAAM,OAAgC,oBAAI,IAAI,CAAC,EAAE;AACjE,WACE,4CAAC,0BAAuB,OAAc,SAAkB,eAAe,KACpE,UACH;AAAA,EAEJ;AAEA,qBAAmB,cAAc;AAMjC,QAAM,uBAAuB,OAAO;AAEpC,QAAM,iBAAiB,aAAAA,QAAM;AAAA,IAC3B,CAAC,OAAO,iBAAiB;AACvB,YAAM,EAAE,OAAO,SAAS,IAAI;AAC5B,YAAM,UAAU,qBAAqB,sBAAsB,KAAK;AAChE,YAAM,mBAAe,2CAAgB,cAAc,QAAQ,aAAa;AACxE,aAAO,4CAAC,0BAAK,KAAK,cAAe,UAAS;AAAA,IAC5C;AAAA,EACF;AAEA,iBAAe,cAAc;AAM7B,QAAM,iBAAiB,OAAO;AAC9B,QAAM,iBAAiB;AAOvB,QAAM,qBAAqB,aAAAA,QAAM;AAAA,IAC/B,CAAC,OAAO,iBAAiB;AACvB,YAAM,EAAE,OAAO,UAAU,GAAG,SAAS,IAAI;AACzC,YAAM,MAAM,aAAAA,QAAM,OAAoB,IAAI;AAC1C,YAAM,mBAAe,2CAAgB,cAAc,GAAG;AACtD,YAAM,UAAU,qBAAqB,gBAAgB,KAAK;AAE1D,mBAAAA,QAAM,UAAU,MAAM;AACpB,gBAAQ,QAAQ,IAAI,KAAK,EAAE,KAAK,GAAI,SAAiC,CAAC;AACtE,eAAO,MAAM,KAAK,QAAQ,QAAQ,OAAO,GAAG;AAAA,MAC9C,CAAC;AAED,aACE,4CAAC,0BAAM,GAAG,EAAE,CAAC,cAAc,GAAG,GAAG,GAAG,KAAK,cACtC,UACH;AAAA,IAEJ;AAAA,EACF;AAEA,qBAAmB,cAAc;AAMjC,WAAS,cAAc,OAAY;AACjC,UAAM,UAAU,qBAAqB,OAAO,sBAAsB,KAAK;AAEvE,UAAM,WAAW,aAAAA,QAAM,YAAY,MAAM;AACvC,YAAM,iBAAiB,QAAQ,cAAc;AAC7C,UAAI,CAAC,eAAgB,QAAO,CAAC;AAC7B,YAAM,eAAe,MAAM,KAAK,eAAe,iBAAiB,IAAI,cAAc,GAAG,CAAC;AACtF,YAAM,QAAQ,MAAM,KAAK,QAAQ,QAAQ,OAAO,CAAC;AACjD,YAAM,eAAe,MAAM;AAAA,QACzB,CAAC,GAAG,MAAM,aAAa,QAAQ,EAAE,IAAI,OAAQ,IAAI,aAAa,QAAQ,EAAE,IAAI,OAAQ;AAAA,MACtF;AACA,aAAO;AAAA,IACT,GAAG,CAAC,QAAQ,eAAe,QAAQ,OAAO,CAAC;AAE3C,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL,EAAE,UAAU,oBAAoB,MAAM,gBAAgB,UAAU,mBAAmB;AAAA,IACnF;AAAA,IACA;AAAA,EACF;AACF;", "names": ["React"]}
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_sanity_next-loader_node_modules_sanity_client_dist__chunks-es-c3fdbc"],{

/***/ "(app-pages-browser)/./node_modules/@sanity/next-loader/node_modules/@sanity/client/dist/_chunks-es/stegaEncodeSourceMap.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/@sanity/next-loader/node_modules/@sanity/client/dist/_chunks-es/stegaEncodeSourceMap.js ***!
  \**************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeIntoResult: function() { return /* binding */ encodeIntoResult; },\n/* harmony export */   stegaEncodeSourceMap: function() { return /* binding */ stegaEncodeSourceMap; },\n/* harmony export */   stegaEncodeSourceMap$1: function() { return /* binding */ stegaEncodeSourceMap$1; }\n/* harmony export */ });\n/* harmony import */ var _stegaClean_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stegaClean.js */ \"(app-pages-browser)/./node_modules/@sanity/next-loader/node_modules/@sanity/client/dist/_chunks-es/stegaClean.js\");\n\nconst reKeySegment = /_key\\s*==\\s*['\"](.*)['\"]/;\nfunction isKeySegment(segment) {\n  return typeof segment == \"string\" ? reKeySegment.test(segment.trim()) : typeof segment == \"object\" && \"_key\" in segment;\n}\nfunction toString(path) {\n  if (!Array.isArray(path))\n    throw new Error(\"Path is not an array\");\n  return path.reduce((target, segment, i) => {\n    const segmentType = typeof segment;\n    if (segmentType === \"number\")\n      return `${target}[${segment}]`;\n    if (segmentType === \"string\")\n      return `${target}${i === 0 ? \"\" : \".\"}${segment}`;\n    if (isKeySegment(segment) && segment._key)\n      return `${target}[_key==\"${segment._key}\"]`;\n    if (Array.isArray(segment)) {\n      const [from, to] = segment;\n      return `${target}[${from}:${to}]`;\n    }\n    throw new Error(`Unsupported path segment \\`${JSON.stringify(segment)}\\``);\n  }, \"\");\n}\nconst ESCAPE = {\n  \"\\f\": \"\\\\f\",\n  \"\\n\": \"\\\\n\",\n  \"\\r\": \"\\\\r\",\n  \"\t\": \"\\\\t\",\n  \"'\": \"\\\\'\",\n  \"\\\\\": \"\\\\\\\\\"\n}, UNESCAPE = {\n  \"\\\\f\": \"\\f\",\n  \"\\\\n\": `\n`,\n  \"\\\\r\": \"\\r\",\n  \"\\\\t\": \"\t\",\n  \"\\\\'\": \"'\",\n  \"\\\\\\\\\": \"\\\\\"\n};\nfunction jsonPath(path) {\n  return `$${path.map((segment) => typeof segment == \"string\" ? `['${segment.replace(/[\\f\\n\\r\\t'\\\\]/g, (match) => ESCAPE[match])}']` : typeof segment == \"number\" ? `[${segment}]` : segment._key !== \"\" ? `[?(@._key=='${segment._key.replace(/['\\\\]/g, (match) => ESCAPE[match])}')]` : `[${segment._index}]`).join(\"\")}`;\n}\nfunction parseJsonPath(path) {\n  const parsed = [], parseRe = /\\['(.*?)'\\]|\\[(\\d+)\\]|\\[\\?\\(@\\._key=='(.*?)'\\)\\]/g;\n  let match;\n  for (; (match = parseRe.exec(path)) !== null; ) {\n    if (match[1] !== void 0) {\n      const key = match[1].replace(/\\\\(\\\\|f|n|r|t|')/g, (m) => UNESCAPE[m]);\n      parsed.push(key);\n      continue;\n    }\n    if (match[2] !== void 0) {\n      parsed.push(parseInt(match[2], 10));\n      continue;\n    }\n    if (match[3] !== void 0) {\n      const _key = match[3].replace(/\\\\(\\\\')/g, (m) => UNESCAPE[m]);\n      parsed.push({\n        _key,\n        _index: -1\n      });\n      continue;\n    }\n  }\n  return parsed;\n}\nfunction jsonPathToStudioPath(path) {\n  return path.map((segment) => {\n    if (typeof segment == \"string\" || typeof segment == \"number\")\n      return segment;\n    if (segment._key !== \"\")\n      return { _key: segment._key };\n    if (segment._index !== -1)\n      return segment._index;\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`);\n  });\n}\nfunction jsonPathToMappingPath(path) {\n  return path.map((segment) => {\n    if (typeof segment == \"string\" || typeof segment == \"number\")\n      return segment;\n    if (segment._index !== -1)\n      return segment._index;\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`);\n  });\n}\nfunction resolveMapping(resultPath, csm) {\n  if (!csm?.mappings)\n    return;\n  const resultMappingPath = jsonPath(jsonPathToMappingPath(resultPath));\n  if (csm.mappings[resultMappingPath] !== void 0)\n    return {\n      mapping: csm.mappings[resultMappingPath],\n      matchedPath: resultMappingPath,\n      pathSuffix: \"\"\n    };\n  const mappings = Object.entries(csm.mappings).filter(([key]) => resultMappingPath.startsWith(key)).sort(([key1], [key2]) => key2.length - key1.length);\n  if (mappings.length == 0)\n    return;\n  const [matchedPath, mapping] = mappings[0], pathSuffix = resultMappingPath.substring(matchedPath.length);\n  return { mapping, matchedPath, pathSuffix };\n}\nfunction isArray(value) {\n  return value !== null && Array.isArray(value);\n}\nfunction walkMap(value, mappingFn, path = []) {\n  if (isArray(value))\n    return value.map((v, idx) => {\n      if ((0,_stegaClean_js__WEBPACK_IMPORTED_MODULE_0__.isRecord)(v)) {\n        const _key = v._key;\n        if (typeof _key == \"string\")\n          return walkMap(v, mappingFn, path.concat({ _key, _index: idx }));\n      }\n      return walkMap(v, mappingFn, path.concat(idx));\n    });\n  if ((0,_stegaClean_js__WEBPACK_IMPORTED_MODULE_0__.isRecord)(value)) {\n    if (value._type === \"block\" || value._type === \"span\") {\n      const result = { ...value };\n      return value._type === \"block\" ? result.children = walkMap(value.children, mappingFn, path.concat(\"children\")) : value._type === \"span\" && (result.text = walkMap(value.text, mappingFn, path.concat(\"text\"))), result;\n    }\n    return Object.fromEntries(\n      Object.entries(value).map(([k, v]) => [k, walkMap(v, mappingFn, path.concat(k))])\n    );\n  }\n  return mappingFn(value, path);\n}\nfunction encodeIntoResult(result, csm, encoder) {\n  return walkMap(result, (value, path) => {\n    if (typeof value != \"string\")\n      return value;\n    const resolveMappingResult = resolveMapping(path, csm);\n    if (!resolveMappingResult)\n      return value;\n    const { mapping, matchedPath } = resolveMappingResult;\n    if (mapping.type !== \"value\" || mapping.source.type !== \"documentValue\")\n      return value;\n    const sourceDocument = csm.documents[mapping.source.document], sourcePath = csm.paths[mapping.source.path], matchPathSegments = parseJsonPath(matchedPath), fullSourceSegments = parseJsonPath(sourcePath).concat(path.slice(matchPathSegments.length));\n    return encoder({\n      sourcePath: fullSourceSegments,\n      sourceDocument,\n      resultPath: path,\n      value\n    });\n  });\n}\nconst DRAFTS_FOLDER = \"drafts\", VERSION_FOLDER = \"versions\", PATH_SEPARATOR = \".\", DRAFTS_PREFIX = `${DRAFTS_FOLDER}${PATH_SEPARATOR}`, VERSION_PREFIX = `${VERSION_FOLDER}${PATH_SEPARATOR}`;\nfunction isDraftId(id) {\n  return id.startsWith(DRAFTS_PREFIX);\n}\nfunction isVersionId(id) {\n  return id.startsWith(VERSION_PREFIX);\n}\nfunction isPublishedId(id) {\n  return !isDraftId(id) && !isVersionId(id);\n}\nfunction getVersionFromId(id) {\n  if (!isVersionId(id)) return;\n  const [_versionPrefix, versionId, ..._publishedId] = id.split(PATH_SEPARATOR);\n  return versionId;\n}\nfunction getPublishedId(id) {\n  return isVersionId(id) ? id.split(PATH_SEPARATOR).slice(2).join(PATH_SEPARATOR) : isDraftId(id) ? id.slice(DRAFTS_PREFIX.length) : id;\n}\nfunction createEditUrl(options) {\n  const {\n    baseUrl,\n    workspace: _workspace = \"default\",\n    tool: _tool = \"default\",\n    id: _id,\n    type,\n    path,\n    projectId,\n    dataset\n  } = options;\n  if (!baseUrl)\n    throw new Error(\"baseUrl is required\");\n  if (!path)\n    throw new Error(\"path is required\");\n  if (!_id)\n    throw new Error(\"id is required\");\n  if (baseUrl !== \"/\" && baseUrl.endsWith(\"/\"))\n    throw new Error(\"baseUrl must not end with a slash\");\n  const workspace = _workspace === \"default\" ? void 0 : _workspace, tool = _tool === \"default\" ? void 0 : _tool, id = getPublishedId(_id), stringifiedPath = Array.isArray(path) ? toString(jsonPathToStudioPath(path)) : path, searchParams = new URLSearchParams({\n    baseUrl,\n    id,\n    type,\n    path: stringifiedPath\n  });\n  if (workspace && searchParams.set(\"workspace\", workspace), tool && searchParams.set(\"tool\", tool), projectId && searchParams.set(\"projectId\", projectId), dataset && searchParams.set(\"dataset\", dataset), isPublishedId(_id))\n    searchParams.set(\"perspective\", \"published\");\n  else if (isVersionId(_id)) {\n    const versionId = getVersionFromId(_id);\n    searchParams.set(\"perspective\", versionId);\n  }\n  const segments = [baseUrl === \"/\" ? \"\" : baseUrl];\n  workspace && segments.push(workspace);\n  const routerParams = [\n    \"mode=presentation\",\n    `id=${id}`,\n    `type=${type}`,\n    `path=${encodeURIComponent(stringifiedPath)}`\n  ];\n  return tool && routerParams.push(`tool=${tool}`), segments.push(\"intent\", \"edit\", `${routerParams.join(\";\")}?${searchParams}`), segments.join(\"/\");\n}\nfunction resolveStudioBaseRoute(studioUrl) {\n  let baseUrl = typeof studioUrl == \"string\" ? studioUrl : studioUrl.baseUrl;\n  return baseUrl !== \"/\" && (baseUrl = baseUrl.replace(/\\/$/, \"\")), typeof studioUrl == \"string\" ? { baseUrl } : { ...studioUrl, baseUrl };\n}\nconst filterDefault = ({ sourcePath, resultPath, value }) => {\n  if (isValidDate(value) || isValidURL(value))\n    return !1;\n  const endPath = sourcePath.at(-1);\n  return !(sourcePath.at(-2) === \"slug\" && endPath === \"current\" || typeof endPath == \"string\" && (endPath.startsWith(\"_\") || endPath.endsWith(\"Id\")) || sourcePath.some(\n    (path) => path === \"meta\" || path === \"metadata\" || path === \"openGraph\" || path === \"seo\"\n  ) || hasTypeLike(sourcePath) || hasTypeLike(resultPath) || typeof endPath == \"string\" && denylist.has(endPath));\n}, denylist = /* @__PURE__ */ new Set([\n  \"color\",\n  \"colour\",\n  \"currency\",\n  \"email\",\n  \"format\",\n  \"gid\",\n  \"hex\",\n  \"href\",\n  \"hsl\",\n  \"hsla\",\n  \"icon\",\n  \"id\",\n  \"index\",\n  \"key\",\n  \"language\",\n  \"layout\",\n  \"link\",\n  \"linkAction\",\n  \"locale\",\n  \"lqip\",\n  \"page\",\n  \"path\",\n  \"ref\",\n  \"rgb\",\n  \"rgba\",\n  \"route\",\n  \"secret\",\n  \"slug\",\n  \"status\",\n  \"tag\",\n  \"template\",\n  \"theme\",\n  \"type\",\n  \"textTheme\",\n  \"unit\",\n  \"url\",\n  \"username\",\n  \"variant\",\n  \"website\"\n]);\nfunction isValidDate(dateString) {\n  return /^\\d{4}-\\d{2}-\\d{2}/.test(dateString) ? !!Date.parse(dateString) : !1;\n}\nfunction isValidURL(url) {\n  try {\n    new URL(url, url.startsWith(\"/\") ? \"https://acme.com\" : void 0);\n  } catch {\n    return !1;\n  }\n  return !0;\n}\nfunction hasTypeLike(path) {\n  return path.some((segment) => typeof segment == \"string\" && segment.match(/type/i) !== null);\n}\nconst TRUNCATE_LENGTH = 20;\nfunction stegaEncodeSourceMap(result, resultSourceMap, config) {\n  const { filter, logger, enabled } = config;\n  if (!enabled) {\n    const msg = \"config.enabled must be true, don't call this function otherwise\";\n    throw logger?.error?.(`[@sanity/client]: ${msg}`, { result, resultSourceMap, config }), new TypeError(msg);\n  }\n  if (!resultSourceMap)\n    return logger?.error?.(\"[@sanity/client]: Missing Content Source Map from response body\", {\n      result,\n      resultSourceMap,\n      config\n    }), result;\n  if (!config.studioUrl) {\n    const msg = \"config.studioUrl must be defined\";\n    throw logger?.error?.(`[@sanity/client]: ${msg}`, { result, resultSourceMap, config }), new TypeError(msg);\n  }\n  const report = {\n    encoded: [],\n    skipped: []\n  }, resultWithStega = encodeIntoResult(\n    result,\n    resultSourceMap,\n    ({ sourcePath, sourceDocument, resultPath, value }) => {\n      if ((typeof filter == \"function\" ? filter({ sourcePath, resultPath, filterDefault, sourceDocument, value }) : filterDefault({ sourcePath, resultPath, value })) === !1)\n        return logger && report.skipped.push({\n          path: prettyPathForLogging(sourcePath),\n          value: `${value.slice(0, TRUNCATE_LENGTH)}${value.length > TRUNCATE_LENGTH ? \"...\" : \"\"}`,\n          length: value.length\n        }), value;\n      logger && report.encoded.push({\n        path: prettyPathForLogging(sourcePath),\n        value: `${value.slice(0, TRUNCATE_LENGTH)}${value.length > TRUNCATE_LENGTH ? \"...\" : \"\"}`,\n        length: value.length\n      });\n      const { baseUrl, workspace, tool } = resolveStudioBaseRoute(\n        typeof config.studioUrl == \"function\" ? config.studioUrl(sourceDocument) : config.studioUrl\n      );\n      if (!baseUrl) return value;\n      const { _id: id, _type: type, _projectId: projectId, _dataset: dataset } = sourceDocument;\n      return (0,_stegaClean_js__WEBPACK_IMPORTED_MODULE_0__.C)(\n        value,\n        {\n          origin: \"sanity.io\",\n          href: createEditUrl({\n            baseUrl,\n            workspace,\n            tool,\n            id,\n            type,\n            path: sourcePath,\n            ...!config.omitCrossDatasetReferenceData && { dataset, projectId }\n          })\n        },\n        // We use custom logic to determine if we should skip encoding\n        !1\n      );\n    }\n  );\n  if (logger) {\n    const isSkipping = report.skipped.length, isEncoding = report.encoded.length;\n    if ((isSkipping || isEncoding) && ((logger?.groupCollapsed || logger.log)?.(\"[@sanity/client]: Encoding source map into result\"), logger.log?.(\n      `[@sanity/client]: Paths encoded: ${report.encoded.length}, skipped: ${report.skipped.length}`\n    )), report.encoded.length > 0 && (logger?.log?.(\"[@sanity/client]: Table of encoded paths\"), (logger?.table || logger.log)?.(report.encoded)), report.skipped.length > 0) {\n      const skipped = /* @__PURE__ */ new Set();\n      for (const { path } of report.skipped)\n        skipped.add(path.replace(reKeySegment, \"0\").replace(/\\[\\d+\\]/g, \"[]\"));\n      logger?.log?.(\"[@sanity/client]: List of skipped paths\", [...skipped.values()]);\n    }\n    (isSkipping || isEncoding) && logger?.groupEnd?.();\n  }\n  return resultWithStega;\n}\nfunction prettyPathForLogging(path) {\n  return toString(jsonPathToStudioPath(path));\n}\nvar stegaEncodeSourceMap$1 = /* @__PURE__ */ Object.freeze({\n  __proto__: null,\n  stegaEncodeSourceMap\n});\n\n//# sourceMappingURL=stegaEncodeSourceMap.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@sanity/next-loader/node_modules/@sanity/client/dist/_chunks-es/stegaEncodeSourceMap.js\n"));

/***/ })

}]);
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/decompress-response";
exports.ids = ["vendor-chunks/decompress-response"];
exports.modules = {

/***/ "(ssr)/./node_modules/decompress-response/index.js":
/*!***************************************************!*\
  !*** ./node_modules/decompress-response/index.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst {Transform, PassThrough} = __webpack_require__(/*! stream */ \"stream\");\nconst zlib = __webpack_require__(/*! zlib */ \"zlib\");\nconst mimicResponse = __webpack_require__(/*! mimic-response */ \"(ssr)/./node_modules/mimic-response/index.js\");\n\nmodule.exports = response => {\n\tconst contentEncoding = (response.headers['content-encoding'] || '').toLowerCase();\n\tdelete response.headers['content-encoding'];\n\n\tif (!['gzip', 'deflate', 'br'].includes(contentEncoding)) {\n\t\treturn response;\n\t}\n\n\t// TODO: Remove this when targeting Node.js 12.\n\tconst isBrotli = contentEncoding === 'br';\n\tif (isBrotli && typeof zlib.createBrotliDecompress !== 'function') {\n\t\tresponse.destroy(new Error('Brotli is not supported on Node.js < 12'));\n\t\treturn response;\n\t}\n\n\tlet isEmpty = true;\n\n\tconst checker = new Transform({\n\t\ttransform(data, _encoding, callback) {\n\t\t\tisEmpty = false;\n\n\t\t\tcallback(null, data);\n\t\t},\n\n\t\tflush(callback) {\n\t\t\tcallback();\n\t\t}\n\t});\n\n\tconst finalStream = new PassThrough({\n\t\tautoDestroy: false,\n\t\tdestroy(error, callback) {\n\t\t\tresponse.destroy();\n\n\t\t\tcallback(error);\n\t\t}\n\t});\n\n\tconst decompressStream = isBrotli ? zlib.createBrotliDecompress() : zlib.createUnzip();\n\n\tdecompressStream.once('error', error => {\n\t\tif (isEmpty && !response.readable) {\n\t\t\tfinalStream.end();\n\t\t\treturn;\n\t\t}\n\n\t\tfinalStream.destroy(error);\n\t});\n\n\tmimicResponse(response, finalStream);\n\tresponse.pipe(checker).pipe(decompressStream).pipe(finalStream);\n\n\treturn finalStream;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/decompress-response/index.js\n");

/***/ })

};
;
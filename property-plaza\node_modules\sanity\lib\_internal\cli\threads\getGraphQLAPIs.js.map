{"version": 3, "file": "getGraphQLAPIs.js", "sources": ["../../../../src/_internal/cli/threads/getGraphQLAPIs.ts"], "sourcesContent": ["import {isMainThread, type MessagePort, parentPort, workerData} from 'node:worker_threads'\n\nimport {type CliV3CommandContext, type GraphQLAPIConfig} from '@sanity/cli'\nimport {type Schema} from '@sanity/types'\nimport {isPlainObject} from 'lodash'\nimport oneline from 'oneline'\nimport {type Workspace} from 'sanity'\n\nimport {type SchemaDefinitionish, type TypeResolvedGraphQLAPI} from '../actions/graphql/types'\nimport {getStudioWorkspaces} from '../util/getStudioWorkspaces'\n\nif (isMainThread || !parentPort) {\n  throw new Error('This module must be run as a worker thread')\n}\n\ngetGraphQLAPIsForked(parentPort)\n\nasync function getGraphQLAPIsForked(parent: MessagePort): Promise<void> {\n  const {cliConfig, cliConfigPath, workDir} = workerData\n  const resolved = await resolveGraphQLApis({cliConfig, cliConfigPath, workDir})\n  parent.postMessage(resolved)\n}\n\nasync function resolveGraphQLApis({\n  cliConfig,\n  cliConfigPath,\n  workDir,\n}: Pick<CliV3CommandContext, 'cliConfig' | 'cliConfigPath' | 'workDir'>): Promise<\n  TypeResolvedGraphQLAPI[]\n> {\n  const workspaces = await getStudioWorkspaces({basePath: workDir})\n  const numSources = workspaces.reduce(\n    (count, workspace) => count + workspace.unstable_sources.length,\n    0,\n  )\n  const multiSource = numSources > 1\n  const multiWorkspace = workspaces.length > 1\n  const hasGraphQLConfig = Boolean(cliConfig?.graphql)\n\n  if (workspaces.length === 0) {\n    throw new Error('No studio configuration found')\n  }\n\n  if (numSources === 0) {\n    throw new Error('No sources (project ID / dataset) configured')\n  }\n\n  // We can only automatically configure if there is a single workspace + source in play\n  if ((multiWorkspace || multiSource) && !hasGraphQLConfig) {\n    throw new Error(oneline`\n      Multiple workspaces/sources configured.\n      You must define an array of GraphQL APIs in ${cliConfigPath || 'sanity.cli.js'}\n      and specify which workspace/source to use.\n    `)\n  }\n\n  // No config is defined, but we have a single workspace + source, so use that\n  if (!hasGraphQLConfig) {\n    const {projectId, dataset, schema} = workspaces[0].unstable_sources[0]\n    return [{schemaTypes: getStrippedSchemaTypes(schema), projectId, dataset}]\n  }\n\n  // Explicity defined config\n  const apiDefs = validateCliConfig(cliConfig?.graphql || [])\n  return resolveGraphQLAPIsFromConfig(apiDefs, workspaces)\n}\n\nfunction resolveGraphQLAPIsFromConfig(\n  apiDefs: GraphQLAPIConfig[],\n  workspaces: Workspace[],\n): TypeResolvedGraphQLAPI[] {\n  const resolvedApis: TypeResolvedGraphQLAPI[] = []\n\n  for (const apiDef of apiDefs) {\n    const {workspace: workspaceName, source: sourceName} = apiDef\n    if (!workspaceName && workspaces.length > 1) {\n      throw new Error(\n        'Must define `workspace` name in GraphQL API config when multiple workspaces are defined',\n      )\n    }\n\n    // If we only have a single workspace defined, we can assume that is the intended one,\n    // even if no `workspace` is defined for the GraphQL API\n    const workspace =\n      !workspaceName && workspaces.length === 1\n        ? workspaces[0]\n        : workspaces.find((space) => space.name === (workspaceName || 'default'))\n\n    if (!workspace) {\n      throw new Error(`Workspace \"${workspaceName || 'default'}\" not found`)\n    }\n\n    // If we only have a single source defined, we can assume that is the intended one,\n    // even if no `source` is defined for the GraphQL API\n    const source =\n      !sourceName && workspace.unstable_sources.length === 1\n        ? workspace.unstable_sources[0]\n        : workspace.unstable_sources.find((src) => src.name === (sourceName || 'default'))\n\n    if (!source) {\n      throw new Error(\n        `Source \"${sourceName || 'default'}\" not found in workspace \"${\n          workspaceName || 'default'\n        }\"`,\n      )\n    }\n\n    resolvedApis.push({\n      ...apiDef,\n      dataset: source.dataset,\n      projectId: source.projectId,\n      schemaTypes: getStrippedSchemaTypes(source.schema),\n    })\n  }\n\n  return resolvedApis\n}\n\nfunction validateCliConfig(\n  config: GraphQLAPIConfig[],\n  configPath = 'sanity.cli.js',\n): GraphQLAPIConfig[] {\n  if (!Array.isArray(config)) {\n    throw new Error(`\"graphql\" key in \"${configPath}\" must be an array if defined`)\n  }\n\n  if (config.length === 0) {\n    throw new Error(`No GraphQL APIs defined in \"${configPath}\"`)\n  }\n\n  return config\n}\n\nfunction getStrippedSchemaTypes(schema: Schema): SchemaDefinitionish[] {\n  const schemaDef = schema._original || {types: []}\n  return schemaDef.types.map((type) => stripType(type))\n}\n\nfunction stripType(input: unknown): SchemaDefinitionish {\n  return strip(input) as SchemaDefinitionish\n}\n\nfunction strip(input: unknown): unknown {\n  if (Array.isArray(input)) {\n    return input.map((item) => strip(item)).filter((item) => typeof item !== 'undefined')\n  }\n\n  if (isPlainishObject(input)) {\n    return Object.keys(input).reduce(\n      (stripped, key) => {\n        stripped[key] = strip(input[key])\n        return stripped\n      },\n      {} as Record<string, unknown>,\n    )\n  }\n\n  return isBasicType(input) ? input : undefined\n}\n\nfunction isPlainishObject(input: unknown): input is Record<string, unknown> {\n  return isPlainObject(input)\n}\n\nfunction isBasicType(input: unknown): boolean {\n  const type = typeof input\n  if (type === 'boolean' || type === 'number' || type === 'string') {\n    return true\n  }\n\n  if (type !== 'object') {\n    return false\n  }\n\n  return Array.isArray(input) || input === null || isPlainishObject(input)\n}\n"], "names": ["isMainThread", "parentPort", "Error", "getGraphQLAPIsForked", "parent", "cliConfig", "cliConfigPath", "workDir", "workerData", "resolved", "resolveGraphQLApis", "postMessage", "workspaces", "getStudioWorkspaces", "basePath", "numSources", "reduce", "count", "workspace", "unstable_sources", "length", "multiSource", "multiWorkspace", "hasGraphQLConfig", "Boolean", "graphql", "oneline", "projectId", "dataset", "schema", "schemaTypes", "getStrippedSchemaTypes", "apiDefs", "validateCliConfig", "resolveGraphQLAPIsFromConfig", "resolvedApis", "apiDef", "workspaceName", "source", "sourceName", "find", "space", "name", "src", "push", "config", "config<PERSON><PERSON>", "Array", "isArray", "_original", "types", "map", "type", "stripType", "input", "strip", "item", "filter", "isPlainishObject", "Object", "keys", "stripped", "key", "isBasicType", "undefined", "isPlainObject"], "mappings": ";;;;;;AAWA,IAAIA,oBAAAA,gBAAgB,CAACC,oBAAAA;AACb,QAAA,IAAIC,MAAM,4CAA4C;AAG9DC,qBAAqBF,8BAAU;AAE/B,eAAeE,qBAAqBC,QAAoC;AAChE,QAAA;AAAA,IAACC;AAAAA,IAAWC;AAAAA,IAAeC;AAAAA,EAAAA,IAAWC,oBAAAA,YACtCC,WAAW,MAAMC,mBAAmB;AAAA,IAACL;AAAAA,IAAWC;AAAAA,IAAeC;AAAAA,EAAAA,CAAQ;AAC7EH,SAAOO,YAAYF,QAAQ;AAC7B;AAEA,eAAeC,mBAAmB;AAAA,EAChCL;AAAAA,EACAC;AAAAA,EACAC;AACoE,GAEpE;AACMK,QAAAA,aAAa,MAAMC,wCAAoB;AAAA,IAACC,UAAUP;AAAAA,EAAQ,CAAA,GAC1DQ,aAAaH,WAAWI,OAC5B,CAACC,OAAOC,cAAcD,QAAQC,UAAUC,iBAAiBC,QACzD,CACF,GACMC,cAAcN,aAAa,GAC3BO,iBAAiBV,WAAWQ,SAAS,GACrCG,mBAAmBC,CAAAA,CAAQnB,WAAWoB;AAE5C,MAAIb,WAAWQ,WAAW;AAClB,UAAA,IAAIlB,MAAM,+BAA+B;AAGjD,MAAIa,eAAe;AACX,UAAA,IAAIb,MAAM,8CAA8C;AAI3DoB,OAAAA,kBAAkBD,gBAAgB,CAACE;AACtC,UAAM,IAAIrB,MAAMwB,iBAAAA;AAAAA;AAAAA,oDAEgCpB,iBAAiB,eAAe;AAAA;AAAA,KAE/E;AAIH,MAAI,CAACiB,kBAAkB;AACf,UAAA;AAAA,MAACI;AAAAA,MAAWC;AAAAA,MAASC;AAAAA,IAAUjB,IAAAA,WAAW,CAAC,EAAEO,iBAAiB,CAAC;AACrE,WAAO,CAAC;AAAA,MAACW,aAAaC,uBAAuBF,MAAM;AAAA,MAAGF;AAAAA,MAAWC;AAAAA,IAAAA,CAAQ;AAAA,EAAA;AAI3E,QAAMI,UAAUC,kBAAkB5B,WAAWoB,WAAW,CAAA,CAAE;AACnDS,SAAAA,6BAA6BF,SAASpB,UAAU;AACzD;AAEA,SAASsB,6BACPF,SACApB,YAC0B;AAC1B,QAAMuB,eAAyC,CAAE;AAEjD,aAAWC,UAAUJ,SAAS;AACtB,UAAA;AAAA,MAACd,WAAWmB;AAAAA,MAAeC,QAAQC;AAAAA,IAAAA,IAAcH;AACnD,QAAA,CAACC,iBAAiBzB,WAAWQ,SAAS;AAClC,YAAA,IAAIlB,MACR,yFACF;AAKF,UAAMgB,YACJ,CAACmB,iBAAiBzB,WAAWQ,WAAW,IACpCR,WAAW,CAAC,IACZA,WAAW4B,KAAMC,CAAAA,UAAUA,MAAMC,UAAUL,iBAAiB,UAAU;AAE5E,QAAI,CAACnB;AACH,YAAM,IAAIhB,MAAM,cAAcmC,iBAAiB,SAAS,aAAa;AAKvE,UAAMC,SACJ,CAACC,cAAcrB,UAAUC,iBAAiBC,WAAW,IACjDF,UAAUC,iBAAiB,CAAC,IAC5BD,UAAUC,iBAAiBqB,KAAMG,SAAQA,IAAID,UAAUH,cAAc,UAAU;AAErF,QAAI,CAACD;AACG,YAAA,IAAIpC,MACR,WAAWqC,cAAc,SAAS,6BAChCF,iBAAiB,SAAS,GAE9B;AAGFF,iBAAaS,KAAK;AAAA,MAChB,GAAGR;AAAAA,MACHR,SAASU,OAAOV;AAAAA,MAChBD,WAAWW,OAAOX;AAAAA,MAClBG,aAAaC,uBAAuBO,OAAOT,MAAM;AAAA,IAAA,CAClD;AAAA,EAAA;AAGIM,SAAAA;AACT;AAEA,SAASF,kBACPY,QACAC,aAAa,iBACO;AAChB,MAAA,CAACC,MAAMC,QAAQH,MAAM;AACvB,UAAM,IAAI3C,MAAM,qBAAqB4C,UAAU,+BAA+B;AAGhF,MAAID,OAAOzB,WAAW;AACpB,UAAM,IAAIlB,MAAM,+BAA+B4C,UAAU,GAAG;AAGvDD,SAAAA;AACT;AAEA,SAASd,uBAAuBF,QAAuC;AAErE,UADkBA,OAAOoB,aAAa;AAAA,IAACC,OAAO,CAAA;AAAA,EAAA,GAC7BA,MAAMC,IAAKC,CAASC,SAAAA,UAAUD,IAAI,CAAC;AACtD;AAEA,SAASC,UAAUC,OAAqC;AACtD,SAAOC,MAAMD,KAAK;AACpB;AAEA,SAASC,MAAMD,OAAyB;AACtC,SAAIP,MAAMC,QAAQM,KAAK,IACdA,MAAMH,IAAKK,UAASD,MAAMC,IAAI,CAAC,EAAEC,OAAQD,UAAS,OAAOA,OAAS,GAAW,IAGlFE,iBAAiBJ,KAAK,IACjBK,OAAOC,KAAKN,KAAK,EAAEtC,OACxB,CAAC6C,UAAUC,SACTD,SAASC,GAAG,IAAIP,MAAMD,MAAMQ,GAAG,CAAC,GACzBD,WAET,CACF,CAAA,IAGKE,YAAYT,KAAK,IAAIA,QAAQU;AACtC;AAEA,SAASN,iBAAiBJ,OAAkD;AAC1E,SAAOW,uBAAAA,QAAcX,KAAK;AAC5B;AAEA,SAASS,YAAYT,OAAyB;AAC5C,QAAMF,OAAO,OAAOE;AACpB,SAAIF,SAAS,aAAaA,SAAS,YAAYA,SAAS,WAC/C,KAGLA,SAAS,WACJ,KAGFL,MAAMC,QAAQM,KAAK,KAAKA,UAAU,QAAQI,iBAAiBJ,KAAK;AACzE;"}
"use strict";
var node_worker_threads = require("node:worker_threads"), _internal = require("@sanity/schema/_internal"), getStudioWorkspaces = require("../../../_chunks-cjs/getStudioWorkspaces.js"), mockBrowserEnvironment = require("../../../_chunks-cjs/mockBrowserEnvironment.js");
if (node_worker_threads.isMainThread || !node_worker_threads.parentPort)
  throw new Error("This module must be run as a worker thread");
const opts = node_worker_threads.workerData, cleanup = mockBrowserEnvironment.mockBrowserEnvironment(opts.workDir);
async function main() {
  try {
    if (opts.format !== "groq-type-nodes")
      throw new Error(`Unsupported format: "${opts.format}"`);
    const workspaces = await getStudioWorkspaces.getStudioWorkspaces({
      basePath: opts.workDir
    }), workspace = getWorkspace({
      workspaces,
      workspaceName: opts.workspaceName
    }), schema = _internal.extractSchema(workspace.schema, {
      enforceRequiredFields: opts.enforceRequiredFields
    });
    node_worker_threads.parentPort?.postMessage({
      schema
    });
  } finally {
    cleanup();
  }
}
main();
function getWorkspace({
  workspaces,
  workspaceName
}) {
  if (workspaces.length === 0)
    throw new Error("No studio configuration found");
  if (workspaces.length === 1)
    return workspaces[0];
  if (workspaceName === void 0)
    throw new Error("Multiple workspaces found. Please specify which workspace to use with '--workspace'.");
  const workspace = workspaces.find((w) => w.name === workspaceName);
  if (!workspace)
    throw new Error(`Could not find workspace "${workspaceName}"`);
  return workspace;
}
//# sourceMappingURL=extractSchema.js.map

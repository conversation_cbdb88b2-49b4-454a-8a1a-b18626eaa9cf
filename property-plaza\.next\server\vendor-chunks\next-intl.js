"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-intl";
exports.ids = ["vendor-chunks/next-intl"];
exports.modules = {

/***/ "(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n\nexports[\"extends\"] = _extends;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7O0FBRTdEO0FBQ0E7QUFDQSxvQkFBb0Isc0JBQXNCO0FBQzFDO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUEsa0JBQWUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9kZXZlbG9wbWVudC9fdmlydHVhbC9fcm9sbHVwUGx1Z2luQmFiZWxIZWxwZXJzLmpzPzdlMTkiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG5mdW5jdGlvbiBfZXh0ZW5kcygpIHtcbiAgcmV0dXJuIF9leHRlbmRzID0gT2JqZWN0LmFzc2lnbiA/IE9iamVjdC5hc3NpZ24uYmluZCgpIDogZnVuY3Rpb24gKG4pIHtcbiAgICBmb3IgKHZhciBlID0gMTsgZSA8IGFyZ3VtZW50cy5sZW5ndGg7IGUrKykge1xuICAgICAgdmFyIHQgPSBhcmd1bWVudHNbZV07XG4gICAgICBmb3IgKHZhciByIGluIHQpICh7fSkuaGFzT3duUHJvcGVydHkuY2FsbCh0LCByKSAmJiAobltyXSA9IHRbcl0pO1xuICAgIH1cbiAgICByZXR1cm4gbjtcbiAgfSwgX2V4dGVuZHMuYXBwbHkobnVsbCwgYXJndW1lbnRzKTtcbn1cblxuZXhwb3J0cy5leHRlbmRzID0gX2V4dGVuZHM7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/index.react-client.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/index.react-client.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar index = __webpack_require__(/*! ./react-client/index.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/index.js\");\nvar useLocale = __webpack_require__(/*! ./react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar NextIntlClientProvider = __webpack_require__(/*! ./shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js\");\nvar useIntl = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/index.js\");\n\n\n\nexports.useFormatter = index.useFormatter;\nexports.useTranslations = index.useTranslations;\nexports.useLocale = useLocale.default;\nexports.NextIntlClientProvider = NextIntlClientProvider.default;\nObject.keys(useIntl).forEach(function (k) {\n\tif (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {\n\t\tenumerable: true,\n\t\tget: function () { return useIntl[k]; }\n\t});\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvaW5kZXgucmVhY3QtY2xpZW50LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QyxFQUFFLGFBQWEsRUFBQzs7QUFFN0QsWUFBWSxtQkFBTyxDQUFDLHNHQUF5QjtBQUM3QyxnQkFBZ0IsbUJBQU8sQ0FBQyw4R0FBNkI7QUFDckQsNkJBQTZCLG1CQUFPLENBQUMsNEhBQW9DO0FBQ3pFLGNBQWMsbUJBQU8sQ0FBQyw2REFBVTs7OztBQUloQyxvQkFBb0I7QUFDcEIsdUJBQXVCO0FBQ3ZCLGlCQUFpQjtBQUNqQiw4QkFBOEI7QUFDOUI7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCLEVBQUU7QUFDRixDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvaW5kZXgucmVhY3QtY2xpZW50LmpzPzA1M2IiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG52YXIgaW5kZXggPSByZXF1aXJlKCcuL3JlYWN0LWNsaWVudC9pbmRleC5qcycpO1xudmFyIHVzZUxvY2FsZSA9IHJlcXVpcmUoJy4vcmVhY3QtY2xpZW50L3VzZUxvY2FsZS5qcycpO1xudmFyIE5leHRJbnRsQ2xpZW50UHJvdmlkZXIgPSByZXF1aXJlKCcuL3NoYXJlZC9OZXh0SW50bENsaWVudFByb3ZpZGVyLmpzJyk7XG52YXIgdXNlSW50bCA9IHJlcXVpcmUoJ3VzZS1pbnRsJyk7XG5cblxuXG5leHBvcnRzLnVzZUZvcm1hdHRlciA9IGluZGV4LnVzZUZvcm1hdHRlcjtcbmV4cG9ydHMudXNlVHJhbnNsYXRpb25zID0gaW5kZXgudXNlVHJhbnNsYXRpb25zO1xuZXhwb3J0cy51c2VMb2NhbGUgPSB1c2VMb2NhbGUuZGVmYXVsdDtcbmV4cG9ydHMuTmV4dEludGxDbGllbnRQcm92aWRlciA9IE5leHRJbnRsQ2xpZW50UHJvdmlkZXIuZGVmYXVsdDtcbk9iamVjdC5rZXlzKHVzZUludGwpLmZvckVhY2goZnVuY3Rpb24gKGspIHtcblx0aWYgKGsgIT09ICdkZWZhdWx0JyAmJiAhT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGV4cG9ydHMsIGspKSBPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgaywge1xuXHRcdGVudW1lcmFibGU6IHRydWUsXG5cdFx0Z2V0OiBmdW5jdGlvbiAoKSB7IHJldHVybiB1c2VJbnRsW2tdOyB9XG5cdH0pO1xufSk7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation.react-client.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation.react-client.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar createSharedPathnamesNavigation = __webpack_require__(/*! ./navigation/react-client/createSharedPathnamesNavigation.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createSharedPathnamesNavigation.js\");\nvar createLocalizedPathnamesNavigation = __webpack_require__(/*! ./navigation/react-client/createLocalizedPathnamesNavigation.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createLocalizedPathnamesNavigation.js\");\nvar createNavigation = __webpack_require__(/*! ./navigation/react-client/createNavigation.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createNavigation.js\");\n\n\n\nexports.createSharedPathnamesNavigation = createSharedPathnamesNavigation.default;\nexports.createLocalizedPathnamesNavigation = createLocalizedPathnamesNavigation.default;\nexports.createNavigation = createNavigation.default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvbmF2aWdhdGlvbi5yZWFjdC1jbGllbnQuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsOENBQTZDLEVBQUUsYUFBYSxFQUFDOztBQUU3RCxzQ0FBc0MsbUJBQU8sQ0FBQyxnTEFBOEQ7QUFDNUcseUNBQXlDLG1CQUFPLENBQUMsc0xBQWlFO0FBQ2xILHVCQUF1QixtQkFBTyxDQUFDLGtKQUErQzs7OztBQUk5RSx1Q0FBdUM7QUFDdkMsMENBQTBDO0FBQzFDLHdCQUF3QiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2RldmVsb3BtZW50L25hdmlnYXRpb24ucmVhY3QtY2xpZW50LmpzP2FiNmMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG52YXIgY3JlYXRlU2hhcmVkUGF0aG5hbWVzTmF2aWdhdGlvbiA9IHJlcXVpcmUoJy4vbmF2aWdhdGlvbi9yZWFjdC1jbGllbnQvY3JlYXRlU2hhcmVkUGF0aG5hbWVzTmF2aWdhdGlvbi5qcycpO1xudmFyIGNyZWF0ZUxvY2FsaXplZFBhdGhuYW1lc05hdmlnYXRpb24gPSByZXF1aXJlKCcuL25hdmlnYXRpb24vcmVhY3QtY2xpZW50L2NyZWF0ZUxvY2FsaXplZFBhdGhuYW1lc05hdmlnYXRpb24uanMnKTtcbnZhciBjcmVhdGVOYXZpZ2F0aW9uID0gcmVxdWlyZSgnLi9uYXZpZ2F0aW9uL3JlYWN0LWNsaWVudC9jcmVhdGVOYXZpZ2F0aW9uLmpzJyk7XG5cblxuXG5leHBvcnRzLmNyZWF0ZVNoYXJlZFBhdGhuYW1lc05hdmlnYXRpb24gPSBjcmVhdGVTaGFyZWRQYXRobmFtZXNOYXZpZ2F0aW9uLmRlZmF1bHQ7XG5leHBvcnRzLmNyZWF0ZUxvY2FsaXplZFBhdGhuYW1lc05hdmlnYXRpb24gPSBjcmVhdGVMb2NhbGl6ZWRQYXRobmFtZXNOYXZpZ2F0aW9uLmRlZmF1bHQ7XG5leHBvcnRzLmNyZWF0ZU5hdmlnYXRpb24gPSBjcmVhdGVOYXZpZ2F0aW9uLmRlZmF1bHQ7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation.react-client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nvar LegacyBaseLink = __webpack_require__(/*! ../shared/LegacyBaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/LegacyBaseLink.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar React__default = /*#__PURE__*/_interopDefault(React);\n\nfunction ClientLink(_ref, ref) {\n  let {\n    locale,\n    localePrefix,\n    ...rest\n  } = _ref;\n  const defaultLocale = useLocale.default();\n  const finalLocale = locale || defaultLocale;\n  const prefix = utils.getLocalePrefix(finalLocale, localePrefix);\n  return /*#__PURE__*/React__default.default.createElement(LegacyBaseLink.default, _rollupPluginBabelHelpers.extends({\n    ref: ref,\n    locale: finalLocale,\n    localePrefixMode: localePrefix.mode,\n    prefix: prefix\n  }, rest));\n}\n\n/**\n * Wraps `next/link` and prefixes the `href` with the current locale if\n * necessary.\n *\n * @example\n * ```tsx\n * import {Link} from 'next-intl';\n *\n * // When the user is on `/en`, the link will point to `/en/about`\n * <Link href=\"/about\">About</Link>\n *\n * // You can override the `locale` to switch to another language\n * <Link href=\"/\" locale=\"de\">Switch to German</Link>\n * ```\n *\n * Note that when a `locale` prop is passed to switch the locale, the `prefetch`\n * prop is not supported. This is because Next.js would prefetch the page and\n * the `set-cookie` response header would cause the locale cookie on the current\n * page to be overwritten before the user even decides to change the locale.\n */\nconst ClientLinkWithRef = /*#__PURE__*/React.forwardRef(ClientLink);\nClientLinkWithRef.displayName = 'ClientLink';\n\nexports[\"default\"] = ClientLinkWithRef;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createLocalizedPathnamesNavigation.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/createLocalizedPathnamesNavigation.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar config = __webpack_require__(/*! ../../routing/config.js */ \"(ssr)/./node_modules/next-intl/dist/development/routing/config.js\");\nvar utils = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\nvar ClientLink = __webpack_require__(/*! ./ClientLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js\");\nvar redirects = __webpack_require__(/*! ./redirects.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/redirects.js\");\nvar useBasePathname = __webpack_require__(/*! ./useBasePathname.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js\");\nvar useBaseRouter = __webpack_require__(/*! ./useBaseRouter.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar React__default = /*#__PURE__*/_interopDefault(React);\n\n/**\n * @deprecated Consider switching to `createNavigation` (see https://next-intl.dev/blog/next-intl-3-22#create-navigation)\n **/\nfunction createLocalizedPathnamesNavigation(routing) {\n  const config$1 = config.receiveRoutingConfig(routing);\n  const localeCookie = config.receiveLocaleCookie(routing.localeCookie);\n  function useTypedLocale() {\n    const locale = useLocale.default();\n    const isValid = config$1.locales.includes(locale);\n    if (!isValid) {\n      throw new Error(\"Unknown locale encountered: \\\"\".concat(locale, \"\\\". Make sure to validate the locale in `i18n.ts`.\") );\n    }\n    return locale;\n  }\n  function Link(_ref, ref) {\n    let {\n      href,\n      locale,\n      ...rest\n    } = _ref;\n    const defaultLocale = useTypedLocale();\n    const finalLocale = locale || defaultLocale;\n    return /*#__PURE__*/React__default.default.createElement(ClientLink.default, _rollupPluginBabelHelpers.extends({\n      ref: ref,\n      href: utils.compileLocalizedPathname({\n        locale: finalLocale,\n        // @ts-expect-error -- This is ok\n        pathname: href,\n        // @ts-expect-error -- This is ok\n        params: typeof href === 'object' ? href.params : undefined,\n        pathnames: config$1.pathnames\n      }),\n      locale: locale,\n      localeCookie: localeCookie,\n      localePrefix: config$1.localePrefix\n    }, rest));\n  }\n  const LinkWithRef = /*#__PURE__*/React.forwardRef(Link);\n  LinkWithRef.displayName = 'Link';\n  function redirect(href) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks -- Reading from context here is fine, since `redirect` should be called during render\n    const locale = useTypedLocale();\n    const resolvedHref = getPathname({\n      href,\n      locale\n    });\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return redirects.clientRedirect({\n      pathname: resolvedHref,\n      localePrefix: config$1.localePrefix\n    }, ...args);\n  }\n  function permanentRedirect(href) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks -- Reading from context here is fine, since `redirect` should be called during render\n    const locale = useTypedLocale();\n    const resolvedHref = getPathname({\n      href,\n      locale\n    });\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n    return redirects.clientPermanentRedirect({\n      pathname: resolvedHref,\n      localePrefix: config$1.localePrefix\n    }, ...args);\n  }\n  function useRouter() {\n    const baseRouter = useBaseRouter.default(config$1.localePrefix, localeCookie);\n    const defaultLocale = useTypedLocale();\n    return React.useMemo(() => ({\n      ...baseRouter,\n      push(href) {\n        var _args$;\n        for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n          args[_key3 - 1] = arguments[_key3];\n        }\n        const resolvedHref = getPathname({\n          href,\n          locale: ((_args$ = args[0]) === null || _args$ === void 0 ? void 0 : _args$.locale) || defaultLocale\n        });\n        return baseRouter.push(resolvedHref, ...args);\n      },\n      replace(href) {\n        var _args$2;\n        for (var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {\n          args[_key4 - 1] = arguments[_key4];\n        }\n        const resolvedHref = getPathname({\n          href,\n          locale: ((_args$2 = args[0]) === null || _args$2 === void 0 ? void 0 : _args$2.locale) || defaultLocale\n        });\n        return baseRouter.replace(resolvedHref, ...args);\n      },\n      prefetch(href) {\n        var _args$3;\n        for (var _len5 = arguments.length, args = new Array(_len5 > 1 ? _len5 - 1 : 0), _key5 = 1; _key5 < _len5; _key5++) {\n          args[_key5 - 1] = arguments[_key5];\n        }\n        const resolvedHref = getPathname({\n          href,\n          locale: ((_args$3 = args[0]) === null || _args$3 === void 0 ? void 0 : _args$3.locale) || defaultLocale\n        });\n        return baseRouter.prefetch(resolvedHref, ...args);\n      }\n    }), [baseRouter, defaultLocale]);\n  }\n  function usePathname() {\n    const pathname = useBasePathname.default(config$1);\n    const locale = useTypedLocale();\n\n    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n    return React.useMemo(() => pathname ? utils.getRoute(locale, pathname, config$1.pathnames) : pathname, [locale, pathname]);\n  }\n  function getPathname(_ref2) {\n    let {\n      href,\n      locale\n    } = _ref2;\n    return utils.compileLocalizedPathname({\n      ...utils.normalizeNameOrNameWithParams(href),\n      locale,\n      pathnames: config$1.pathnames\n    });\n  }\n  return {\n    Link: LinkWithRef,\n    redirect,\n    permanentRedirect,\n    usePathname,\n    useRouter,\n    getPathname\n  };\n}\n\nexports[\"default\"] = createLocalizedPathnamesNavigation;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createLocalizedPathnamesNavigation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createNavigation.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/createNavigation.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar createSharedNavigationFns = __webpack_require__(/*! ../shared/createSharedNavigationFns.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/createSharedNavigationFns.js\");\nvar syncLocaleCookie = __webpack_require__(/*! ../shared/syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js\");\nvar utils = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\nvar useBasePathname = __webpack_require__(/*! ./useBasePathname.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js\");\n\nfunction createNavigation(routing) {\n  function useTypedLocale() {\n    return useLocale.default();\n  }\n  const {\n    Link,\n    config,\n    getPathname,\n    ...redirects\n  } = createSharedNavigationFns.default(useTypedLocale, routing);\n\n  /** @see https://next-intl.dev/docs/routing/navigation#usepathname */\n  function usePathname() {\n    const pathname = useBasePathname.default(config);\n    const locale = useTypedLocale();\n\n    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n    return React.useMemo(() => pathname &&\n    // @ts-expect-error -- This is fine\n    config.pathnames ? utils.getRoute(locale, pathname,\n    // @ts-expect-error -- This is fine\n    config.pathnames) : pathname, [locale, pathname]);\n  }\n  function useRouter() {\n    const router = navigation.useRouter();\n    const curLocale = useTypedLocale();\n    const nextPathname = navigation.usePathname();\n    return React.useMemo(() => {\n      function createHandler(fn) {\n        return function handler(href, options) {\n          const {\n            locale: nextLocale,\n            ...rest\n          } = options || {};\n\n          // @ts-expect-error -- We're passing a domain here just in case\n          const pathname = getPathname({\n            href,\n            locale: nextLocale || curLocale,\n            domain: window.location.host\n          });\n          const args = [pathname];\n          if (Object.keys(rest).length > 0) {\n            // @ts-expect-error -- This is fine\n            args.push(rest);\n          }\n          fn(...args);\n          syncLocaleCookie.default(config.localeCookie, nextPathname, curLocale, nextLocale);\n        };\n      }\n      return {\n        ...router,\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        push: createHandler(router.push),\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        replace: createHandler(router.replace),\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        prefetch: createHandler(router.prefetch)\n      };\n    }, [curLocale, nextPathname, router]);\n  }\n  return {\n    ...redirects,\n    Link,\n    usePathname,\n    useRouter,\n    getPathname\n  };\n}\n\nexports[\"default\"] = createNavigation;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createNavigation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createSharedPathnamesNavigation.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/createSharedPathnamesNavigation.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar config = __webpack_require__(/*! ../../routing/config.js */ \"(ssr)/./node_modules/next-intl/dist/development/routing/config.js\");\nvar ClientLink = __webpack_require__(/*! ./ClientLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js\");\nvar redirects = __webpack_require__(/*! ./redirects.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/redirects.js\");\nvar useBasePathname = __webpack_require__(/*! ./useBasePathname.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js\");\nvar useBaseRouter = __webpack_require__(/*! ./useBaseRouter.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar React__default = /*#__PURE__*/_interopDefault(React);\n\n/**\n * @deprecated Consider switching to `createNavigation` (see https://next-intl.dev/blog/next-intl-3-22#create-navigation)\n **/\nfunction createSharedPathnamesNavigation(routing) {\n  const localePrefix = config.receiveLocalePrefixConfig(routing === null || routing === void 0 ? void 0 : routing.localePrefix);\n  const localeCookie = config.receiveLocaleCookie(routing === null || routing === void 0 ? void 0 : routing.localeCookie);\n  function Link(props, ref) {\n    return /*#__PURE__*/React__default.default.createElement(ClientLink.default, _rollupPluginBabelHelpers.extends({\n      ref: ref,\n      localeCookie: localeCookie,\n      localePrefix: localePrefix\n    }, props));\n  }\n  const LinkWithRef = /*#__PURE__*/React.forwardRef(Link);\n  LinkWithRef.displayName = 'Link';\n  function redirect(pathname) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return redirects.clientRedirect({\n      pathname,\n      localePrefix\n    }, ...args);\n  }\n  function permanentRedirect(pathname) {\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n    return redirects.clientPermanentRedirect({\n      pathname,\n      localePrefix\n    }, ...args);\n  }\n  function usePathname() {\n    const result = useBasePathname.default({\n      localePrefix,\n      defaultLocale: routing === null || routing === void 0 ? void 0 : routing.defaultLocale\n    });\n    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n    return result;\n  }\n  function useRouter() {\n    return useBaseRouter.default(localePrefix, localeCookie);\n  }\n  return {\n    Link: LinkWithRef,\n    redirect,\n    permanentRedirect,\n    usePathname,\n    useRouter\n  };\n}\n\nexports[\"default\"] = createSharedPathnamesNavigation;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createSharedPathnamesNavigation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/redirects.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/redirects.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar redirects = __webpack_require__(/*! ../shared/redirects.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/redirects.js\");\n\nfunction createRedirectFn(redirectFn) {\n  return function clientRedirect(params) {\n    let locale;\n    try {\n      // eslint-disable-next-line react-hooks/rules-of-hooks -- Reading from context here is fine, since `redirect` should be called during render\n      locale = useLocale.default();\n    } catch (e) {\n      {\n        throw new Error('`redirect()` and `permanentRedirect()` can only be called during render. To redirect in an event handler or similar, you can use `useRouter()` instead.');\n      }\n    }\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return redirectFn({\n      ...params,\n      locale\n    }, ...args);\n  };\n}\nconst clientRedirect = createRedirectFn(redirects.baseRedirect);\nconst clientPermanentRedirect = createRedirectFn(redirects.basePermanentRedirect);\n\nexports.clientPermanentRedirect = clientPermanentRedirect;\nexports.clientRedirect = clientRedirect;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvbmF2aWdhdGlvbi9yZWFjdC1jbGllbnQvcmVkaXJlY3RzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QyxFQUFFLGFBQWEsRUFBQzs7QUFFN0QsZ0JBQWdCLG1CQUFPLENBQUMsa0hBQWlDO0FBQ3pELGdCQUFnQixtQkFBTyxDQUFDLDhHQUF3Qjs7QUFFaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkZBQTJGLGFBQWE7QUFDeEc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTs7QUFFQSwrQkFBK0I7QUFDL0Isc0JBQXNCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvbmF2aWdhdGlvbi9yZWFjdC1jbGllbnQvcmVkaXJlY3RzLmpzPzRjNmEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG52YXIgdXNlTG9jYWxlID0gcmVxdWlyZSgnLi4vLi4vcmVhY3QtY2xpZW50L3VzZUxvY2FsZS5qcycpO1xudmFyIHJlZGlyZWN0cyA9IHJlcXVpcmUoJy4uL3NoYXJlZC9yZWRpcmVjdHMuanMnKTtcblxuZnVuY3Rpb24gY3JlYXRlUmVkaXJlY3RGbihyZWRpcmVjdEZuKSB7XG4gIHJldHVybiBmdW5jdGlvbiBjbGllbnRSZWRpcmVjdChwYXJhbXMpIHtcbiAgICBsZXQgbG9jYWxlO1xuICAgIHRyeSB7XG4gICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvcnVsZXMtb2YtaG9va3MgLS0gUmVhZGluZyBmcm9tIGNvbnRleHQgaGVyZSBpcyBmaW5lLCBzaW5jZSBgcmVkaXJlY3RgIHNob3VsZCBiZSBjYWxsZWQgZHVyaW5nIHJlbmRlclxuICAgICAgbG9jYWxlID0gdXNlTG9jYWxlLmRlZmF1bHQoKTtcbiAgICB9IGNhdGNoIChlKSB7XG4gICAgICB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignYHJlZGlyZWN0KClgIGFuZCBgcGVybWFuZW50UmVkaXJlY3QoKWAgY2FuIG9ubHkgYmUgY2FsbGVkIGR1cmluZyByZW5kZXIuIFRvIHJlZGlyZWN0IGluIGFuIGV2ZW50IGhhbmRsZXIgb3Igc2ltaWxhciwgeW91IGNhbiB1c2UgYHVzZVJvdXRlcigpYCBpbnN0ZWFkLicpO1xuICAgICAgfVxuICAgIH1cbiAgICBmb3IgKHZhciBfbGVuID0gYXJndW1lbnRzLmxlbmd0aCwgYXJncyA9IG5ldyBBcnJheShfbGVuID4gMSA/IF9sZW4gLSAxIDogMCksIF9rZXkgPSAxOyBfa2V5IDwgX2xlbjsgX2tleSsrKSB7XG4gICAgICBhcmdzW19rZXkgLSAxXSA9IGFyZ3VtZW50c1tfa2V5XTtcbiAgICB9XG4gICAgcmV0dXJuIHJlZGlyZWN0Rm4oe1xuICAgICAgLi4ucGFyYW1zLFxuICAgICAgbG9jYWxlXG4gICAgfSwgLi4uYXJncyk7XG4gIH07XG59XG5jb25zdCBjbGllbnRSZWRpcmVjdCA9IGNyZWF0ZVJlZGlyZWN0Rm4ocmVkaXJlY3RzLmJhc2VSZWRpcmVjdCk7XG5jb25zdCBjbGllbnRQZXJtYW5lbnRSZWRpcmVjdCA9IGNyZWF0ZVJlZGlyZWN0Rm4ocmVkaXJlY3RzLmJhc2VQZXJtYW5lbnRSZWRpcmVjdCk7XG5cbmV4cG9ydHMuY2xpZW50UGVybWFuZW50UmVkaXJlY3QgPSBjbGllbnRQZXJtYW5lbnRSZWRpcmVjdDtcbmV4cG9ydHMuY2xpZW50UmVkaXJlY3QgPSBjbGllbnRSZWRpcmVjdDtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/redirects.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\n\nfunction useBasePathname(config) {\n  // The types aren't entirely correct here. Outside of Next.js\n  // `useParams` can be called, but the return type is `null`.\n\n  // Notes on `useNextPathname`:\n  // - Types aren't entirely correct. Outside of Next.js the\n  //   hook will return `null` (e.g. unit tests)\n  // - A base path is stripped from the result\n  // - Rewrites *are* taken into account (i.e. the pathname\n  //   that the user sees in the browser is returned)\n  const pathname = navigation.usePathname();\n  const locale = useLocale.default();\n  return React.useMemo(() => {\n    if (!pathname) return pathname;\n    let unlocalizedPathname = pathname;\n    const prefix = utils.getLocalePrefix(locale, config.localePrefix);\n    const isPathnamePrefixed = utils.hasPathnamePrefixed(prefix, pathname);\n    if (isPathnamePrefixed) {\n      unlocalizedPathname = utils.unprefixPathname(pathname, prefix);\n    } else if (config.localePrefix.mode === 'as-needed' && config.localePrefix.prefixes) {\n      // Workaround for https://github.com/vercel/next.js/issues/73085\n      const localeAsPrefix = utils.getLocaleAsPrefix(locale);\n      if (utils.hasPathnamePrefixed(localeAsPrefix, pathname)) {\n        unlocalizedPathname = utils.unprefixPathname(pathname, localeAsPrefix);\n      }\n    }\n    return unlocalizedPathname;\n  }, [config.localePrefix, locale, pathname]);\n}\n\nexports[\"default\"] = useBasePathname;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar utils$1 = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nvar syncLocaleCookie = __webpack_require__(/*! ../shared/syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js\");\nvar utils = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\n/**\n * Returns a wrapped instance of `useRouter` from `next/navigation` that\n * will automatically localize the `href` parameters it receives.\n *\n * @example\n * ```tsx\n * 'use client';\n *\n * import {useRouter} from 'next-intl/client';\n *\n * const router = useRouter();\n *\n * // When the user is on `/en`, the router will navigate to `/en/about`\n * router.push('/about');\n *\n * // Optionally, you can switch the locale by passing the second argument\n * router.push('/about', {locale: 'de'});\n * ```\n */ function useBaseRouter(localePrefix, localeCookie) {\n    const router = navigation.useRouter();\n    const locale = useLocale.default();\n    const pathname = navigation.usePathname();\n    return React.useMemo(()=>{\n        function localize(href, nextLocale) {\n            let curPathname = window.location.pathname;\n            const basePath = utils.getBasePath(pathname);\n            if (basePath) curPathname = curPathname.replace(basePath, \"\");\n            const targetLocale = nextLocale || locale;\n            // We generate a prefix in any case, but decide\n            // in `localizeHref` if we apply it or not\n            const prefix = utils$1.getLocalePrefix(targetLocale, localePrefix);\n            return utils$1.localizeHref(href, targetLocale, locale, curPathname, prefix);\n        }\n        function createHandler(fn) {\n            return function handler(href, options) {\n                const { locale: nextLocale, ...rest } = options || {};\n                syncLocaleCookie.default(localeCookie, pathname, locale, nextLocale);\n                const args = [\n                    localize(href, nextLocale)\n                ];\n                if (Object.keys(rest).length > 0) {\n                    args.push(rest);\n                }\n                // @ts-expect-error -- This is ok\n                return fn(...args);\n            };\n        }\n        return {\n            ...router,\n            push: createHandler(router.push),\n            replace: createHandler(router.replace),\n            prefetch: createHandler(router.prefetch)\n        };\n    }, [\n        locale,\n        localeCookie,\n        localePrefix,\n        pathname,\n        router\n    ]);\n}\nexports[\"default\"] = useBaseRouter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar NextLink = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar syncLocaleCookie = __webpack_require__(/*! ./syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar NextLink__default = /*#__PURE__*/ _interopDefault(NextLink);\nvar React__default = /*#__PURE__*/ _interopDefault(React);\nfunction BaseLink(_ref, ref) {\n    let { defaultLocale, href, locale, localeCookie, onClick, prefetch, unprefixed, ...rest } = _ref;\n    const curLocale = useLocale.default();\n    const isChangingLocale = locale != null && locale !== curLocale;\n    const linkLocale = locale || curLocale;\n    const host = useHost();\n    const finalHref = // Only after hydration (to avoid mismatches)\n    host && // If there is an `unprefixed` prop, the\n    // `defaultLocale` might differ by domain\n    unprefixed && // Unprefix the pathname if a domain matches\n    (unprefixed.domains[host] === linkLocale || // … and handle unknown domains by applying the\n    // global `defaultLocale` (e.g. on localhost)\n    !Object.keys(unprefixed.domains).includes(host) && curLocale === defaultLocale && !locale) ? unprefixed.pathname : href;\n    // The types aren't entirely correct here. Outside of Next.js\n    // `useParams` can be called, but the return type is `null`.\n    const pathname = navigation.usePathname();\n    function onLinkClick(event) {\n        syncLocaleCookie.default(localeCookie, pathname, curLocale, locale);\n        if (onClick) onClick(event);\n    }\n    if (isChangingLocale) {\n        if (prefetch && \"development\" !== \"production\") {\n            console.error(\"The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`\");\n        }\n        prefetch = false;\n    }\n    return /*#__PURE__*/ React__default.default.createElement(NextLink__default.default, _rollupPluginBabelHelpers.extends({\n        ref: ref,\n        href: finalHref,\n        hrefLang: isChangingLocale ? locale : undefined,\n        onClick: onLinkClick,\n        prefetch: prefetch\n    }, rest));\n}\nfunction useHost() {\n    const [host, setHost] = React.useState();\n    React.useEffect(()=>{\n        setHost(window.location.host);\n    }, []);\n    return host;\n}\nvar BaseLink$1 = /*#__PURE__*/ React.forwardRef(BaseLink);\nexports[\"default\"] = BaseLink$1;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/LegacyBaseLink.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/LegacyBaseLink.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nvar BaseLink = __webpack_require__(/*! ./BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefault(React);\nfunction LegacyBaseLink(_ref, ref) {\n    let { href, locale, localeCookie, localePrefixMode, prefix, ...rest } = _ref;\n    // The types aren't entirely correct here. Outside of Next.js\n    // `useParams` can be called, but the return type is `null`.\n    const pathname = navigation.usePathname();\n    const curLocale = useLocale.default();\n    const isChangingLocale = locale !== curLocale;\n    const [localizedHref, setLocalizedHref] = React.useState(()=>utils.isLocalizableHref(href) && (localePrefixMode !== \"never\" || isChangingLocale) ? // For the `localePrefix: 'as-needed' strategy, the href shouldn't\n        // be prefixed if the locale is the default locale. To determine this, we\n        // need a) the default locale and b) the information if we use prefixed\n        // routing. The default locale can vary by domain, therefore during the\n        // RSC as well as the SSR render, we can't determine the default locale\n        // statically. Therefore we always prefix the href since this will\n        // always result in a valid URL, even if it might cause a redirect. This\n        // is better than pointing to a non-localized href during the server\n        // render, which would potentially be wrong. The final href is\n        // determined in the effect below.\n        utils.prefixHref(href, prefix) : href);\n    React.useEffect(()=>{\n        if (!pathname) return;\n        setLocalizedHref(utils.localizeHref(href, locale, curLocale, pathname, prefix));\n    }, [\n        curLocale,\n        href,\n        locale,\n        pathname,\n        prefix\n    ]);\n    return /*#__PURE__*/ React__default.default.createElement(BaseLink.default, _rollupPluginBabelHelpers.extends({\n        ref: ref,\n        href: localizedHref,\n        locale: locale,\n        localeCookie: localeCookie\n    }, rest));\n}\nconst LegacyBaseLinkWithRef = /*#__PURE__*/ React.forwardRef(LegacyBaseLink);\nLegacyBaseLinkWithRef.displayName = \"ClientLink\";\nexports[\"default\"] = LegacyBaseLinkWithRef;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/LegacyBaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/createSharedNavigationFns.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/createSharedNavigationFns.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar config = __webpack_require__(/*! ../../routing/config.js */ \"(ssr)/./node_modules/next-intl/dist/development/routing/config.js\");\nvar utils$1 = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nvar BaseLink = __webpack_require__(/*! ./BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js\");\nvar utils = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar React__default = /*#__PURE__*/_interopDefault(React);\n\n/**\n * Shared implementations for `react-server` and `react-client`\n */\nfunction createSharedNavigationFns(getLocale, routing) {\n  const config$1 = config.receiveRoutingConfig(routing || {});\n  {\n    utils.validateReceivedConfig(config$1);\n  }\n  const pathnames = config$1.pathnames;\n\n  // This combination requires that the current host is known in order to\n  // compute a correct pathname. Since that can only be achieved by reading from\n  // headers, this would break static rendering. Therefore, as a workaround we\n  // always add a prefix in this case to be on the safe side. The downside is\n  // that the user might get redirected again if the middleware detects that the\n  // prefix is not needed.\n  const forcePrefixSsr = config$1.localePrefix.mode === 'as-needed' && config$1.domains || undefined;\n  function Link(_ref, ref) {\n    let {\n      href,\n      locale,\n      ...rest\n    } = _ref;\n    let pathname, params, query;\n    if (typeof href === 'object') {\n      pathname = href.pathname;\n      query = href.query;\n      // @ts-expect-error -- This is ok\n      params = href.params;\n    } else {\n      pathname = href;\n    }\n\n    // @ts-expect-error -- This is ok\n    const isLocalizable = utils$1.isLocalizableHref(href);\n    const localePromiseOrValue = getLocale();\n    const curLocale = utils$1.isPromise(localePromiseOrValue) ? React.use(localePromiseOrValue) : localePromiseOrValue;\n    const finalPathname = isLocalizable ? getPathname(\n    // @ts-expect-error -- This is ok\n    {\n      locale: locale || curLocale,\n      href: pathnames == null ? pathname : {\n        pathname,\n        params\n      }\n    }, locale != null || forcePrefixSsr || undefined) : pathname;\n    return /*#__PURE__*/React__default.default.createElement(BaseLink.default, _rollupPluginBabelHelpers.extends({\n      ref: ref\n      // @ts-expect-error -- Available after the validation\n      ,\n      defaultLocale: config$1.defaultLocale\n      // @ts-expect-error -- This is ok\n      ,\n      href: typeof href === 'object' ? {\n        ...href,\n        pathname: finalPathname\n      } : finalPathname,\n      locale: locale,\n      localeCookie: config$1.localeCookie\n      // Provide the minimal relevant information to the client side in order\n      // to potentially remove the prefix in case of the `forcePrefixSsr` case\n      ,\n      unprefixed: forcePrefixSsr && isLocalizable ? {\n        domains: config$1.domains.reduce((acc, domain) => {\n          // @ts-expect-error -- This is ok\n          acc[domain.domain] = domain.defaultLocale;\n          return acc;\n        }, {}),\n        pathname: getPathname(\n        // @ts-expect-error -- This is ok\n        {\n          locale: curLocale,\n          href: pathnames == null ? {\n            pathname,\n            query\n          } : {\n            pathname,\n            query,\n            params\n          }\n        }, false)\n      } : undefined\n    }, rest));\n  }\n  const LinkWithRef = /*#__PURE__*/React.forwardRef(Link);\n  function getPathname(args, /** @private Removed in types returned below */\n  _forcePrefix) {\n    const {\n      href,\n      locale\n    } = args;\n    let pathname;\n    if (pathnames == null) {\n      if (typeof href === 'object') {\n        pathname = href.pathname;\n        if (href.query) {\n          pathname += utils.serializeSearchParams(href.query);\n        }\n      } else {\n        pathname = href;\n      }\n    } else {\n      pathname = utils.compileLocalizedPathname({\n        locale,\n        // @ts-expect-error -- This is ok\n        ...utils.normalizeNameOrNameWithParams(href),\n        // @ts-expect-error -- This is ok\n        pathnames: config$1.pathnames\n      });\n    }\n    return utils.applyPathnamePrefix(pathname, locale, config$1,\n    // @ts-expect-error -- This is ok\n    args.domain, _forcePrefix);\n  }\n  function getRedirectFn(fn) {\n    /** @see https://next-intl.dev/docs/routing/navigation#redirect */\n    return function redirectFn(args) {\n      for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        rest[_key - 1] = arguments[_key];\n      }\n      return fn(\n      // @ts-expect-error -- We're forcing the prefix when no domain is provided\n      getPathname(args, args.domain ? undefined : forcePrefixSsr), ...rest);\n    };\n  }\n  const redirect = getRedirectFn(navigation.redirect);\n  const permanentRedirect = getRedirectFn(navigation.permanentRedirect);\n  return {\n    config: config$1,\n    Link: LinkWithRef,\n    redirect,\n    permanentRedirect,\n    // Remove `_forcePrefix` from public API\n    getPathname: getPathname\n  };\n}\n\nexports[\"default\"] = createSharedNavigationFns;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/createSharedNavigationFns.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/redirects.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/redirects.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\n\nfunction createRedirectFn(redirectFn) {\n  return function baseRedirect(params) {\n    const prefix = utils.getLocalePrefix(params.locale, params.localePrefix);\n\n    // This logic is considered legacy and is replaced by `applyPathnamePrefix`.\n    // We keep it this way for now for backwards compatibility.\n    const localizedPathname = params.localePrefix.mode === 'never' || !utils.isLocalizableHref(params.pathname) ? params.pathname : utils.prefixPathname(prefix, params.pathname);\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return redirectFn(localizedPathname, ...args);\n  };\n}\nconst baseRedirect = createRedirectFn(navigation.redirect);\nconst basePermanentRedirect = createRedirectFn(navigation.permanentRedirect);\n\nexports.basePermanentRedirect = basePermanentRedirect;\nexports.baseRedirect = baseRedirect;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/redirects.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar utils = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\n\n/**\n * We have to keep the cookie value in sync as Next.js might\n * skip a request to the server due to its router cache.\n * See https://github.com/amannn/next-intl/issues/786.\n */\nfunction syncLocaleCookie(localeCookie, pathname, locale, nextLocale) {\n  const isSwitchingLocale = nextLocale !== locale && nextLocale != null;\n  if (!localeCookie || !isSwitchingLocale ||\n  // Theoretical case, we always have a pathname in a real app,\n  // only not when running e.g. in a simulated test environment\n  !pathname) {\n    return;\n  }\n  const basePath = utils.getBasePath(pathname);\n  const hasBasePath = basePath !== '';\n  const defaultPath = hasBasePath ? basePath : '/';\n  const {\n    name,\n    ...rest\n  } = localeCookie;\n  if (!rest.path) {\n    rest.path = defaultPath;\n  }\n  let localeCookieString = \"\".concat(name, \"=\").concat(nextLocale, \";\");\n  for (const [key, value] of Object.entries(rest)) {\n    // Map object properties to cookie properties.\n    // Interestingly, `maxAge` corresponds to `max-age`,\n    // while `sameSite` corresponds to `SameSite`.\n    // Also, keys are case-insensitive.\n    const targetKey = key === 'maxAge' ? 'max-age' : key;\n    localeCookieString += \"\".concat(targetKey);\n    if (typeof value !== 'boolean') {\n      localeCookieString += '=' + value;\n    }\n\n    // A trailing \";\" is allowed by browsers\n    localeCookieString += ';';\n  }\n\n  // Note that writing to `document.cookie` doesn't overwrite all\n  // cookies, but only the ones referenced via the name here.\n  document.cookie = localeCookieString;\n}\n\nexports[\"default\"] = syncLocaleCookie;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/utils.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\n\n// Minor false positive: A route that has both optional and\n// required params will allow optional params.\n\n// For `Link`\n\n// For `getPathname` (hence also its consumers: `redirect`, `useRouter`, …)\n\nfunction normalizeNameOrNameWithParams(href) {\n  return typeof href === 'string' ? {\n    pathname: href\n  } : href;\n}\nfunction serializeSearchParams(searchParams) {\n  function serializeValue(value) {\n    return String(value);\n  }\n  const urlSearchParams = new URLSearchParams();\n  for (const [key, value] of Object.entries(searchParams)) {\n    if (Array.isArray(value)) {\n      value.forEach(cur => {\n        urlSearchParams.append(key, serializeValue(cur));\n      });\n    } else {\n      urlSearchParams.set(key, serializeValue(value));\n    }\n  }\n  return '?' + urlSearchParams.toString();\n}\nfunction compileLocalizedPathname(_ref) {\n  let {\n    pathname,\n    locale,\n    params,\n    pathnames,\n    query\n  } = _ref;\n  function getNamedPath(value) {\n    let namedPath = pathnames[value];\n    if (!namedPath) {\n      // Unknown pathnames\n      namedPath = value;\n    }\n    return namedPath;\n  }\n  function compilePath(namedPath) {\n    const template = typeof namedPath === 'string' ? namedPath : namedPath[locale];\n    let compiled = template;\n    if (params) {\n      Object.entries(params).forEach(_ref2 => {\n        let [key, value] = _ref2;\n        let regexp, replacer;\n        if (Array.isArray(value)) {\n          regexp = \"(\\\\[)?\\\\[...\".concat(key, \"\\\\](\\\\])?\");\n          replacer = value.map(v => String(v)).join('/');\n        } else {\n          regexp = \"\\\\[\".concat(key, \"\\\\]\");\n          replacer = String(value);\n        }\n        compiled = compiled.replace(new RegExp(regexp, 'g'), replacer);\n      });\n    }\n\n    // Clean up optional catch-all segments that were not replaced\n    compiled = compiled.replace(/\\[\\[\\.\\.\\..+\\]\\]/g, '');\n    compiled = utils.normalizeTrailingSlash(compiled);\n    if (compiled.includes('[')) {\n      // Next.js throws anyway, therefore better provide a more helpful error message\n      throw new Error(\"Insufficient params provided for localized pathname.\\nTemplate: \".concat(template, \"\\nParams: \").concat(JSON.stringify(params)));\n    }\n    if (query) {\n      compiled += serializeSearchParams(query);\n    }\n    return compiled;\n  }\n  if (typeof pathname === 'string') {\n    const namedPath = getNamedPath(pathname);\n    const compiled = compilePath(namedPath);\n    return compiled;\n  } else {\n    const {\n      pathname: href,\n      ...rest\n    } = pathname;\n    const namedPath = getNamedPath(href);\n    const compiled = compilePath(namedPath);\n    const result = {\n      ...rest,\n      pathname: compiled\n    };\n    return result;\n  }\n}\nfunction getRoute(locale, pathname, pathnames) {\n  const sortedPathnames = utils.getSortedPathnames(Object.keys(pathnames));\n  const decoded = decodeURI(pathname);\n  for (const internalPathname of sortedPathnames) {\n    const localizedPathnamesOrPathname = pathnames[internalPathname];\n    if (typeof localizedPathnamesOrPathname === 'string') {\n      const localizedPathname = localizedPathnamesOrPathname;\n      if (utils.matchesPathname(localizedPathname, decoded)) {\n        return internalPathname;\n      }\n    } else {\n      if (utils.matchesPathname(localizedPathnamesOrPathname[locale], decoded)) {\n        return internalPathname;\n      }\n    }\n  }\n  return pathname;\n}\nfunction getBasePath(pathname) {\n  let windowPathname = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : window.location.pathname;\n  if (pathname === '/') {\n    return windowPathname;\n  } else {\n    return windowPathname.replace(pathname, '');\n  }\n}\nfunction applyPathnamePrefix(pathname, locale, routing, domain, force) {\n  const {\n    mode\n  } = routing.localePrefix;\n  let shouldPrefix;\n  if (force !== undefined) {\n    shouldPrefix = force;\n  } else if (utils.isLocalizableHref(pathname)) {\n    if (mode === 'always') {\n      shouldPrefix = true;\n    } else if (mode === 'as-needed') {\n      let defaultLocale = routing.defaultLocale;\n      if (routing.domains) {\n        const domainConfig = routing.domains.find(cur => cur.domain === domain);\n        if (domainConfig) {\n          defaultLocale = domainConfig.defaultLocale;\n        } else {\n          if (!domain) {\n            console.error(\"You're using a routing configuration with `localePrefix: 'as-needed'` in combination with `domains`. In order to compute a correct pathname, you need to provide a `domain` parameter.\\n\\nSee: https://next-intl.dev/docs/routing#domains-localeprefix-asneeded\");\n          }\n        }\n      }\n      shouldPrefix = defaultLocale !== locale;\n    }\n  }\n  return shouldPrefix ? utils.prefixPathname(utils.getLocalePrefix(locale, routing.localePrefix), pathname) : pathname;\n}\nfunction validateReceivedConfig(config) {\n  var _config$localePrefix;\n  if (((_config$localePrefix = config.localePrefix) === null || _config$localePrefix === void 0 ? void 0 : _config$localePrefix.mode) === 'as-needed' && !('defaultLocale' in config)) {\n    throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\");\n  }\n}\n\nexports.applyPathnamePrefix = applyPathnamePrefix;\nexports.compileLocalizedPathname = compileLocalizedPathname;\nexports.getBasePath = getBasePath;\nexports.getRoute = getRoute;\nexports.normalizeNameOrNameWithParams = normalizeNameOrNameWithParams;\nexports.serializeSearchParams = serializeSearchParams;\nexports.validateReceivedConfig = validateReceivedConfig;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/react-client/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/react-client/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar useIntl = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/index.js\");\n\n/**\n * This is the main entry file when non-'react-server'\n * environments import from 'next-intl'.\n *\n * Maintainer notes:\n * - Make sure this mirrors the API from 'react-server'.\n * - Make sure everything exported from this module is\n *   supported in all Next.js versions that are supported.\n */\n\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nfunction callHook(name, hook) {\n  return function () {\n    try {\n      return hook(...arguments);\n    } catch (_unused) {\n      throw new Error(\"Failed to call `\".concat(name, \"` because the context from `NextIntlClientProvider` was not found.\\n\\nThis can happen because:\\n1) You intended to render this component as a Server Component, the render\\n   failed, and therefore React attempted to render the component on the client\\n   instead. If this is the case, check the console for server errors.\\n2) You intended to render this component on the client side, but no context was found.\\n   Learn more about this error here: https://next-intl.dev/docs/environments/server-client-components#missing-context\") );\n    }\n  };\n}\nconst useTranslations = callHook('useTranslations', useIntl.useTranslations);\nconst useFormatter = callHook('useFormatter', useIntl.useFormatter);\n\nexports.useFormatter = useFormatter;\nexports.useTranslations = useTranslations;\nObject.keys(useIntl).forEach(function (k) {\n  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {\n    enumerable: true,\n    get: function () { return useIntl[k]; }\n  });\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/react-client/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js":
/*!***************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/react-client/useLocale.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar _useLocale = __webpack_require__(/*! use-intl/_useLocale */ \"(ssr)/./node_modules/use-intl/dist/_useLocale.js\");\nvar constants = __webpack_require__(/*! ../shared/constants.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/constants.js\");\n\nlet hasWarnedForParams = false;\nfunction useLocale() {\n  // The types aren't entirely correct here. Outside of Next.js\n  // `useParams` can be called, but the return type is `null`.\n  const params = navigation.useParams();\n  let locale;\n  try {\n    // eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/rules-of-hooks, react-compiler/react-compiler -- False positive\n    locale = _useLocale.useLocale();\n  } catch (error) {\n    if (typeof (params === null || params === void 0 ? void 0 : params[constants.LOCALE_SEGMENT_NAME]) === 'string') {\n      if (!hasWarnedForParams) {\n        console.warn('Deprecation warning: `useLocale` has returned a default from `useParams().locale` since no `NextIntlClientProvider` ancestor was found for the calling component. This behavior will be removed in the next major version. Please ensure all Client Components that use `next-intl` are wrapped in a `NextIntlClientProvider`.');\n        hasWarnedForParams = true;\n      }\n      locale = params[constants.LOCALE_SEGMENT_NAME];\n    } else {\n      throw error;\n    }\n  }\n  return locale;\n}\n\nexports[\"default\"] = useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/routing/config.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/routing/config.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction receiveRoutingConfig(input) {\n  var _input$localeDetectio, _input$alternateLinks;\n  return {\n    ...input,\n    localePrefix: receiveLocalePrefixConfig(input.localePrefix),\n    localeCookie: receiveLocaleCookie(input.localeCookie),\n    localeDetection: (_input$localeDetectio = input.localeDetection) !== null && _input$localeDetectio !== void 0 ? _input$localeDetectio : true,\n    alternateLinks: (_input$alternateLinks = input.alternateLinks) !== null && _input$alternateLinks !== void 0 ? _input$alternateLinks : true\n  };\n}\nfunction receiveLocaleCookie(localeCookie) {\n  return (localeCookie !== null && localeCookie !== void 0 ? localeCookie : true) ? {\n    name: 'NEXT_LOCALE',\n    maxAge: ********,\n    // 1 year\n    sameSite: 'lax',\n    ...(typeof localeCookie === 'object' && localeCookie)\n\n    // `path` needs to be provided based on a detected base path\n    // that depends on the environment when setting a cookie\n  } : false;\n}\nfunction receiveLocalePrefixConfig(localePrefix) {\n  return typeof localePrefix === 'object' ? localePrefix : {\n    mode: localePrefix || 'always'\n  };\n}\n\nexports.receiveLocaleCookie = receiveLocaleCookie;\nexports.receiveLocalePrefixConfig = receiveLocalePrefixConfig;\nexports.receiveRoutingConfig = receiveRoutingConfig;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/routing/config.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/server.react-client.js":
/*!************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/server.react-client.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar index = __webpack_require__(/*! ./server/react-client/index.js */ \"(ssr)/./node_modules/next-intl/dist/development/server/react-client/index.js\");\n\n\n\nexports.getFormatter = index.getFormatter;\nexports.getLocale = index.getLocale;\nexports.getMessages = index.getMessages;\nexports.getNow = index.getNow;\nexports.getRequestConfig = index.getRequestConfig;\nexports.getTimeZone = index.getTimeZone;\nexports.getTranslations = index.getTranslations;\nexports.setRequestLocale = index.setRequestLocale;\nexports.unstable_setRequestLocale = index.unstable_setRequestLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvc2VydmVyLnJlYWN0LWNsaWVudC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7O0FBRTdELFlBQVksbUJBQU8sQ0FBQyxvSEFBZ0M7Ozs7QUFJcEQsb0JBQW9CO0FBQ3BCLGlCQUFpQjtBQUNqQixtQkFBbUI7QUFDbkIsY0FBYztBQUNkLHdCQUF3QjtBQUN4QixtQkFBbUI7QUFDbkIsdUJBQXVCO0FBQ3ZCLHdCQUF3QjtBQUN4QixpQ0FBaUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9kZXZlbG9wbWVudC9zZXJ2ZXIucmVhY3QtY2xpZW50LmpzP2I5ZGEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG52YXIgaW5kZXggPSByZXF1aXJlKCcuL3NlcnZlci9yZWFjdC1jbGllbnQvaW5kZXguanMnKTtcblxuXG5cbmV4cG9ydHMuZ2V0Rm9ybWF0dGVyID0gaW5kZXguZ2V0Rm9ybWF0dGVyO1xuZXhwb3J0cy5nZXRMb2NhbGUgPSBpbmRleC5nZXRMb2NhbGU7XG5leHBvcnRzLmdldE1lc3NhZ2VzID0gaW5kZXguZ2V0TWVzc2FnZXM7XG5leHBvcnRzLmdldE5vdyA9IGluZGV4LmdldE5vdztcbmV4cG9ydHMuZ2V0UmVxdWVzdENvbmZpZyA9IGluZGV4LmdldFJlcXVlc3RDb25maWc7XG5leHBvcnRzLmdldFRpbWVab25lID0gaW5kZXguZ2V0VGltZVpvbmU7XG5leHBvcnRzLmdldFRyYW5zbGF0aW9ucyA9IGluZGV4LmdldFRyYW5zbGF0aW9ucztcbmV4cG9ydHMuc2V0UmVxdWVzdExvY2FsZSA9IGluZGV4LnNldFJlcXVlc3RMb2NhbGU7XG5leHBvcnRzLnVuc3RhYmxlX3NldFJlcXVlc3RMb2NhbGUgPSBpbmRleC51bnN0YWJsZV9zZXRSZXF1ZXN0TG9jYWxlO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/server.react-client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/server/react-client/index.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/server/react-client/index.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\n/**\n * Allows to import `next-intl/server` in non-RSC environments.\n *\n * This is mostly relevant for testing, since e.g. a `generateMetadata`\n * export from a page might use `next-intl/server`, but the test\n * only uses the default export for a page.\n */\n\nfunction notSupported(message) {\n  return () => {\n    throw new Error(\"`\".concat(message, \"` is not supported in Client Components.\"));\n  };\n}\nfunction getRequestConfig() {\n  return notSupported('getRequestConfig');\n}\nconst getFormatter = notSupported('getFormatter');\nconst getNow = notSupported('getNow');\nconst getTimeZone = notSupported('getTimeZone');\nconst getMessages = notSupported('getMessages');\nconst getLocale = notSupported('getLocale');\n\n// The type of `getTranslations` is not assigned here because it\n// causes a type error. The types use the `react-server` entry\n// anyway, therefore this is irrelevant.\nconst getTranslations = notSupported('getTranslations');\nconst unstable_setRequestLocale = notSupported('unstable_setRequestLocale');\nconst setRequestLocale = notSupported('setRequestLocale');\n\nexports.getFormatter = getFormatter;\nexports.getLocale = getLocale;\nexports.getMessages = getMessages;\nexports.getNow = getNow;\nexports.getRequestConfig = getRequestConfig;\nexports.getTimeZone = getTimeZone;\nexports.getTranslations = getTranslations;\nexports.setRequestLocale = setRequestLocale;\nexports.unstable_setRequestLocale = unstable_setRequestLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvc2VydmVyL3JlYWN0LWNsaWVudC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7O0FBRTdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxvQkFBb0I7QUFDcEIsaUJBQWlCO0FBQ2pCLG1CQUFtQjtBQUNuQixjQUFjO0FBQ2Qsd0JBQXdCO0FBQ3hCLG1CQUFtQjtBQUNuQix1QkFBdUI7QUFDdkIsd0JBQXdCO0FBQ3hCLGlDQUFpQyIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1jbGllbnQvaW5kZXguanM/M2I2MyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAnX19lc01vZHVsZScsIHsgdmFsdWU6IHRydWUgfSk7XG5cbi8qKlxuICogQWxsb3dzIHRvIGltcG9ydCBgbmV4dC1pbnRsL3NlcnZlcmAgaW4gbm9uLVJTQyBlbnZpcm9ubWVudHMuXG4gKlxuICogVGhpcyBpcyBtb3N0bHkgcmVsZXZhbnQgZm9yIHRlc3RpbmcsIHNpbmNlIGUuZy4gYSBgZ2VuZXJhdGVNZXRhZGF0YWBcbiAqIGV4cG9ydCBmcm9tIGEgcGFnZSBtaWdodCB1c2UgYG5leHQtaW50bC9zZXJ2ZXJgLCBidXQgdGhlIHRlc3RcbiAqIG9ubHkgdXNlcyB0aGUgZGVmYXVsdCBleHBvcnQgZm9yIGEgcGFnZS5cbiAqL1xuXG5mdW5jdGlvbiBub3RTdXBwb3J0ZWQobWVzc2FnZSkge1xuICByZXR1cm4gKCkgPT4ge1xuICAgIHRocm93IG5ldyBFcnJvcihcImBcIi5jb25jYXQobWVzc2FnZSwgXCJgIGlzIG5vdCBzdXBwb3J0ZWQgaW4gQ2xpZW50IENvbXBvbmVudHMuXCIpKTtcbiAgfTtcbn1cbmZ1bmN0aW9uIGdldFJlcXVlc3RDb25maWcoKSB7XG4gIHJldHVybiBub3RTdXBwb3J0ZWQoJ2dldFJlcXVlc3RDb25maWcnKTtcbn1cbmNvbnN0IGdldEZvcm1hdHRlciA9IG5vdFN1cHBvcnRlZCgnZ2V0Rm9ybWF0dGVyJyk7XG5jb25zdCBnZXROb3cgPSBub3RTdXBwb3J0ZWQoJ2dldE5vdycpO1xuY29uc3QgZ2V0VGltZVpvbmUgPSBub3RTdXBwb3J0ZWQoJ2dldFRpbWVab25lJyk7XG5jb25zdCBnZXRNZXNzYWdlcyA9IG5vdFN1cHBvcnRlZCgnZ2V0TWVzc2FnZXMnKTtcbmNvbnN0IGdldExvY2FsZSA9IG5vdFN1cHBvcnRlZCgnZ2V0TG9jYWxlJyk7XG5cbi8vIFRoZSB0eXBlIG9mIGBnZXRUcmFuc2xhdGlvbnNgIGlzIG5vdCBhc3NpZ25lZCBoZXJlIGJlY2F1c2UgaXRcbi8vIGNhdXNlcyBhIHR5cGUgZXJyb3IuIFRoZSB0eXBlcyB1c2UgdGhlIGByZWFjdC1zZXJ2ZXJgIGVudHJ5XG4vLyBhbnl3YXksIHRoZXJlZm9yZSB0aGlzIGlzIGlycmVsZXZhbnQuXG5jb25zdCBnZXRUcmFuc2xhdGlvbnMgPSBub3RTdXBwb3J0ZWQoJ2dldFRyYW5zbGF0aW9ucycpO1xuY29uc3QgdW5zdGFibGVfc2V0UmVxdWVzdExvY2FsZSA9IG5vdFN1cHBvcnRlZCgndW5zdGFibGVfc2V0UmVxdWVzdExvY2FsZScpO1xuY29uc3Qgc2V0UmVxdWVzdExvY2FsZSA9IG5vdFN1cHBvcnRlZCgnc2V0UmVxdWVzdExvY2FsZScpO1xuXG5leHBvcnRzLmdldEZvcm1hdHRlciA9IGdldEZvcm1hdHRlcjtcbmV4cG9ydHMuZ2V0TG9jYWxlID0gZ2V0TG9jYWxlO1xuZXhwb3J0cy5nZXRNZXNzYWdlcyA9IGdldE1lc3NhZ2VzO1xuZXhwb3J0cy5nZXROb3cgPSBnZXROb3c7XG5leHBvcnRzLmdldFJlcXVlc3RDb25maWcgPSBnZXRSZXF1ZXN0Q29uZmlnO1xuZXhwb3J0cy5nZXRUaW1lWm9uZSA9IGdldFRpbWVab25lO1xuZXhwb3J0cy5nZXRUcmFuc2xhdGlvbnMgPSBnZXRUcmFuc2xhdGlvbnM7XG5leHBvcnRzLnNldFJlcXVlc3RMb2NhbGUgPSBzZXRSZXF1ZXN0TG9jYWxlO1xuZXhwb3J0cy51bnN0YWJsZV9zZXRSZXF1ZXN0TG9jYWxlID0gdW5zdGFibGVfc2V0UmVxdWVzdExvY2FsZTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/server/react-client/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar _IntlProvider = __webpack_require__(/*! use-intl/_IntlProvider */ \"(ssr)/./node_modules/use-intl/dist/_IntlProvider.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefault(React);\nfunction NextIntlClientProvider(_ref) {\n    let { locale, ...rest } = _ref;\n    // TODO: We could call `useParams` here to receive a default value\n    // for `locale`, but this would require dropping Next.js <13.\n    if (!locale) {\n        throw new Error(\"Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\\n\\nSee https://next-intl.dev/docs/configuration#locale\");\n    }\n    return /*#__PURE__*/ React__default.default.createElement(_IntlProvider.IntlProvider, _rollupPluginBabelHelpers.extends({\n        locale: locale\n    }, rest));\n}\nexports[\"default\"] = NextIntlClientProvider;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/shared/constants.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/shared/constants.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\n// Should take precedence over the cookie\nconst HEADER_LOCALE_NAME = 'X-NEXT-INTL-LOCALE';\n\n// In a URL like \"/en-US/about\", the locale segment is \"en-US\"\nconst LOCALE_SEGMENT_NAME = 'locale';\n\nexports.HEADER_LOCALE_NAME = HEADER_LOCALE_NAME;\nexports.LOCALE_SEGMENT_NAME = LOCALE_SEGMENT_NAME;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvc2hhcmVkL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7O0FBRTdEO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSwwQkFBMEI7QUFDMUIsMkJBQTJCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvc2hhcmVkL2NvbnN0YW50cy5qcz9jNTIxIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICdfX2VzTW9kdWxlJywgeyB2YWx1ZTogdHJ1ZSB9KTtcblxuLy8gU2hvdWxkIHRha2UgcHJlY2VkZW5jZSBvdmVyIHRoZSBjb29raWVcbmNvbnN0IEhFQURFUl9MT0NBTEVfTkFNRSA9ICdYLU5FWFQtSU5UTC1MT0NBTEUnO1xuXG4vLyBJbiBhIFVSTCBsaWtlIFwiL2VuLVVTL2Fib3V0XCIsIHRoZSBsb2NhbGUgc2VnbWVudCBpcyBcImVuLVVTXCJcbmNvbnN0IExPQ0FMRV9TRUdNRU5UX05BTUUgPSAnbG9jYWxlJztcblxuZXhwb3J0cy5IRUFERVJfTE9DQUxFX05BTUUgPSBIRUFERVJfTE9DQUxFX05BTUU7XG5leHBvcnRzLkxPQ0FMRV9TRUdNRU5UX05BTUUgPSBMT0NBTEVfU0VHTUVOVF9OQU1FO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/shared/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/shared/utils.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/shared/utils.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction isRelativeHref(href) {\n  const pathname = typeof href === 'object' ? href.pathname : href;\n  return pathname != null && !pathname.startsWith('/');\n}\nfunction isLocalHref(href) {\n  if (typeof href === 'object') {\n    return href.host == null && href.hostname == null;\n  } else {\n    const hasProtocol = /^[a-z]+:/i.test(href);\n    return !hasProtocol;\n  }\n}\nfunction isLocalizableHref(href) {\n  return isLocalHref(href) && !isRelativeHref(href);\n}\nfunction localizeHref(href, locale) {\n  let curLocale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : locale;\n  let curPathname = arguments.length > 3 ? arguments[3] : undefined;\n  let prefix = arguments.length > 4 ? arguments[4] : undefined;\n  if (!isLocalizableHref(href)) {\n    return href;\n  }\n  const isSwitchingLocale = locale !== curLocale;\n  const isPathnamePrefixed = hasPathnamePrefixed(prefix, curPathname);\n  const shouldPrefix = isSwitchingLocale || isPathnamePrefixed;\n  if (shouldPrefix && prefix != null) {\n    return prefixHref(href, prefix);\n  }\n  return href;\n}\nfunction prefixHref(href, prefix) {\n  let prefixedHref;\n  if (typeof href === 'string') {\n    prefixedHref = prefixPathname(prefix, href);\n  } else {\n    prefixedHref = {\n      ...href\n    };\n    if (href.pathname) {\n      prefixedHref.pathname = prefixPathname(prefix, href.pathname);\n    }\n  }\n  return prefixedHref;\n}\nfunction unprefixPathname(pathname, prefix) {\n  return pathname.replace(new RegExp(\"^\".concat(prefix)), '') || '/';\n}\nfunction prefixPathname(prefix, pathname) {\n  let localizedHref = prefix;\n\n  // Avoid trailing slashes\n  if (/^\\/(\\?.*)?$/.test(pathname)) {\n    pathname = pathname.slice(1);\n  }\n  localizedHref += pathname;\n  return localizedHref;\n}\nfunction hasPathnamePrefixed(prefix, pathname) {\n  return pathname === prefix || pathname.startsWith(\"\".concat(prefix, \"/\"));\n}\nfunction hasTrailingSlash() {\n  try {\n    // Provided via `env` setting in `next.config.js` via the plugin\n    return process.env._next_intl_trailing_slash === 'true';\n  } catch (_unused) {\n    return false;\n  }\n}\nfunction normalizeTrailingSlash(pathname) {\n  const trailingSlash = hasTrailingSlash();\n  if (pathname !== '/') {\n    const pathnameEndsWithSlash = pathname.endsWith('/');\n    if (trailingSlash && !pathnameEndsWithSlash) {\n      pathname += '/';\n    } else if (!trailingSlash && pathnameEndsWithSlash) {\n      pathname = pathname.slice(0, -1);\n    }\n  }\n  return pathname;\n}\nfunction matchesPathname(/** E.g. `/users/[userId]-[userName]` */\ntemplate, /** E.g. `/users/23-jane` */\npathname) {\n  const normalizedTemplate = normalizeTrailingSlash(template);\n  const normalizedPathname = normalizeTrailingSlash(pathname);\n  const regex = templateToRegex(normalizedTemplate);\n  return regex.test(normalizedPathname);\n}\nfunction getLocalePrefix(locale, localePrefix) {\n  var _localePrefix$prefixe;\n  return localePrefix.mode !== 'never' && ((_localePrefix$prefixe = localePrefix.prefixes) === null || _localePrefix$prefixe === void 0 ? void 0 : _localePrefix$prefixe[locale]) ||\n  // We return a prefix even if `mode: 'never'`. It's up to the consumer\n  // to decide to use it or not.\n  getLocaleAsPrefix(locale);\n}\nfunction getLocaleAsPrefix(locale) {\n  return '/' + locale;\n}\nfunction templateToRegex(template) {\n  const regexPattern = template\n  // Replace optional catchall ('[[...slug]]')\n  .replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g, '?(.*)')\n  // Replace catchall ('[...slug]')\n  .replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g, '(.+)')\n  // Replace regular parameter ('[slug]')\n  .replace(/\\[([^\\]]+)\\]/g, '([^/]+)');\n  return new RegExp(\"^\".concat(regexPattern, \"$\"));\n}\nfunction isOptionalCatchAllSegment(pathname) {\n  return pathname.includes('[[...');\n}\nfunction isCatchAllSegment(pathname) {\n  return pathname.includes('[...');\n}\nfunction isDynamicSegment(pathname) {\n  return pathname.includes('[');\n}\nfunction comparePathnamePairs(a, b) {\n  const pathA = a.split('/');\n  const pathB = b.split('/');\n  const maxLength = Math.max(pathA.length, pathB.length);\n  for (let i = 0; i < maxLength; i++) {\n    const segmentA = pathA[i];\n    const segmentB = pathB[i];\n\n    // If one of the paths ends, prioritize the shorter path\n    if (!segmentA && segmentB) return -1;\n    if (segmentA && !segmentB) return 1;\n    if (!segmentA && !segmentB) continue;\n\n    // Prioritize static segments over dynamic segments\n    if (!isDynamicSegment(segmentA) && isDynamicSegment(segmentB)) return -1;\n    if (isDynamicSegment(segmentA) && !isDynamicSegment(segmentB)) return 1;\n\n    // Prioritize non-catch-all segments over catch-all segments\n    if (!isCatchAllSegment(segmentA) && isCatchAllSegment(segmentB)) return -1;\n    if (isCatchAllSegment(segmentA) && !isCatchAllSegment(segmentB)) return 1;\n\n    // Prioritize non-optional catch-all segments over optional catch-all segments\n    if (!isOptionalCatchAllSegment(segmentA) && isOptionalCatchAllSegment(segmentB)) {\n      return -1;\n    }\n    if (isOptionalCatchAllSegment(segmentA) && !isOptionalCatchAllSegment(segmentB)) {\n      return 1;\n    }\n    if (segmentA === segmentB) continue;\n  }\n\n  // Both pathnames are completely static\n  return 0;\n}\nfunction getSortedPathnames(pathnames) {\n  return pathnames.sort(comparePathnamePairs);\n}\nfunction isPromise(value) {\n  // https://github.com/amannn/next-intl/issues/1711\n  return typeof value.then === 'function';\n}\n\nexports.getLocaleAsPrefix = getLocaleAsPrefix;\nexports.getLocalePrefix = getLocalePrefix;\nexports.getSortedPathnames = getSortedPathnames;\nexports.hasPathnamePrefixed = hasPathnamePrefixed;\nexports.isLocalizableHref = isLocalizableHref;\nexports.isPromise = isPromise;\nexports.localizeHref = localizeHref;\nexports.matchesPathname = matchesPathname;\nexports.normalizeTrailingSlash = normalizeTrailingSlash;\nexports.prefixHref = prefixHref;\nexports.prefixPathname = prefixPathname;\nexports.templateToRegex = templateToRegex;\nexports.unprefixPathname = unprefixPathname;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"extends\": () => (/* binding */ n)\n/* harmony export */ });\nfunction n(){return n=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)({}).hasOwnProperty.call(t,a)&&(n[a]=t[a])}return n},n.apply(null,arguments)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGFBQWEsd0RBQXdELFlBQVksbUJBQW1CLEtBQUssbUJBQW1CLGtCQUFrQix3Q0FBd0MsU0FBUyx5QkFBOEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qcz84ZTM4Il0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIG4oKXtyZXR1cm4gbj1PYmplY3QuYXNzaWduP09iamVjdC5hc3NpZ24uYmluZCgpOmZ1bmN0aW9uKG4pe2Zvcih2YXIgcj0xO3I8YXJndW1lbnRzLmxlbmd0aDtyKyspe3ZhciB0PWFyZ3VtZW50c1tyXTtmb3IodmFyIGEgaW4gdCkoe30pLmhhc093blByb3BlcnR5LmNhbGwodCxhKSYmKG5bYV09dFthXSl9cmV0dXJuIG59LG4uYXBwbHkobnVsbCxhcmd1bWVudHMpfWV4cG9ydHtuIGFzIGV4dGVuZHN9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ p)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _react_client_useLocale_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/esm/react-client/useLocale.js\");\n/* harmony import */ var _syncLocaleCookie_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/syncLocaleCookie.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction s(n, s) {\n    let { defaultLocale: p, href: f, locale: u, localeCookie: m, onClick: h, prefetch: d, unprefixed: k, ...x } = n;\n    const L = (0,_react_client_useLocale_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(), g = null != u && u !== L, j = u || L, v = function() {\n        const [e, o] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n        return (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n            o(window.location.host);\n        }, []), e;\n    }(), w = v && k && (k.domains[v] === j || !Object.keys(k.domains).includes(v) && L === p && !u) ? k.pathname : f, C = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    return g && (d && console.error(\"The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`\"), d = !1), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement(next_link__WEBPACK_IMPORTED_MODULE_0__[\"default\"], (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_4__[\"extends\"])({\n        ref: s,\n        href: w,\n        hrefLang: g ? u : void 0,\n        onClick: function(e) {\n            (0,_syncLocaleCookie_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(m, C, L, u), h && h(e);\n        },\n        prefetch: d\n    }, x));\n}\nvar p = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(s);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ p)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_client_useLocale_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/esm/react-client/useLocale.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/utils.js\");\n/* harmony import */ var _BaseLink_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction f(l, f) {\n    let { href: p, locale: u, localeCookie: d, localePrefixMode: x, prefix: j, ...k } = l;\n    const h = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.usePathname)(), v = (0,_react_client_useLocale_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(), C = u !== v, [L, g] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.isLocalizableHref)(p) && (\"never\" !== x || C) ? (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.prefixHref)(p, j) : p);\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        h && g((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.localizeHref)(p, u, v, h, j));\n    }, [\n        v,\n        p,\n        u,\n        h,\n        j\n    ]), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_BaseLink_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"], (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_5__[\"extends\"])({\n        ref: f,\n        href: L,\n        locale: u,\n        localeCookie: d\n    }, k));\n}\nconst p = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(f);\np.displayName = \"ClientLink\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/syncLocaleCookie.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/syncLocaleCookie.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ o)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js\");\nfunction o(o,e,n,a){if(!o||!(a!==n&&null!=a)||!e)return;const c=(0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.getBasePath)(e),f=\"\"!==c?c:\"/\",{name:r,...i}=o;i.path||(i.path=f);let l=\"\".concat(r,\"=\").concat(a,\";\");for(const[t,o]of Object.entries(i)){l+=\"\".concat(\"maxAge\"===t?\"max-age\":t),\"boolean\"!=typeof o&&(l+=\"=\"+o),l+=\";\"}document.cookie=l}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL25hdmlnYXRpb24vc2hhcmVkL3N5bmNMb2NhbGVDb29raWUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUMsb0JBQW9CLG9DQUFvQyxRQUFRLHNEQUFDLG9CQUFvQixZQUFZLEdBQUcsbUJBQW1CLGtDQUFrQyxHQUFHLG9DQUFvQyw0RUFBNEUsRUFBRSxrQkFBdUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vbmF2aWdhdGlvbi9zaGFyZWQvc3luY0xvY2FsZUNvb2tpZS5qcz8xYjEzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtnZXRCYXNlUGF0aCBhcyB0fWZyb21cIi4vdXRpbHMuanNcIjtmdW5jdGlvbiBvKG8sZSxuLGEpe2lmKCFvfHwhKGEhPT1uJiZudWxsIT1hKXx8IWUpcmV0dXJuO2NvbnN0IGM9dChlKSxmPVwiXCIhPT1jP2M6XCIvXCIse25hbWU6ciwuLi5pfT1vO2kucGF0aHx8KGkucGF0aD1mKTtsZXQgbD1cIlwiLmNvbmNhdChyLFwiPVwiKS5jb25jYXQoYSxcIjtcIik7Zm9yKGNvbnN0W3Qsb11vZiBPYmplY3QuZW50cmllcyhpKSl7bCs9XCJcIi5jb25jYXQoXCJtYXhBZ2VcIj09PXQ/XCJtYXgtYWdlXCI6dCksXCJib29sZWFuXCIhPXR5cGVvZiBvJiYobCs9XCI9XCIrbyksbCs9XCI7XCJ9ZG9jdW1lbnQuY29va2llPWx9ZXhwb3J0e28gYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/syncLocaleCookie.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js":
/*!********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/utils.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyPathnamePrefix: () => (/* binding */ d),\n/* harmony export */   compileLocalizedPathname: () => (/* binding */ f),\n/* harmony export */   getBasePath: () => (/* binding */ l),\n/* harmony export */   getRoute: () => (/* binding */ s),\n/* harmony export */   normalizeNameOrNameWithParams: () => (/* binding */ i),\n/* harmony export */   serializeSearchParams: () => (/* binding */ c),\n/* harmony export */   validateReceivedConfig: () => (/* binding */ u)\n/* harmony export */ });\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/utils.js\");\nfunction i(e){return\"string\"==typeof e?{pathname:e}:e}function c(e){function n(e){return String(e)}const t=new URLSearchParams;for(const[r,o]of Object.entries(e))Array.isArray(o)?o.forEach((e=>{t.append(r,n(e))})):t.set(r,n(o));return\"?\"+t.toString()}function f(e){let{pathname:n,locale:t,params:r,pathnames:o,query:i}=e;function f(e){let n=o[e];return n||(n=e),n}function s(e){const n=\"string\"==typeof e?e:e[t];let o=n;if(r&&Object.entries(r).forEach((e=>{let n,t,[r,a]=e;Array.isArray(a)?(n=\"(\\\\[)?\\\\[...\".concat(r,\"\\\\](\\\\])?\"),t=a.map((e=>String(e))).join(\"/\")):(n=\"\\\\[\".concat(r,\"\\\\]\"),t=String(a)),o=o.replace(new RegExp(n,\"g\"),t)})),o=o.replace(/\\[\\[\\.\\.\\..+\\]\\]/g,\"\"),o=(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.normalizeTrailingSlash)(o),o.includes(\"[\"))throw new Error(\"Insufficient params provided for localized pathname.\\nTemplate: \".concat(n,\"\\nParams: \").concat(JSON.stringify(r)));return i&&(o+=c(i)),o}if(\"string\"==typeof n){return s(f(n))}{const{pathname:e,...t}=n;return{...t,pathname:s(f(e))}}}function s(t,r,o){const a=(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getSortedPathnames)(Object.keys(o)),i=decodeURI(r);for(const e of a){const r=o[e];if(\"string\"==typeof r){if((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(r,i))return e}else if((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(r[t],i))return e}return r}function l(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.pathname;return\"/\"===e?n:n.replace(e,\"\")}function d(e,n,a,i,c){const{mode:f}=a.localePrefix;let s;if(void 0!==c)s=c;else if((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.isLocalizableHref)(e))if(\"always\"===f)s=!0;else if(\"as-needed\"===f){let e=a.defaultLocale;if(a.domains){const n=a.domains.find((e=>e.domain===i));n?e=n.defaultLocale:i||console.error(\"You're using a routing configuration with `localePrefix: 'as-needed'` in combination with `domains`. In order to compute a correct pathname, you need to provide a `domain` parameter.\\n\\nSee: https://next-intl.dev/docs/routing#domains-localeprefix-asneeded\")}s=e!==n}return s?(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.prefixPathname)((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalePrefix)(n,a.localePrefix),e):e}function u(e){var n;if(\"as-needed\"===(null===(n=e.localePrefix)||void 0===n?void 0:n.mode)&&!(\"defaultLocale\"in e))throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\")}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/react-client/useLocale.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-client/useLocale.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var use_intl_useLocale__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/_useLocale */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale.js\");\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shared/constants.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/constants.js\");\nlet o=!1;function r(){const r=(0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.useParams)();let a;try{a=(0,use_intl_useLocale__WEBPACK_IMPORTED_MODULE_1__.useLocale)()}catch(e){if(\"string\"!=typeof(null==r?void 0:r[_shared_constants_js__WEBPACK_IMPORTED_MODULE_2__.LOCALE_SEGMENT_NAME]))throw e;o||(console.warn(\"Deprecation warning: `useLocale` has returned a default from `useParams().locale` since no `NextIntlClientProvider` ancestor was found for the calling component. This behavior will be removed in the next major version. Please ensure all Client Components that use `next-intl` are wrapped in a `NextIntlClientProvider`.\"),o=!0),a=r[_shared_constants_js__WEBPACK_IMPORTED_MODULE_2__.LOCALE_SEGMENT_NAME]}return a}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LWNsaWVudC91c2VMb2NhbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF5SixTQUFTLGFBQWEsUUFBUSwwREFBQyxHQUFHLE1BQU0sSUFBSSxFQUFFLDZEQUFDLEdBQUcsU0FBUyxxQ0FBcUMscUVBQUMsV0FBVyw2VkFBNlYscUVBQUMsRUFBRSxTQUE4QiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9yZWFjdC1jbGllbnQvdXNlTG9jYWxlLmpzP2Y5ZTEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e3VzZVBhcmFtcyBhcyBlfWZyb21cIm5leHQvbmF2aWdhdGlvblwiO2ltcG9ydHt1c2VMb2NhbGUgYXMgdH1mcm9tXCJ1c2UtaW50bC9fdXNlTG9jYWxlXCI7aW1wb3J0e0xPQ0FMRV9TRUdNRU5UX05BTUUgYXMgbn1mcm9tXCIuLi9zaGFyZWQvY29uc3RhbnRzLmpzXCI7bGV0IG89ITE7ZnVuY3Rpb24gcigpe2NvbnN0IHI9ZSgpO2xldCBhO3RyeXthPXQoKX1jYXRjaChlKXtpZihcInN0cmluZ1wiIT10eXBlb2YobnVsbD09cj92b2lkIDA6cltuXSkpdGhyb3cgZTtvfHwoY29uc29sZS53YXJuKFwiRGVwcmVjYXRpb24gd2FybmluZzogYHVzZUxvY2FsZWAgaGFzIHJldHVybmVkIGEgZGVmYXVsdCBmcm9tIGB1c2VQYXJhbXMoKS5sb2NhbGVgIHNpbmNlIG5vIGBOZXh0SW50bENsaWVudFByb3ZpZGVyYCBhbmNlc3RvciB3YXMgZm91bmQgZm9yIHRoZSBjYWxsaW5nIGNvbXBvbmVudC4gVGhpcyBiZWhhdmlvciB3aWxsIGJlIHJlbW92ZWQgaW4gdGhlIG5leHQgbWFqb3IgdmVyc2lvbi4gUGxlYXNlIGVuc3VyZSBhbGwgQ2xpZW50IENvbXBvbmVudHMgdGhhdCB1c2UgYG5leHQtaW50bGAgYXJlIHdyYXBwZWQgaW4gYSBgTmV4dEludGxDbGllbnRQcm92aWRlcmAuXCIpLG89ITApLGE9cltuXX1yZXR1cm4gYX1leHBvcnR7ciBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/react-client/useLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_intl_IntlProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/_IntlProvider */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction r(r) {\n    let { locale: o, ...i } = r;\n    if (!o) throw new Error(\"Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\\n\\nSee https://next-intl.dev/docs/configuration#locale\");\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(use_intl_IntlProvider__WEBPACK_IMPORTED_MODULE_1__.IntlProvider, (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__[\"extends\"])({\n        locale: o\n    }, i));\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NoYXJlZC9OZXh0SW50bENsaWVudFByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OzZEQUNtRTtBQUFxQjtBQUFzRDtBQUFBLFNBQVNLLEVBQUVBLENBQUM7SUFBRSxJQUFHLEVBQUNDLFFBQU9DLENBQUMsRUFBQyxHQUFHQyxHQUFFLEdBQUNIO0lBQUUsSUFBRyxDQUFDRSxHQUFFLE1BQU0sSUFBSUUsTUFBTTtJQUErSixxQkFBT1AsMERBQWUsQ0FBQ0UsK0RBQUNBLEVBQUNILGdGQUFDQSxDQUFDO1FBQUNLLFFBQU9DO0lBQUMsR0FBRUM7QUFBRztBQUFzQiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9zaGFyZWQvTmV4dEludGxDbGllbnRQcm92aWRlci5qcz9kY2U0Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0e2V4dGVuZHMgYXMgZX1mcm9tXCIuLi9fdmlydHVhbC9fcm9sbHVwUGx1Z2luQmFiZWxIZWxwZXJzLmpzXCI7aW1wb3J0IGwgZnJvbVwicmVhY3RcIjtpbXBvcnR7SW50bFByb3ZpZGVyIGFzIHR9ZnJvbVwidXNlLWludGwvX0ludGxQcm92aWRlclwiO2Z1bmN0aW9uIHIocil7bGV0e2xvY2FsZTpvLC4uLml9PXI7aWYoIW8pdGhyb3cgbmV3IEVycm9yKFwiRmFpbGVkIHRvIGRldGVybWluZSBsb2NhbGUgaW4gYE5leHRJbnRsQ2xpZW50UHJvdmlkZXJgLCBwbGVhc2UgcHJvdmlkZSB0aGUgYGxvY2FsZWAgcHJvcCBleHBsaWNpdGx5LlxcblxcblNlZSBodHRwczovL25leHQtaW50bC5kZXYvZG9jcy9jb25maWd1cmF0aW9uI2xvY2FsZVwiKTtyZXR1cm4gbC5jcmVhdGVFbGVtZW50KHQsZSh7bG9jYWxlOm99LGkpKX1leHBvcnR7ciBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6WyJleHRlbmRzIiwiZSIsImwiLCJJbnRsUHJvdmlkZXIiLCJ0IiwiciIsImxvY2FsZSIsIm8iLCJpIiwiRXJyb3IiLCJjcmVhdGVFbGVtZW50IiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/shared/constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/constants.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HEADER_LOCALE_NAME: () => (/* binding */ o),\n/* harmony export */   LOCALE_SEGMENT_NAME: () => (/* binding */ L)\n/* harmony export */ });\nconst o=\"X-NEXT-INTL-LOCALE\",L=\"locale\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NoYXJlZC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSx3Q0FBaUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2hhcmVkL2NvbnN0YW50cy5qcz83MDU1Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG89XCJYLU5FWFQtSU5UTC1MT0NBTEVcIixMPVwibG9jYWxlXCI7ZXhwb3J0e28gYXMgSEVBREVSX0xPQ0FMRV9OQU1FLEwgYXMgTE9DQUxFX1NFR01FTlRfTkFNRX07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/shared/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/shared/utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/utils.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocaleAsPrefix: () => (/* binding */ l),\n/* harmony export */   getLocalePrefix: () => (/* binding */ f),\n/* harmony export */   getSortedPathnames: () => (/* binding */ d),\n/* harmony export */   hasPathnamePrefixed: () => (/* binding */ i),\n/* harmony export */   isLocalizableHref: () => (/* binding */ n),\n/* harmony export */   isPromise: () => (/* binding */ v),\n/* harmony export */   localizeHref: () => (/* binding */ t),\n/* harmony export */   matchesPathname: () => (/* binding */ c),\n/* harmony export */   normalizeTrailingSlash: () => (/* binding */ o),\n/* harmony export */   prefixHref: () => (/* binding */ e),\n/* harmony export */   prefixPathname: () => (/* binding */ u),\n/* harmony export */   templateToRegex: () => (/* binding */ s),\n/* harmony export */   unprefixPathname: () => (/* binding */ r)\n/* harmony export */ });\nfunction n(n){return function(n){return\"object\"==typeof n?null==n.host&&null==n.hostname:!/^[a-z]+:/i.test(n)}(n)&&!function(n){const t=\"object\"==typeof n?n.pathname:n;return null!=t&&!t.startsWith(\"/\")}(n)}function t(t,r){let u=arguments.length>2&&void 0!==arguments[2]?arguments[2]:r,o=arguments.length>3?arguments[3]:void 0,c=arguments.length>4?arguments[4]:void 0;if(!n(t))return t;const f=r!==u,l=i(c,o);return(f||l)&&null!=c?e(t,c):t}function e(n,t){let e;return\"string\"==typeof n?e=u(t,n):(e={...n},n.pathname&&(e.pathname=u(t,n.pathname))),e}function r(n,t){return n.replace(new RegExp(\"^\".concat(t)),\"\")||\"/\"}function u(n,t){let e=n;return/^\\/(\\?.*)?$/.test(t)&&(t=t.slice(1)),e+=t,e}function i(n,t){return t===n||t.startsWith(\"\".concat(n,\"/\"))}function o(n){const t=function(){try{return\"true\"===process.env._next_intl_trailing_slash}catch(n){return!1}}();if(\"/\"!==n){const e=n.endsWith(\"/\");t&&!e?n+=\"/\":!t&&e&&(n=n.slice(0,-1))}return n}function c(n,t){const e=o(n),r=o(t);return s(e).test(r)}function f(n,t){var e;return\"never\"!==t.mode&&(null===(e=t.prefixes)||void 0===e?void 0:e[n])||l(n)}function l(n){return\"/\"+n}function s(n){const t=n.replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g,\"?(.*)\").replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g,\"(.+)\").replace(/\\[([^\\]]+)\\]/g,\"([^/]+)\");return new RegExp(\"^\".concat(t,\"$\"))}function a(n){return n.includes(\"[[...\")}function p(n){return n.includes(\"[...\")}function h(n){return n.includes(\"[\")}function g(n,t){const e=n.split(\"/\"),r=t.split(\"/\"),u=Math.max(e.length,r.length);for(let n=0;n<u;n++){const t=e[n],u=r[n];if(!t&&u)return-1;if(t&&!u)return 1;if(t||u){if(!h(t)&&h(u))return-1;if(h(t)&&!h(u))return 1;if(!p(t)&&p(u))return-1;if(p(t)&&!p(u))return 1;if(!a(t)&&a(u))return-1;if(a(t)&&!a(u))return 1}}return 0}function d(n){return n.sort(g)}function v(n){return\"function\"==typeof n.then}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/shared/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"extends\": () => (/* binding */ n)\n/* harmony export */ });\nfunction n(){return n=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)({}).hasOwnProperty.call(t,a)&&(n[a]=t[a])}return n},n.apply(null,arguments)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGFBQWEsd0RBQXdELFlBQVksbUJBQW1CLEtBQUssbUJBQW1CLGtCQUFrQix3Q0FBd0MsU0FBUyx5QkFBOEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qcz9jNjExIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIG4oKXtyZXR1cm4gbj1PYmplY3QuYXNzaWduP09iamVjdC5hc3NpZ24uYmluZCgpOmZ1bmN0aW9uKG4pe2Zvcih2YXIgcj0xO3I8YXJndW1lbnRzLmxlbmd0aDtyKyspe3ZhciB0PWFyZ3VtZW50c1tyXTtmb3IodmFyIGEgaW4gdCkoe30pLmhhc093blByb3BlcnR5LmNhbGwodCxhKSYmKG5bYV09dFthXSl9cmV0dXJuIG59LG4uYXBwbHkobnVsbCxhcmd1bWVudHMpfWV4cG9ydHtuIGFzIGV4dGVuZHN9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/navigation/react-server/createNavigation.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/react-server/createNavigation.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ t)\n/* harmony export */ });\n/* harmony import */ var _shared_createSharedNavigationFns_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/createSharedNavigationFns.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/createSharedNavigationFns.js\");\n/* harmony import */ var _getServerLocale_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getServerLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/react-server/getServerLocale.js\");\nfunction t(t){const{config:n,...r}=(0,_shared_createSharedNavigationFns_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((function(){return (0,_getServerLocale_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])()}),t);function u(e){return()=>{throw new Error(\"`\".concat(e,\"` is not supported in Server Components. You can use this hook if you convert the calling component to a Client Component.\"))}}return{...r,usePathname:u(\"usePathname\"),useRouter:u(\"useRouter\")}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL25hdmlnYXRpb24vcmVhY3Qtc2VydmVyL2NyZWF0ZU5hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTBGLGNBQWMsTUFBTSxjQUFjLENBQUMsZ0ZBQUMsYUFBYSxPQUFPLCtEQUFDLEdBQUcsS0FBSyxjQUFjLFdBQVcsNkpBQTZKLE9BQU8sNERBQWlGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL25hdmlnYXRpb24vcmVhY3Qtc2VydmVyL2NyZWF0ZU5hdmlnYXRpb24uanM/NWEzYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZSBmcm9tXCIuLi9zaGFyZWQvY3JlYXRlU2hhcmVkTmF2aWdhdGlvbkZucy5qc1wiO2ltcG9ydCBvIGZyb21cIi4vZ2V0U2VydmVyTG9jYWxlLmpzXCI7ZnVuY3Rpb24gdCh0KXtjb25zdHtjb25maWc6biwuLi5yfT1lKChmdW5jdGlvbigpe3JldHVybiBvKCl9KSx0KTtmdW5jdGlvbiB1KGUpe3JldHVybigpPT57dGhyb3cgbmV3IEVycm9yKFwiYFwiLmNvbmNhdChlLFwiYCBpcyBub3Qgc3VwcG9ydGVkIGluIFNlcnZlciBDb21wb25lbnRzLiBZb3UgY2FuIHVzZSB0aGlzIGhvb2sgaWYgeW91IGNvbnZlcnQgdGhlIGNhbGxpbmcgY29tcG9uZW50IHRvIGEgQ2xpZW50IENvbXBvbmVudC5cIikpfX1yZXR1cm57Li4ucix1c2VQYXRobmFtZTp1KFwidXNlUGF0aG5hbWVcIiksdXNlUm91dGVyOnUoXCJ1c2VSb3V0ZXJcIil9fWV4cG9ydHt0IGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/navigation/react-server/createNavigation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/navigation/react-server/getServerLocale.js":
/*!************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/react-server/getServerLocale.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var _server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../server/react-server/getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nasync function r(){return(await (0,_server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])()).locale}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL25hdmlnYXRpb24vcmVhY3Qtc2VydmVyL2dldFNlcnZlckxvY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFzRCxtQkFBbUIsYUFBYSw2RUFBQyxXQUFnQyIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9uYXZpZ2F0aW9uL3JlYWN0LXNlcnZlci9nZXRTZXJ2ZXJMb2NhbGUuanM/ZTkwNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZSBmcm9tXCIuLi8uLi9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldENvbmZpZy5qc1wiO2FzeW5jIGZ1bmN0aW9uIHIoKXtyZXR1cm4oYXdhaXQgZSgpKS5sb2NhbGV9ZXhwb3J0e3IgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/navigation/react-server/getServerLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\node_modules\next-intl\dist\esm\navigation\shared\BaseLink.js#default`));


/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/createSharedNavigationFns.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/createSharedNavigationFns.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ h)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _routing_config_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../routing/config.js */ \"(rsc)/./node_modules/next-intl/dist/esm/routing/config.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\");\n/* harmony import */ var _BaseLink_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./BaseLink.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js\");\nfunction h(h,y){const j=(0,_routing_config_js__WEBPACK_IMPORTED_MODULE_2__.receiveRoutingConfig)(y||{});(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.validateReceivedConfig)(j);const g=j.pathnames,v=\"as-needed\"===j.localePrefix.mode&&j.domains||void 0;function q(o,a){let n,l,u,{href:f,locale:s,...p}=o;\"object\"==typeof f?(n=f.pathname,u=f.query,l=f.params):n=f;const d=(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_4__.isLocalizableHref)(f),y=h(),q=(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_4__.isPromise)(y)?(0,react__WEBPACK_IMPORTED_MODULE_1__.use)(y):y,x=d?L({locale:s||q,href:null==g?n:{pathname:n,params:l}},null!=s||v||void 0):n;return react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_BaseLink_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"],(0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_6__[\"extends\"])({ref:a,defaultLocale:j.defaultLocale,href:\"object\"==typeof f?{...f,pathname:x}:x,locale:s,localeCookie:j.localeCookie,unprefixed:v&&d?{domains:j.domains.reduce(((e,o)=>(e[o.domain]=o.defaultLocale,e)),{}),pathname:L({locale:q,href:null==g?{pathname:n,query:u}:{pathname:n,query:u,params:l}},!1)}:void 0},p))}const x=(0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(q);function L(e,o){const{href:a,locale:t}=e;let n;return null==g?\"object\"==typeof a?(n=a.pathname,a.query&&(n+=(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.serializeSearchParams)(a.query))):n=a:n=(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.compileLocalizedPathname)({locale:t,...(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.normalizeNameOrNameWithParams)(a),pathnames:j.pathnames}),(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.applyPathnamePrefix)(n,t,j,e.domain,o)}function b(e){return function(o){for(var a=arguments.length,t=new Array(a>1?a-1:0),n=1;n<a;n++)t[n-1]=arguments[n];return e(L(o,o.domain?void 0:v),...t)}}const k=b(next_navigation__WEBPACK_IMPORTED_MODULE_0__.redirect),P=b(next_navigation__WEBPACK_IMPORTED_MODULE_0__.permanentRedirect);return{config:j,Link:x,redirect:k,permanentRedirect:P,getPathname:L}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/createSharedNavigationFns.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js":
/*!********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/utils.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyPathnamePrefix: () => (/* binding */ d),\n/* harmony export */   compileLocalizedPathname: () => (/* binding */ f),\n/* harmony export */   getBasePath: () => (/* binding */ l),\n/* harmony export */   getRoute: () => (/* binding */ s),\n/* harmony export */   normalizeNameOrNameWithParams: () => (/* binding */ i),\n/* harmony export */   serializeSearchParams: () => (/* binding */ c),\n/* harmony export */   validateReceivedConfig: () => (/* binding */ u)\n/* harmony export */ });\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\");\nfunction i(e){return\"string\"==typeof e?{pathname:e}:e}function c(e){function n(e){return String(e)}const t=new URLSearchParams;for(const[r,o]of Object.entries(e))Array.isArray(o)?o.forEach((e=>{t.append(r,n(e))})):t.set(r,n(o));return\"?\"+t.toString()}function f(e){let{pathname:n,locale:t,params:r,pathnames:o,query:i}=e;function f(e){let n=o[e];return n||(n=e),n}function s(e){const n=\"string\"==typeof e?e:e[t];let o=n;if(r&&Object.entries(r).forEach((e=>{let n,t,[r,a]=e;Array.isArray(a)?(n=\"(\\\\[)?\\\\[...\".concat(r,\"\\\\](\\\\])?\"),t=a.map((e=>String(e))).join(\"/\")):(n=\"\\\\[\".concat(r,\"\\\\]\"),t=String(a)),o=o.replace(new RegExp(n,\"g\"),t)})),o=o.replace(/\\[\\[\\.\\.\\..+\\]\\]/g,\"\"),o=(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.normalizeTrailingSlash)(o),o.includes(\"[\"))throw new Error(\"Insufficient params provided for localized pathname.\\nTemplate: \".concat(n,\"\\nParams: \").concat(JSON.stringify(r)));return i&&(o+=c(i)),o}if(\"string\"==typeof n){return s(f(n))}{const{pathname:e,...t}=n;return{...t,pathname:s(f(e))}}}function s(t,r,o){const a=(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getSortedPathnames)(Object.keys(o)),i=decodeURI(r);for(const e of a){const r=o[e];if(\"string\"==typeof r){if((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(r,i))return e}else if((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(r[t],i))return e}return r}function l(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.pathname;return\"/\"===e?n:n.replace(e,\"\")}function d(e,n,a,i,c){const{mode:f}=a.localePrefix;let s;if(void 0!==c)s=c;else if((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.isLocalizableHref)(e))if(\"always\"===f)s=!0;else if(\"as-needed\"===f){let e=a.defaultLocale;if(a.domains){const n=a.domains.find((e=>e.domain===i));n?e=n.defaultLocale:i||console.error(\"You're using a routing configuration with `localePrefix: 'as-needed'` in combination with `domains`. In order to compute a correct pathname, you need to provide a `domain` parameter.\\n\\nSee: https://next-intl.dev/docs/routing#domains-localeprefix-asneeded\")}s=e!==n}return s?(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.prefixPathname)((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalePrefix)(n,a.localePrefix),e):e}function u(e){var n;if(\"as-needed\"===(null===(n=e.localePrefix)||void 0===n?void 0:n.mode)&&!(\"defaultLocale\"in e))throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\")}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ i)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\");\n/* harmony import */ var _server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../server/react-server/getLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getLocale.js\");\n/* harmony import */ var _server_react_server_getNow_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../server/react-server/getNow.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getNow.js\");\n/* harmony import */ var _server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../server/react-server/getTimeZone.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js\");\nasync function i(i){let{locale:n,now:s,timeZone:m,...c}=i;return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],(0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__[\"extends\"])({locale:null!=n?n:await (0,_server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(),now:null!=s?s:await (0,_server_react_server_getNow_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),timeZone:null!=m?m:await (0,_server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()},c))}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci9OZXh0SW50bENsaWVudFByb3ZpZGVyU2VydmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQW1TLG9CQUFvQixJQUFJLCtCQUErQixHQUFHLE9BQU8sMERBQWUsQ0FBQyx5RUFBQyxDQUFDLGdGQUFDLEVBQUUsdUJBQXVCLDZFQUFDLHVCQUF1QiwwRUFBQyw0QkFBNEIsK0VBQUMsR0FBRyxLQUEwQiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9yZWFjdC1zZXJ2ZXIvTmV4dEludGxDbGllbnRQcm92aWRlclNlcnZlci5qcz82ODU3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtleHRlbmRzIGFzIGV9ZnJvbVwiLi4vX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qc1wiO2ltcG9ydCByIGZyb21cInJlYWN0XCI7aW1wb3J0IHQgZnJvbVwiLi4vc2hhcmVkL05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanNcIjtpbXBvcnQgbyBmcm9tXCIuLi9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldExvY2FsZS5qc1wiO2ltcG9ydCBsIGZyb21cIi4uL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Tm93LmpzXCI7aW1wb3J0IGEgZnJvbVwiLi4vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRUaW1lWm9uZS5qc1wiO2FzeW5jIGZ1bmN0aW9uIGkoaSl7bGV0e2xvY2FsZTpuLG5vdzpzLHRpbWVab25lOm0sLi4uY309aTtyZXR1cm4gci5jcmVhdGVFbGVtZW50KHQsZSh7bG9jYWxlOm51bGwhPW4/bjphd2FpdCBvKCksbm93Om51bGwhPXM/czphd2FpdCBsKCksdGltZVpvbmU6bnVsbCE9bT9tOmF3YWl0IGEoKX0sYykpfWV4cG9ydHtpIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/getTranslator.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/getTranslator.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ t)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/development/core.js\");\nvar t=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((function(r,t){return (0,use_intl_core__WEBPACK_IMPORTED_MODULE_1__.createTranslator)({...r,namespace:t})}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci9nZXRUcmFuc2xhdG9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBK0UsTUFBTSw0Q0FBQyxnQkFBZ0IsT0FBTywrREFBQyxFQUFFLGlCQUFpQixFQUFFLEdBQXdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci9nZXRUcmFuc2xhdG9yLmpzPzY2OTAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e2NhY2hlIGFzIHJ9ZnJvbVwicmVhY3RcIjtpbXBvcnR7Y3JlYXRlVHJhbnNsYXRvciBhcyBlfWZyb21cInVzZS1pbnRsL2NvcmVcIjt2YXIgdD1yKChmdW5jdGlvbihyLHQpe3JldHVybiBlKHsuLi5yLG5hbWVzcGFjZTp0fSl9KSk7ZXhwb3J0e3QgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/getTranslator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/useConfig.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../server/react-server/getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nfunction r(r){return function(n,r){try{return (0,react__WEBPACK_IMPORTED_MODULE_0__.use)(r)}catch(e){throw e instanceof TypeError&&e.message.includes(\"Cannot read properties of null (reading 'use')\")?new Error(\"`\".concat(n,\"` is not callable within an async component. Please refer to https://next-intl.dev/docs/environments/server-client-components#async-components\"),{cause:e}):e}}(r,(0,_server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])())}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci91c2VDb25maWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUErRSxjQUFjLHFCQUFxQixJQUFJLE9BQU8sMENBQUMsSUFBSSxTQUFTLDZRQUE2USxRQUFRLEtBQUssR0FBRyw2RUFBQyxJQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9yZWFjdC1zZXJ2ZXIvdXNlQ29uZmlnLmpzPzliOGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e3VzZSBhcyBlfWZyb21cInJlYWN0XCI7aW1wb3J0IG4gZnJvbVwiLi4vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRDb25maWcuanNcIjtmdW5jdGlvbiByKHIpe3JldHVybiBmdW5jdGlvbihuLHIpe3RyeXtyZXR1cm4gZShyKX1jYXRjaChlKXt0aHJvdyBlIGluc3RhbmNlb2YgVHlwZUVycm9yJiZlLm1lc3NhZ2UuaW5jbHVkZXMoXCJDYW5ub3QgcmVhZCBwcm9wZXJ0aWVzIG9mIG51bGwgKHJlYWRpbmcgJ3VzZScpXCIpP25ldyBFcnJvcihcImBcIi5jb25jYXQobixcImAgaXMgbm90IGNhbGxhYmxlIHdpdGhpbiBhbiBhc3luYyBjb21wb25lbnQuIFBsZWFzZSByZWZlciB0byBodHRwczovL25leHQtaW50bC5kZXYvZG9jcy9lbnZpcm9ubWVudHMvc2VydmVyLWNsaWVudC1jb21wb25lbnRzI2FzeW5jLWNvbXBvbmVudHNcIikse2NhdXNlOmV9KTplfX0ocixuKCkpfWV4cG9ydHtyIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/useTranslations.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ o)\n/* harmony export */ });\n/* harmony import */ var _getTranslator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getTranslator.js */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/getTranslator.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js\");\nfunction o(){for(var o=arguments.length,n=new Array(o),e=0;e<o;e++)n[e]=arguments[e];let[s]=n;const a=(0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"useTranslations\");return (0,_getTranslator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(a,s)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci91c2VUcmFuc2xhdGlvbnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdFLGFBQWEsOENBQThDLElBQUksc0JBQXNCLFNBQVMsUUFBUSx5REFBQyxvQkFBb0IsT0FBTyw2REFBQyxNQUEyQiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9yZWFjdC1zZXJ2ZXIvdXNlVHJhbnNsYXRpb25zLmpzPzVlN2UiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHIgZnJvbVwiLi9nZXRUcmFuc2xhdG9yLmpzXCI7aW1wb3J0IHQgZnJvbVwiLi91c2VDb25maWcuanNcIjtmdW5jdGlvbiBvKCl7Zm9yKHZhciBvPWFyZ3VtZW50cy5sZW5ndGgsbj1uZXcgQXJyYXkobyksZT0wO2U8bztlKyspbltlXT1hcmd1bWVudHNbZV07bGV0W3NdPW47Y29uc3QgYT10KFwidXNlVHJhbnNsYXRpb25zXCIpO3JldHVybiByKGEscyl9ZXhwb3J0e28gYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/routing/config.js":
/*!***********************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/routing/config.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   receiveLocaleCookie: () => (/* binding */ o),\n/* harmony export */   receiveLocalePrefixConfig: () => (/* binding */ l),\n/* harmony export */   receiveRoutingConfig: () => (/* binding */ e)\n/* harmony export */ });\nfunction e(e){var t,n;return{...e,localePrefix:l(e.localePrefix),localeCookie:o(e.localeCookie),localeDetection:null===(t=e.localeDetection)||void 0===t||t,alternateLinks:null===(n=e.alternateLinks)||void 0===n||n}}function o(e){return!(null!=e&&!e)&&{name:\"NEXT_LOCALE\",maxAge:31536e3,sameSite:\"lax\",...\"object\"==typeof e&&e}}function l(e){return\"object\"==typeof e?e:{mode:e||\"always\"}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JvdXRpbmcvY29uZmlnLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLGNBQWMsUUFBUSxPQUFPLDBMQUEwTCxjQUFjLHVCQUF1QiwyRUFBMkUsY0FBYyw0QkFBNEIsa0JBQTRHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JvdXRpbmcvY29uZmlnLmpzPzU4NDAiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gZShlKXt2YXIgdCxuO3JldHVybnsuLi5lLGxvY2FsZVByZWZpeDpsKGUubG9jYWxlUHJlZml4KSxsb2NhbGVDb29raWU6byhlLmxvY2FsZUNvb2tpZSksbG9jYWxlRGV0ZWN0aW9uOm51bGw9PT0odD1lLmxvY2FsZURldGVjdGlvbil8fHZvaWQgMD09PXR8fHQsYWx0ZXJuYXRlTGlua3M6bnVsbD09PShuPWUuYWx0ZXJuYXRlTGlua3MpfHx2b2lkIDA9PT1ufHxufX1mdW5jdGlvbiBvKGUpe3JldHVybiEobnVsbCE9ZSYmIWUpJiZ7bmFtZTpcIk5FWFRfTE9DQUxFXCIsbWF4QWdlOjMxNTM2ZTMsc2FtZVNpdGU6XCJsYXhcIiwuLi5cIm9iamVjdFwiPT10eXBlb2YgZSYmZX19ZnVuY3Rpb24gbChlKXtyZXR1cm5cIm9iamVjdFwiPT10eXBlb2YgZT9lOnttb2RlOmV8fFwiYWx3YXlzXCJ9fWV4cG9ydHtvIGFzIHJlY2VpdmVMb2NhbGVDb29raWUsbCBhcyByZWNlaXZlTG9jYWxlUHJlZml4Q29uZmlnLGUgYXMgcmVjZWl2ZVJvdXRpbmdDb25maWd9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/routing/config.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRequestLocale: () => (/* binding */ a)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/constants.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\");\n/* harmony import */ var _RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocaleCache.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\");\nconst i=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((async function(){const e=(0,next_headers__WEBPACK_IMPORTED_MODULE_0__.headers)();return (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_2__.isPromise)(e)?await e:e}));const s=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((async function(){let t;try{t=(await i()).get(_shared_constants_js__WEBPACK_IMPORTED_MODULE_3__.HEADER_LOCALE_NAME)||void 0}catch(t){if(t instanceof Error&&\"DYNAMIC_SERVER_USAGE\"===t.digest){const e=new Error(\"Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering\",{cause:t});throw e.digest=t.digest,e}throw t}return t}));async function a(){return (0,_RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__.getCachedRequestLocale)()||await s()}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQXVQLFFBQVEsNENBQUMsbUJBQW1CLFFBQVEscURBQUMsR0FBRyxPQUFPLDJEQUFDLGNBQWMsR0FBRyxRQUFRLDRDQUFDLG1CQUFtQixNQUFNLElBQUksa0JBQWtCLG9FQUFDLFVBQVUsU0FBUywwREFBMEQsK1VBQStVLFFBQVEsRUFBRSwwQkFBMEIsUUFBUSxTQUFTLEdBQUcsbUJBQW1CLE9BQU8sOEVBQUMsY0FBNEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2VydmVyL3JlYWN0LXNlcnZlci9SZXF1ZXN0TG9jYWxlLmpzPzU0NDMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e2hlYWRlcnMgYXMgdH1mcm9tXCJuZXh0L2hlYWRlcnNcIjtpbXBvcnR7Y2FjaGUgYXMgZX1mcm9tXCJyZWFjdFwiO2ltcG9ydHtIRUFERVJfTE9DQUxFX05BTUUgYXMgbn1mcm9tXCIuLi8uLi9zaGFyZWQvY29uc3RhbnRzLmpzXCI7aW1wb3J0e2lzUHJvbWlzZSBhcyByfWZyb21cIi4uLy4uL3NoYXJlZC91dGlscy5qc1wiO2ltcG9ydHtnZXRDYWNoZWRSZXF1ZXN0TG9jYWxlIGFzIG99ZnJvbVwiLi9SZXF1ZXN0TG9jYWxlQ2FjaGUuanNcIjtjb25zdCBpPWUoKGFzeW5jIGZ1bmN0aW9uKCl7Y29uc3QgZT10KCk7cmV0dXJuIHIoZSk/YXdhaXQgZTplfSkpO2NvbnN0IHM9ZSgoYXN5bmMgZnVuY3Rpb24oKXtsZXQgdDt0cnl7dD0oYXdhaXQgaSgpKS5nZXQobil8fHZvaWQgMH1jYXRjaCh0KXtpZih0IGluc3RhbmNlb2YgRXJyb3ImJlwiRFlOQU1JQ19TRVJWRVJfVVNBR0VcIj09PXQuZGlnZXN0KXtjb25zdCBlPW5ldyBFcnJvcihcIlVzYWdlIG9mIG5leHQtaW50bCBBUElzIGluIFNlcnZlciBDb21wb25lbnRzIGN1cnJlbnRseSBvcHRzIGludG8gZHluYW1pYyByZW5kZXJpbmcuIFRoaXMgbGltaXRhdGlvbiB3aWxsIGV2ZW50dWFsbHkgYmUgbGlmdGVkLCBidXQgYXMgYSBzdG9wZ2FwIHNvbHV0aW9uLCB5b3UgY2FuIHVzZSB0aGUgYHNldFJlcXVlc3RMb2NhbGVgIEFQSSB0byBlbmFibGUgc3RhdGljIHJlbmRlcmluZywgc2VlIGh0dHBzOi8vbmV4dC1pbnRsLmRldi9kb2NzL2dldHRpbmctc3RhcnRlZC9hcHAtcm91dGVyL3dpdGgtaTE4bi1yb3V0aW5nI3N0YXRpYy1yZW5kZXJpbmdcIix7Y2F1c2U6dH0pO3Rocm93IGUuZGlnZXN0PXQuZGlnZXN0LGV9dGhyb3cgdH1yZXR1cm4gdH0pKTthc3luYyBmdW5jdGlvbiBhKCl7cmV0dXJuIG8oKXx8YXdhaXQgcygpfWV4cG9ydHthIGFzIGdldFJlcXVlc3RMb2NhbGV9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCachedRequestLocale: () => (/* binding */ t),\n/* harmony export */   setCachedRequestLocale: () => (/* binding */ c)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nconst n=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((function(){return{locale:void 0}}));function t(){return n().locale}function c(o){n().locale=o}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZUNhY2hlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBOEIsUUFBUSw0Q0FBQyxhQUFhLE9BQU8sZUFBZSxHQUFHLGFBQWEsa0JBQWtCLGNBQWMsYUFBNkUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2VydmVyL3JlYWN0LXNlcnZlci9SZXF1ZXN0TG9jYWxlQ2FjaGUuanM/OTM5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7Y2FjaGUgYXMgb31mcm9tXCJyZWFjdFwiO2NvbnN0IG49bygoZnVuY3Rpb24oKXtyZXR1cm57bG9jYWxlOnZvaWQgMH19KSk7ZnVuY3Rpb24gdCgpe3JldHVybiBuKCkubG9jYWxlfWZ1bmN0aW9uIGMobyl7bigpLmxvY2FsZT1vfWV4cG9ydHt0IGFzIGdldENhY2hlZFJlcXVlc3RMb2NhbGUsYyBhcyBzZXRDYWNoZWRSZXF1ZXN0TG9jYWxlfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js":
/*!************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRequestLocale: () => (/* binding */ s)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/constants.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js\");\n/* harmony import */ var _RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocaleCache.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\");\nconst i=(0,react__WEBPACK_IMPORTED_MODULE_2__.cache)((function(){let n;try{n=(0,next_headers__WEBPACK_IMPORTED_MODULE_0__.headers)().get(_shared_constants_js__WEBPACK_IMPORTED_MODULE_3__.HEADER_LOCALE_NAME)}catch(e){throw e instanceof Error&&\"DYNAMIC_SERVER_USAGE\"===e.digest?new Error(\"Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering\",{cause:e}):e}return n||(console.error(\"\\nUnable to find `next-intl` locale because the middleware didn't run on this request. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\\n\"),(0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)()),n}));function s(){return (0,_RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__.getCachedRequestLocale)()||i()}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZUxlZ2FjeS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQWdQLFFBQVEsNENBQUMsYUFBYSxNQUFNLElBQUksRUFBRSxxREFBQyxPQUFPLG9FQUFDLEVBQUUsU0FBUyxtWUFBbVksUUFBUSxJQUFJLG9QQUFvUCx5REFBQyxNQUFNLEdBQUcsYUFBYSxPQUFPLDhFQUFDLFFBQXNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZUxlZ2FjeS5qcz9mMjczIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtoZWFkZXJzIGFzIGV9ZnJvbVwibmV4dC9oZWFkZXJzXCI7aW1wb3J0e25vdEZvdW5kIGFzIHR9ZnJvbVwibmV4dC9uYXZpZ2F0aW9uXCI7aW1wb3J0e2NhY2hlIGFzIG59ZnJvbVwicmVhY3RcIjtpbXBvcnR7SEVBREVSX0xPQ0FMRV9OQU1FIGFzIG99ZnJvbVwiLi4vLi4vc2hhcmVkL2NvbnN0YW50cy5qc1wiO2ltcG9ydHtnZXRDYWNoZWRSZXF1ZXN0TG9jYWxlIGFzIHJ9ZnJvbVwiLi9SZXF1ZXN0TG9jYWxlQ2FjaGUuanNcIjtjb25zdCBpPW4oKGZ1bmN0aW9uKCl7bGV0IG47dHJ5e249ZSgpLmdldChvKX1jYXRjaChlKXt0aHJvdyBlIGluc3RhbmNlb2YgRXJyb3ImJlwiRFlOQU1JQ19TRVJWRVJfVVNBR0VcIj09PWUuZGlnZXN0P25ldyBFcnJvcihcIlVzYWdlIG9mIG5leHQtaW50bCBBUElzIGluIFNlcnZlciBDb21wb25lbnRzIGN1cnJlbnRseSBvcHRzIGludG8gZHluYW1pYyByZW5kZXJpbmcuIFRoaXMgbGltaXRhdGlvbiB3aWxsIGV2ZW50dWFsbHkgYmUgbGlmdGVkLCBidXQgYXMgYSBzdG9wZ2FwIHNvbHV0aW9uLCB5b3UgY2FuIHVzZSB0aGUgYHNldFJlcXVlc3RMb2NhbGVgIEFQSSB0byBlbmFibGUgc3RhdGljIHJlbmRlcmluZywgc2VlIGh0dHBzOi8vbmV4dC1pbnRsLmRldi9kb2NzL2dldHRpbmctc3RhcnRlZC9hcHAtcm91dGVyL3dpdGgtaTE4bi1yb3V0aW5nI3N0YXRpYy1yZW5kZXJpbmdcIix7Y2F1c2U6ZX0pOmV9cmV0dXJuIG58fChjb25zb2xlLmVycm9yKFwiXFxuVW5hYmxlIHRvIGZpbmQgYG5leHQtaW50bGAgbG9jYWxlIGJlY2F1c2UgdGhlIG1pZGRsZXdhcmUgZGlkbid0IHJ1biBvbiB0aGlzIHJlcXVlc3QuIFNlZSBodHRwczovL25leHQtaW50bC5kZXYvZG9jcy9yb3V0aW5nL21pZGRsZXdhcmUjdW5hYmxlLXRvLWZpbmQtbG9jYWxlLiBUaGUgYG5vdEZvdW5kKClgIGZ1bmN0aW9uIHdpbGwgYmUgY2FsbGVkIGFzIGEgcmVzdWx0LlxcblwiKSx0KCkpLG59KSk7ZnVuY3Rpb24gcygpe3JldHVybiByKCl8fGkoKX1leHBvcnR7cyBhcyBnZXRSZXF1ZXN0TG9jYWxlfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getConfig.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ w)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/development/core.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\");\n/* harmony import */ var _RequestLocale_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js\");\n/* harmony import */ var _RequestLocaleLegacy_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./RequestLocaleLegacy.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js\");\n/* harmony import */ var next_intl_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl/config */ \"(rsc)/./lib/locale/i18n.ts\");\nlet c=!1,u=!1;const f=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((function(){return new Date}));const d=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((function(){return Intl.DateTimeFormat().resolvedOptions().timeZone}));const m=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((async function(t,n){if(\"function\"!=typeof t)throw new Error(\"Invalid i18n request configuration detected.\\n\\nPlease verify that:\\n1. In case you've specified a custom location in your Next.js config, make sure that the path is correct.\\n2. You have a default export in your i18n request configuration file.\\n\\nSee also: https://next-intl.dev/docs/usage/configuration#i18n-request\\n\");const o={get locale(){return u||(console.warn(\"\\nThe `locale` parameter in `getRequestConfig` is deprecated, please switch to `await requestLocale`. See https://next-intl.dev/blog/next-intl-3-22#await-request-locale\\n\"),u=!0),n||(0,_RequestLocaleLegacy_js__WEBPACK_IMPORTED_MODULE_3__.getRequestLocale)()},get requestLocale(){return n?Promise.resolve(n):(0,_RequestLocale_js__WEBPACK_IMPORTED_MODULE_4__.getRequestLocale)()}};let r=t(o);(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.isPromise)(r)&&(r=await r);let s=r.locale;return s||(c||(console.error(\"\\nA `locale` is expected to be returned from `getRequestConfig`, but none was returned. This will be an error in the next major version of next-intl.\\n\\nSee: https://next-intl.dev/blog/next-intl-3-22#await-request-locale\\n\"),c=!0),s=await o.requestLocale,s||(console.error(\"\\nUnable to find `next-intl` locale because the middleware didn't run on this request and no `locale` was returned in `getRequestConfig`. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\\n\"),(0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)())),{...r,locale:s,now:r.now||f(),timeZone:r.timeZone||d()}})),p=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_6__._createIntlFormatters),g=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_6__._createCache);const w=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((async function(e){const t=await m(next_intl_config__WEBPACK_IMPORTED_MODULE_2__[\"default\"],e);return{...(0,use_intl_core__WEBPACK_IMPORTED_MODULE_6__.initializeConfig)(t),_formatters:p(g())}}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getLocale.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getLocale.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nconst r=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((async function(){const o=await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();return Promise.resolve(o.locale)}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TG9jYWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBNEQsUUFBUSw0Q0FBQyxtQkFBbUIsY0FBYyx5REFBQyxHQUFHLGlDQUFpQyxHQUF3QiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldExvY2FsZS5qcz81MWVjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtjYWNoZSBhcyBvfWZyb21cInJlYWN0XCI7aW1wb3J0IHQgZnJvbVwiLi9nZXRDb25maWcuanNcIjtjb25zdCByPW8oKGFzeW5jIGZ1bmN0aW9uKCl7Y29uc3Qgbz1hd2FpdCB0KCk7cmV0dXJuIFByb21pc2UucmVzb2x2ZShvLmxvY2FsZSl9KSk7ZXhwb3J0e3IgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getMessages.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r),\n/* harmony export */   getMessagesFromConfig: () => (/* binding */ t)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nfunction t(e){if(!e.messages)throw new Error(\"No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages\");return e.messages}const n=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((async function(e){return t(await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(e))}));async function r(e){return n(null==e?void 0:e.locale)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TWVzc2FnZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNEQsY0FBYywrSUFBK0ksa0JBQWtCLFFBQVEsNENBQUMsb0JBQW9CLGVBQWUseURBQUMsS0FBSyxHQUFHLG9CQUFvQixrQ0FBa0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRNZXNzYWdlcy5qcz83NWVmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtjYWNoZSBhcyBlfWZyb21cInJlYWN0XCI7aW1wb3J0IG8gZnJvbVwiLi9nZXRDb25maWcuanNcIjtmdW5jdGlvbiB0KGUpe2lmKCFlLm1lc3NhZ2VzKXRocm93IG5ldyBFcnJvcihcIk5vIG1lc3NhZ2VzIGZvdW5kLiBIYXZlIHlvdSBjb25maWd1cmVkIHRoZW0gY29ycmVjdGx5PyBTZWUgaHR0cHM6Ly9uZXh0LWludGwuZGV2L2RvY3MvY29uZmlndXJhdGlvbiNtZXNzYWdlc1wiKTtyZXR1cm4gZS5tZXNzYWdlc31jb25zdCBuPWUoKGFzeW5jIGZ1bmN0aW9uKGUpe3JldHVybiB0KGF3YWl0IG8oZSkpfSkpO2FzeW5jIGZ1bmN0aW9uIHIoZSl7cmV0dXJuIG4obnVsbD09ZT92b2lkIDA6ZS5sb2NhbGUpfWV4cG9ydHtyIGFzIGRlZmF1bHQsdCBhcyBnZXRNZXNzYWdlc0Zyb21Db25maWd9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getNow.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getNow.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nconst t=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((async function(n){return(await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(n)).now}));async function r(n){return t(null==n?void 0:n.locale)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Tm93LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBNEQsUUFBUSw0Q0FBQyxvQkFBb0IsYUFBYSx5REFBQyxTQUFTLEdBQUcsb0JBQW9CLGtDQUF1RCIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldE5vdy5qcz9lY2NjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtjYWNoZSBhcyBufWZyb21cInJlYWN0XCI7aW1wb3J0IG8gZnJvbVwiLi9nZXRDb25maWcuanNcIjtjb25zdCB0PW4oKGFzeW5jIGZ1bmN0aW9uKG4pe3JldHVybihhd2FpdCBvKG4pKS5ub3d9KSk7YXN5bmMgZnVuY3Rpb24gcihuKXtyZXR1cm4gdChudWxsPT1uP3ZvaWQgMDpuLmxvY2FsZSl9ZXhwb3J0e3IgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getNow.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ t)\n/* harmony export */ });\nfunction t(t){return t}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0UmVxdWVzdENvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsY0FBYyxTQUE4QiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldFJlcXVlc3RDb25maWcuanM/NDliNSJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiB0KHQpe3JldHVybiB0fWV4cG9ydHt0IGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nconst o=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((async function(t){return(await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(t)).timeZone}));async function r(t){return o(null==t?void 0:t.locale)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VGltZVpvbmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE0RCxRQUFRLDRDQUFDLG9CQUFvQixhQUFhLHlEQUFDLGNBQWMsR0FBRyxvQkFBb0Isa0NBQXVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VGltZVpvbmUuanM/MDU0NiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7Y2FjaGUgYXMgdH1mcm9tXCJyZWFjdFwiO2ltcG9ydCBuIGZyb21cIi4vZ2V0Q29uZmlnLmpzXCI7Y29uc3Qgbz10KChhc3luYyBmdW5jdGlvbih0KXtyZXR1cm4oYXdhaXQgbih0KSkudGltZVpvbmV9KSk7YXN5bmMgZnVuY3Rpb24gcih0KXtyZXR1cm4gbyhudWxsPT10P3ZvaWQgMDp0LmxvY2FsZSl9ZXhwb3J0e3IgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTranslations.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getTranslations.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ s)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/development/core.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nvar s=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((async function(e){let s,o;\"string\"==typeof e?s=e:e&&(o=e.locale,s=e.namespace);const r=await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(o);return (0,use_intl_core__WEBPACK_IMPORTED_MODULE_2__.createTranslator)({...r,namespace:s,messages:r.messages})}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VHJhbnNsYXRpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTZHLE1BQU0sNENBQUMsb0JBQW9CLFFBQVEscURBQXFELGNBQWMseURBQUMsSUFBSSxPQUFPLCtEQUFDLEVBQUUscUNBQXFDLEVBQUUsR0FBd0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRUcmFuc2xhdGlvbnMuanM/MTZmMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7Y2FjaGUgYXMgZX1mcm9tXCJyZWFjdFwiO2ltcG9ydHtjcmVhdGVUcmFuc2xhdG9yIGFzIHR9ZnJvbVwidXNlLWludGwvY29yZVwiO2ltcG9ydCBhIGZyb21cIi4vZ2V0Q29uZmlnLmpzXCI7dmFyIHM9ZSgoYXN5bmMgZnVuY3Rpb24oZSl7bGV0IHMsbztcInN0cmluZ1wiPT10eXBlb2YgZT9zPWU6ZSYmKG89ZS5sb2NhbGUscz1lLm5hbWVzcGFjZSk7Y29uc3Qgcj1hd2FpdCBhKG8pO3JldHVybiB0KHsuLi5yLG5hbWVzcGFjZTpzLG1lc3NhZ2VzOnIubWVzc2FnZXN9KX0pKTtleHBvcnR7cyBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTranslations.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\node_modules\next-intl\dist\esm\shared\NextIntlClientProvider.js#default`));


/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/constants.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HEADER_LOCALE_NAME: () => (/* binding */ o),\n/* harmony export */   LOCALE_SEGMENT_NAME: () => (/* binding */ L)\n/* harmony export */ });\nconst o=\"X-NEXT-INTL-LOCALE\",L=\"locale\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NoYXJlZC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSx3Q0FBaUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2hhcmVkL2NvbnN0YW50cy5qcz8zOTNjIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG89XCJYLU5FWFQtSU5UTC1MT0NBTEVcIixMPVwibG9jYWxlXCI7ZXhwb3J0e28gYXMgSEVBREVSX0xPQ0FMRV9OQU1FLEwgYXMgTE9DQUxFX1NFR01FTlRfTkFNRX07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/utils.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocaleAsPrefix: () => (/* binding */ l),\n/* harmony export */   getLocalePrefix: () => (/* binding */ f),\n/* harmony export */   getSortedPathnames: () => (/* binding */ d),\n/* harmony export */   hasPathnamePrefixed: () => (/* binding */ i),\n/* harmony export */   isLocalizableHref: () => (/* binding */ n),\n/* harmony export */   isPromise: () => (/* binding */ v),\n/* harmony export */   localizeHref: () => (/* binding */ t),\n/* harmony export */   matchesPathname: () => (/* binding */ c),\n/* harmony export */   normalizeTrailingSlash: () => (/* binding */ o),\n/* harmony export */   prefixHref: () => (/* binding */ e),\n/* harmony export */   prefixPathname: () => (/* binding */ u),\n/* harmony export */   templateToRegex: () => (/* binding */ s),\n/* harmony export */   unprefixPathname: () => (/* binding */ r)\n/* harmony export */ });\nfunction n(n){return function(n){return\"object\"==typeof n?null==n.host&&null==n.hostname:!/^[a-z]+:/i.test(n)}(n)&&!function(n){const t=\"object\"==typeof n?n.pathname:n;return null!=t&&!t.startsWith(\"/\")}(n)}function t(t,r){let u=arguments.length>2&&void 0!==arguments[2]?arguments[2]:r,o=arguments.length>3?arguments[3]:void 0,c=arguments.length>4?arguments[4]:void 0;if(!n(t))return t;const f=r!==u,l=i(c,o);return(f||l)&&null!=c?e(t,c):t}function e(n,t){let e;return\"string\"==typeof n?e=u(t,n):(e={...n},n.pathname&&(e.pathname=u(t,n.pathname))),e}function r(n,t){return n.replace(new RegExp(\"^\".concat(t)),\"\")||\"/\"}function u(n,t){let e=n;return/^\\/(\\?.*)?$/.test(t)&&(t=t.slice(1)),e+=t,e}function i(n,t){return t===n||t.startsWith(\"\".concat(n,\"/\"))}function o(n){const t=function(){try{return\"true\"===process.env._next_intl_trailing_slash}catch(n){return!1}}();if(\"/\"!==n){const e=n.endsWith(\"/\");t&&!e?n+=\"/\":!t&&e&&(n=n.slice(0,-1))}return n}function c(n,t){const e=o(n),r=o(t);return s(e).test(r)}function f(n,t){var e;return\"never\"!==t.mode&&(null===(e=t.prefixes)||void 0===e?void 0:e[n])||l(n)}function l(n){return\"/\"+n}function s(n){const t=n.replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g,\"?(.*)\").replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g,\"(.+)\").replace(/\\[([^\\]]+)\\]/g,\"([^/]+)\");return new RegExp(\"^\".concat(t,\"$\"))}function a(n){return n.includes(\"[[...\")}function p(n){return n.includes(\"[...\")}function h(n){return n.includes(\"[\")}function g(n,t){const e=n.split(\"/\"),r=t.split(\"/\"),u=Math.max(e.length,r.length);for(let n=0;n<u;n++){const t=e[n],u=r[n];if(!t&&u)return-1;if(t&&!u)return 1;if(t||u){if(!h(t)&&h(u))return-1;if(h(t)&&!h(u))return 1;if(!p(t)&&p(u))return-1;if(p(t)&&!p(u))return 1;if(!a(t)&&a(u))return-1;if(a(t)&&!a(u))return 1}}return 0}function d(n){return n.sort(g)}function v(n){return\"function\"==typeof n.then}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\n");

/***/ })

};
;
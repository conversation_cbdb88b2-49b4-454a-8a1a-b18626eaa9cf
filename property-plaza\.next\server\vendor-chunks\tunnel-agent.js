"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tunnel-agent";
exports.ids = ["vendor-chunks/tunnel-agent"];
exports.modules = {

/***/ "(ssr)/./node_modules/tunnel-agent/index.js":
/*!********************************************!*\
  !*** ./node_modules/tunnel-agent/index.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar net = __webpack_require__(/*! net */ \"net\")\n  , tls = __webpack_require__(/*! tls */ \"tls\")\n  , http = __webpack_require__(/*! http */ \"http\")\n  , https = __webpack_require__(/*! https */ \"https\")\n  , events = __webpack_require__(/*! events */ \"events\")\n  , assert = __webpack_require__(/*! assert */ \"assert\")\n  , util = __webpack_require__(/*! util */ \"util\")\n  , Buffer = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer)\n  ;\n\nexports.httpOverHttp = httpOverHttp\nexports.httpsOverHttp = httpsOverHttp\nexports.httpOverHttps = httpOverHttps\nexports.httpsOverHttps = httpsOverHttps\n\n\nfunction httpOverHttp(options) {\n  var agent = new TunnelingAgent(options)\n  agent.request = http.request\n  return agent\n}\n\nfunction httpsOverHttp(options) {\n  var agent = new TunnelingAgent(options)\n  agent.request = http.request\n  agent.createSocket = createSecureSocket\n  agent.defaultPort = 443\n  return agent\n}\n\nfunction httpOverHttps(options) {\n  var agent = new TunnelingAgent(options)\n  agent.request = https.request\n  return agent\n}\n\nfunction httpsOverHttps(options) {\n  var agent = new TunnelingAgent(options)\n  agent.request = https.request\n  agent.createSocket = createSecureSocket\n  agent.defaultPort = 443\n  return agent\n}\n\n\nfunction TunnelingAgent(options) {\n  var self = this\n  self.options = options || {}\n  self.proxyOptions = self.options.proxy || {}\n  self.maxSockets = self.options.maxSockets || http.Agent.defaultMaxSockets\n  self.requests = []\n  self.sockets = []\n\n  self.on('free', function onFree(socket, host, port) {\n    for (var i = 0, len = self.requests.length; i < len; ++i) {\n      var pending = self.requests[i]\n      if (pending.host === host && pending.port === port) {\n        // Detect the request to connect same origin server,\n        // reuse the connection.\n        self.requests.splice(i, 1)\n        pending.request.onSocket(socket)\n        return\n      }\n    }\n    socket.destroy()\n    self.removeSocket(socket)\n  })\n}\nutil.inherits(TunnelingAgent, events.EventEmitter)\n\nTunnelingAgent.prototype.addRequest = function addRequest(req, options) {\n  var self = this\n\n   // Legacy API: addRequest(req, host, port, path)\n  if (typeof options === 'string') {\n    options = {\n      host: options,\n      port: arguments[2],\n      path: arguments[3]\n    };\n  }\n\n  if (self.sockets.length >= this.maxSockets) {\n    // We are over limit so we'll add it to the queue.\n    self.requests.push({host: options.host, port: options.port, request: req})\n    return\n  }\n\n  // If we are under maxSockets create a new one.\n  self.createConnection({host: options.host, port: options.port, request: req})\n}\n\nTunnelingAgent.prototype.createConnection = function createConnection(pending) {\n  var self = this\n\n  self.createSocket(pending, function(socket) {\n    socket.on('free', onFree)\n    socket.on('close', onCloseOrRemove)\n    socket.on('agentRemove', onCloseOrRemove)\n    pending.request.onSocket(socket)\n\n    function onFree() {\n      self.emit('free', socket, pending.host, pending.port)\n    }\n\n    function onCloseOrRemove(err) {\n      self.removeSocket(socket)\n      socket.removeListener('free', onFree)\n      socket.removeListener('close', onCloseOrRemove)\n      socket.removeListener('agentRemove', onCloseOrRemove)\n    }\n  })\n}\n\nTunnelingAgent.prototype.createSocket = function createSocket(options, cb) {\n  var self = this\n  var placeholder = {}\n  self.sockets.push(placeholder)\n\n  var connectOptions = mergeOptions({}, self.proxyOptions,\n    { method: 'CONNECT'\n    , path: options.host + ':' + options.port\n    , agent: false\n    }\n  )\n  if (connectOptions.proxyAuth) {\n    connectOptions.headers = connectOptions.headers || {}\n    connectOptions.headers['Proxy-Authorization'] = 'Basic ' +\n        Buffer.from(connectOptions.proxyAuth).toString('base64')\n  }\n\n  debug('making CONNECT request')\n  var connectReq = self.request(connectOptions)\n  connectReq.useChunkedEncodingByDefault = false // for v0.6\n  connectReq.once('response', onResponse) // for v0.6\n  connectReq.once('upgrade', onUpgrade)   // for v0.6\n  connectReq.once('connect', onConnect)   // for v0.7 or later\n  connectReq.once('error', onError)\n  connectReq.end()\n\n  function onResponse(res) {\n    // Very hacky. This is necessary to avoid http-parser leaks.\n    res.upgrade = true\n  }\n\n  function onUpgrade(res, socket, head) {\n    // Hacky.\n    process.nextTick(function() {\n      onConnect(res, socket, head)\n    })\n  }\n\n  function onConnect(res, socket, head) {\n    connectReq.removeAllListeners()\n    socket.removeAllListeners()\n\n    if (res.statusCode === 200) {\n      assert.equal(head.length, 0)\n      debug('tunneling connection has established')\n      self.sockets[self.sockets.indexOf(placeholder)] = socket\n      cb(socket)\n    } else {\n      debug('tunneling socket could not be established, statusCode=%d', res.statusCode)\n      var error = new Error('tunneling socket could not be established, ' + 'statusCode=' + res.statusCode)\n      error.code = 'ECONNRESET'\n      options.request.emit('error', error)\n      self.removeSocket(placeholder)\n    }\n  }\n\n  function onError(cause) {\n    connectReq.removeAllListeners()\n\n    debug('tunneling socket could not be established, cause=%s\\n', cause.message, cause.stack)\n    var error = new Error('tunneling socket could not be established, ' + 'cause=' + cause.message)\n    error.code = 'ECONNRESET'\n    options.request.emit('error', error)\n    self.removeSocket(placeholder)\n  }\n}\n\nTunnelingAgent.prototype.removeSocket = function removeSocket(socket) {\n  var pos = this.sockets.indexOf(socket)\n  if (pos === -1) return\n\n  this.sockets.splice(pos, 1)\n\n  var pending = this.requests.shift()\n  if (pending) {\n    // If we have pending requests and a socket gets closed a new one\n    // needs to be created to take over in the pool for the one that closed.\n    this.createConnection(pending)\n  }\n}\n\nfunction createSecureSocket(options, cb) {\n  var self = this\n  TunnelingAgent.prototype.createSocket.call(self, options, function(socket) {\n    // 0 is dummy port for v0.6\n    var secureSocket = tls.connect(0, mergeOptions({}, self.options,\n      { servername: options.host\n      , socket: socket\n      }\n    ))\n    self.sockets[self.sockets.indexOf(socket)] = secureSocket\n    cb(secureSocket)\n  })\n}\n\n\nfunction mergeOptions(target) {\n  for (var i = 1, len = arguments.length; i < len; ++i) {\n    var overrides = arguments[i]\n    if (typeof overrides === 'object') {\n      var keys = Object.keys(overrides)\n      for (var j = 0, keyLen = keys.length; j < keyLen; ++j) {\n        var k = keys[j]\n        if (overrides[k] !== undefined) {\n          target[k] = overrides[k]\n        }\n      }\n    }\n  }\n  return target\n}\n\n\nvar debug\nif (process.env.NODE_DEBUG && /\\btunnel\\b/.test(process.env.NODE_DEBUG)) {\n  debug = function() {\n    var args = Array.prototype.slice.call(arguments)\n    if (typeof args[0] === 'string') {\n      args[0] = 'TUNNEL: ' + args[0]\n    } else {\n      args.unshift('TUNNEL:')\n    }\n    console.error.apply(console, args)\n  }\n} else {\n  debug = function() {}\n}\nexports.debug = debug // for test\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tunnel-agent/index.js\n");

/***/ })

};
;
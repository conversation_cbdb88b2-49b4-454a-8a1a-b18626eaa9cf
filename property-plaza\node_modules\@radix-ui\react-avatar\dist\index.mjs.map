{"version": 3, "sources": ["../src/Avatar.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Avatar\n * -----------------------------------------------------------------------------------------------*/\n\nconst AVATAR_NAME = 'Avatar';\n\ntype ScopedProps<P> = P & { __scopeAvatar?: Scope };\nconst [createAvatarContext, createAvatarScope] = createContextScope(AVATAR_NAME);\n\ntype ImageLoadingStatus = 'idle' | 'loading' | 'loaded' | 'error';\n\ntype AvatarContextValue = {\n  imageLoadingStatus: ImageLoadingStatus;\n  onImageLoadingStatusChange(status: ImageLoadingStatus): void;\n};\n\nconst [AvatarProvider, useAvatarContext] = createAvatarContext<AvatarContextValue>(AVATAR_NAME);\n\ntype AvatarElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface AvatarProps extends PrimitiveSpanProps {}\n\nconst Avatar = React.forwardRef<AvatarElement, AvatarProps>(\n  (props: ScopedProps<AvatarProps>, forwardedRef) => {\n    const { __scopeAvatar, ...avatarProps } = props;\n    const [imageLoadingStatus, setImageLoadingStatus] = React.useState<ImageLoadingStatus>('idle');\n    return (\n      <AvatarProvider\n        scope={__scopeAvatar}\n        imageLoadingStatus={imageLoadingStatus}\n        onImageLoadingStatusChange={setImageLoadingStatus}\n      >\n        <Primitive.span {...avatarProps} ref={forwardedRef} />\n      </AvatarProvider>\n    );\n  }\n);\n\nAvatar.displayName = AVATAR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AvatarImage\n * -----------------------------------------------------------------------------------------------*/\n\nconst IMAGE_NAME = 'AvatarImage';\n\ntype AvatarImageElement = React.ElementRef<typeof Primitive.img>;\ntype PrimitiveImageProps = React.ComponentPropsWithoutRef<typeof Primitive.img>;\ninterface AvatarImageProps extends PrimitiveImageProps {\n  onLoadingStatusChange?: (status: ImageLoadingStatus) => void;\n}\n\nconst AvatarImage = React.forwardRef<AvatarImageElement, AvatarImageProps>(\n  (props: ScopedProps<AvatarImageProps>, forwardedRef) => {\n    const { __scopeAvatar, src, onLoadingStatusChange = () => {}, ...imageProps } = props;\n    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);\n    const imageLoadingStatus = useImageLoadingStatus(src);\n    const handleLoadingStatusChange = useCallbackRef((status: ImageLoadingStatus) => {\n      onLoadingStatusChange(status);\n      context.onImageLoadingStatusChange(status);\n    });\n\n    useLayoutEffect(() => {\n      if (imageLoadingStatus !== 'idle') {\n        handleLoadingStatusChange(imageLoadingStatus);\n      }\n    }, [imageLoadingStatus, handleLoadingStatusChange]);\n\n    return imageLoadingStatus === 'loaded' ? (\n      <Primitive.img {...imageProps} ref={forwardedRef} src={src} />\n    ) : null;\n  }\n);\n\nAvatarImage.displayName = IMAGE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AvatarFallback\n * -----------------------------------------------------------------------------------------------*/\n\nconst FALLBACK_NAME = 'AvatarFallback';\n\ntype AvatarFallbackElement = React.ElementRef<typeof Primitive.span>;\ninterface AvatarFallbackProps extends PrimitiveSpanProps {\n  delayMs?: number;\n}\n\nconst AvatarFallback = React.forwardRef<AvatarFallbackElement, AvatarFallbackProps>(\n  (props: ScopedProps<AvatarFallbackProps>, forwardedRef) => {\n    const { __scopeAvatar, delayMs, ...fallbackProps } = props;\n    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);\n    const [canRender, setCanRender] = React.useState(delayMs === undefined);\n\n    React.useEffect(() => {\n      if (delayMs !== undefined) {\n        const timerId = window.setTimeout(() => setCanRender(true), delayMs);\n        return () => window.clearTimeout(timerId);\n      }\n    }, [delayMs]);\n\n    return canRender && context.imageLoadingStatus !== 'loaded' ? (\n      <Primitive.span {...fallbackProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nAvatarFallback.displayName = FALLBACK_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction useImageLoadingStatus(src?: string) {\n  const [loadingStatus, setLoadingStatus] = React.useState<ImageLoadingStatus>('idle');\n\n  useLayoutEffect(() => {\n    if (!src) {\n      setLoadingStatus('error');\n      return;\n    }\n\n    let isMounted = true;\n    const image = new window.Image();\n\n    const updateStatus = (status: ImageLoadingStatus) => () => {\n      if (!isMounted) return;\n      setLoadingStatus(status);\n    };\n\n    setLoadingStatus('loading');\n    image.onload = updateStatus('loaded');\n    image.onerror = updateStatus('error');\n    image.src = src;\n\n    return () => {\n      isMounted = false;\n    };\n  }, [src]);\n\n  return loadingStatus;\n}\nconst Root = Avatar;\nconst Image = AvatarImage;\nconst Fallback = AvatarFallback;\n\nexport {\n  createAvatarScope,\n  //\n  Avatar,\n  AvatarImage,\n  AvatarFallback,\n  //\n  Root,\n  Image,\n  Fallback,\n};\nexport type { AvatarProps, AvatarImageProps, AvatarFallbackProps };\n"], "mappings": ";;;AAAA,YAAY,WAAW;AACvB,SAAS,0BAA0B;AACnC,SAAS,sBAAsB;AAC/B,SAAS,uBAAuB;AAChC,SAAS,iBAAiB;AAoClB;AA5BR,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,IAAI,mBAAmB,WAAW;AAS/E,IAAM,CAAC,gBAAgB,gBAAgB,IAAI,oBAAwC,WAAW;AAM9F,IAAM,SAAe;AAAA,EACnB,CAAC,OAAiC,iBAAiB;AACjD,UAAM,EAAE,eAAe,GAAG,YAAY,IAAI;AAC1C,UAAM,CAAC,oBAAoB,qBAAqB,IAAU,eAA6B,MAAM;AAC7F,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO;AAAA,QACP;AAAA,QACA,4BAA4B;AAAA,QAE5B,8BAAC,UAAU,MAAV,EAAgB,GAAG,aAAa,KAAK,cAAc;AAAA;AAAA,IACtD;AAAA,EAEJ;AACF;AAEA,OAAO,cAAc;AAMrB,IAAM,aAAa;AAQnB,IAAM,cAAoB;AAAA,EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,eAAe,KAAK,wBAAwB,MAAM;AAAA,IAAC,GAAG,GAAG,WAAW,IAAI;AAChF,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,UAAM,qBAAqB,sBAAsB,GAAG;AACpD,UAAM,4BAA4B,eAAe,CAAC,WAA+B;AAC/E,4BAAsB,MAAM;AAC5B,cAAQ,2BAA2B,MAAM;AAAA,IAC3C,CAAC;AAED,oBAAgB,MAAM;AACpB,UAAI,uBAAuB,QAAQ;AACjC,kCAA0B,kBAAkB;AAAA,MAC9C;AAAA,IACF,GAAG,CAAC,oBAAoB,yBAAyB,CAAC;AAElD,WAAO,uBAAuB,WAC5B,oBAAC,UAAU,KAAV,EAAe,GAAG,YAAY,KAAK,cAAc,KAAU,IAC1D;AAAA,EACN;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,gBAAgB;AAOtB,IAAM,iBAAuB;AAAA,EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,EAAE,eAAe,SAAS,GAAG,cAAc,IAAI;AACrD,UAAM,UAAU,iBAAiB,eAAe,aAAa;AAC7D,UAAM,CAAC,WAAW,YAAY,IAAU,eAAS,YAAY,MAAS;AAEtE,IAAM,gBAAU,MAAM;AACpB,UAAI,YAAY,QAAW;AACzB,cAAM,UAAU,OAAO,WAAW,MAAM,aAAa,IAAI,GAAG,OAAO;AACnE,eAAO,MAAM,OAAO,aAAa,OAAO;AAAA,MAC1C;AAAA,IACF,GAAG,CAAC,OAAO,CAAC;AAEZ,WAAO,aAAa,QAAQ,uBAAuB,WACjD,oBAAC,UAAU,MAAV,EAAgB,GAAG,eAAe,KAAK,cAAc,IACpD;AAAA,EACN;AACF;AAEA,eAAe,cAAc;AAI7B,SAAS,sBAAsB,KAAc;AAC3C,QAAM,CAAC,eAAe,gBAAgB,IAAU,eAA6B,MAAM;AAEnF,kBAAgB,MAAM;AACpB,QAAI,CAAC,KAAK;AACR,uBAAiB,OAAO;AACxB;AAAA,IACF;AAEA,QAAI,YAAY;AAChB,UAAM,QAAQ,IAAI,OAAO,MAAM;AAE/B,UAAM,eAAe,CAAC,WAA+B,MAAM;AACzD,UAAI,CAAC,UAAW;AAChB,uBAAiB,MAAM;AAAA,IACzB;AAEA,qBAAiB,SAAS;AAC1B,UAAM,SAAS,aAAa,QAAQ;AACpC,UAAM,UAAU,aAAa,OAAO;AACpC,UAAM,MAAM;AAEZ,WAAO,MAAM;AACX,kBAAY;AAAA,IACd;AAAA,EACF,GAAG,CAAC,GAAG,CAAC;AAER,SAAO;AACT;AACA,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,WAAW;", "names": []}
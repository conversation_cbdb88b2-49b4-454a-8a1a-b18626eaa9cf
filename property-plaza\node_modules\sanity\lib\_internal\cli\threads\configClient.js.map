{"version": 3, "file": "configClient.js", "sources": ["../../../../src/_internal/cli/threads/configClient.ts"], "sourcesContent": ["import {getCliClient} from '@sanity/cli'\nimport ConfigStore from 'configstore'\n\n// eslint-disable-next-line no-process-env\nconst sanityEnv = (process.env.SANITY_INTERNAL_ENV || '').toLowerCase()\nconst defaults = {}\nconst config = new ConfigStore(\n  sanityEnv && sanityEnv !== 'production' ? `sanity-${sanityEnv}` : 'sanity',\n  defaults,\n  {globalConfigPath: true},\n)\n\nconst token = config.get('authToken')\nif (!token) {\n  throw new Error(\n    '--with-user-token specified, but no auth token could be found. Run `sanity login`',\n  )\n}\n\n// eslint-disable-next-line camelcase\ngetCliClient.__internal__getToken = () => token\n"], "names": ["sanityEnv", "process", "env", "SANITY_INTERNAL_ENV", "toLowerCase", "defaults", "config", "ConfigStore", "globalConfigPath", "token", "get", "Error", "getCliClient", "__internal__getToken"], "mappings": ";;;;;;AAIA,MAAMA,aAAaC,QAAQC,IAAIC,uBAAuB,IAAIC,YAAY,GAChEC,WAAW,IACXC,SAAS,IAAIC,6BACjBP,aAAaA,cAAc,eAAe,UAAUA,SAAS,KAAK,UAClEK,UACA;AAAA,EAACG,kBAAkB;AAAI,CACzB,GAEMC,QAAQH,OAAOI,IAAI,WAAW;AACpC,IAAI,CAACD;AACG,QAAA,IAAIE,MACR,mFACF;AAIFC,IAAAA,aAAaC,uBAAuB,MAAMJ;"}
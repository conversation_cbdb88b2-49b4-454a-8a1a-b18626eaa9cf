"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/vaul";
exports.ids = ["vendor-chunks/vaul"];
exports.modules = {

/***/ "(ssr)/./node_modules/vaul/dist/index.mjs":
/*!******************************************!*\
  !*** ./node_modules/vaul/dist/index.mjs ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Drawer: () => (/* binding */ Drawer),\n/* harmony export */   Handle: () => (/* binding */ Handle),\n/* harmony export */   NestedRoot: () => (/* binding */ NestedRoot),\n/* harmony export */   Overlay: () => (/* binding */ Overlay),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ Content,Drawer,Handle,NestedRoot,Overlay,Portal,Root auto */ function __insertCSS(code) {\n    if (!code || typeof document == \"undefined\") return;\n    let head = document.head || document.getElementsByTagName(\"head\")[0];\n    let style = document.createElement(\"style\");\n    style.type = \"text/css\";\n    head.appendChild(style);\n    style.styleSheet ? style.styleSheet.cssText = code : style.appendChild(document.createTextNode(code));\n}\n\n\n\nconst DrawerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    drawerRef: {\n        current: null\n    },\n    overlayRef: {\n        current: null\n    },\n    onPress: ()=>{},\n    onRelease: ()=>{},\n    onDrag: ()=>{},\n    onNestedDrag: ()=>{},\n    onNestedOpenChange: ()=>{},\n    onNestedRelease: ()=>{},\n    openProp: undefined,\n    dismissible: false,\n    isOpen: false,\n    isDragging: false,\n    keyboardIsOpen: {\n        current: false\n    },\n    snapPointsOffset: null,\n    snapPoints: null,\n    handleOnly: false,\n    modal: false,\n    shouldFade: false,\n    activeSnapPoint: null,\n    onOpenChange: ()=>{},\n    setActiveSnapPoint: ()=>{},\n    closeDrawer: ()=>{},\n    direction: \"bottom\",\n    shouldScaleBackground: false,\n    setBackgroundColorOnScale: true,\n    noBodyStyles: false,\n    container: null,\n    autoFocus: false\n});\nconst useDrawerContext = ()=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DrawerContext);\n    if (!context) {\n        throw new Error(\"useDrawerContext must be used within a Drawer.Root\");\n    }\n    return context;\n};\n__insertCSS(\"[data-vaul-drawer]{touch-action:none;will-change:transform;transition:transform .5s cubic-bezier(.32, .72, 0, 1);animation-duration:.5s;animation-timing-function:cubic-bezier(0.32,0.72,0,1)}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=open]{animation-name:slideFromBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=closed]{animation-name:slideToBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=open]{animation-name:slideFromTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=closed]{animation-name:slideToTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=open]{animation-name:slideFromLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=closed]{animation-name:slideToLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=open]{animation-name:slideFromRight}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=closed]{animation-name:slideToRight}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,100%,0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,-100%,0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(-100%,0,0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(100%,0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,var(--snap-point-height,0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,var(--snap-point-height,0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(var(--snap-point-height,0),0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(var(--snap-point-height,0),0,0)}[data-vaul-overlay][data-vaul-snap-points=false]{animation-duration:.5s;animation-timing-function:cubic-bezier(0.32,0.72,0,1)}[data-vaul-overlay][data-vaul-snap-points=false][data-state=open]{animation-name:fadeIn}[data-vaul-overlay][data-state=closed]{animation-name:fadeOut}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:0;transition:opacity .5s cubic-bezier(.32, .72, 0, 1)}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:1}[data-vaul-drawer]:not([data-vaul-custom-container=true])::after{content:'';position:absolute;background:inherit;background-color:inherit}[data-vaul-drawer][data-vaul-drawer-direction=top]::after{top:initial;bottom:100%;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=bottom]::after{top:100%;bottom:initial;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=left]::after{left:initial;right:100%;top:0;bottom:0;width:200%}[data-vaul-drawer][data-vaul-drawer-direction=right]::after{left:100%;right:initial;top:0;bottom:0;width:200%}[data-vaul-overlay][data-vaul-snap-points=true]:not([data-vaul-snap-points-overlay=true]):not(\\n[data-state=closed]\\n){opacity:0}[data-vaul-overlay][data-vaul-snap-points-overlay=true]{opacity:1}[data-vaul-handle]{display:block;position:relative;opacity:.7;background:#e2e2e4;margin-left:auto;margin-right:auto;height:5px;width:32px;border-radius:1rem;touch-action:pan-y}[data-vaul-handle]:active,[data-vaul-handle]:hover{opacity:1}[data-vaul-handle-hitarea]{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:max(100%,2.75rem);height:max(100%,2.75rem);touch-action:inherit}@media (hover:hover) and (pointer:fine){[data-vaul-drawer]{user-select:none}}@media (pointer:fine){[data-vaul-handle-hitarea]:{width:100%;height:100%}}@keyframes fadeIn{from{opacity:0}to{opacity:1}}@keyframes fadeOut{to{opacity:0}}@keyframes slideFromBottom{from{transform:translate3d(0,100%,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToBottom{to{transform:translate3d(0,100%,0)}}@keyframes slideFromTop{from{transform:translate3d(0,-100%,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToTop{to{transform:translate3d(0,-100%,0)}}@keyframes slideFromLeft{from{transform:translate3d(-100%,0,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToLeft{to{transform:translate3d(-100%,0,0)}}@keyframes slideFromRight{from{transform:translate3d(100%,0,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToRight{to{transform:translate3d(100%,0,0)}}\");\n// This code comes from https://github.com/adobe/react-spectrum/blob/main/packages/%40react-aria/overlays/src/usePreventScroll.ts\nconst KEYBOARD_BUFFER = 24;\nconst useIsomorphicLayoutEffect =  false ? 0 : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\nfunction chain$1(...callbacks) {\n    return (...args)=>{\n        for (let callback of callbacks){\n            if (typeof callback === \"function\") {\n                callback(...args);\n            }\n        }\n    };\n}\nfunction isMac() {\n    return testPlatform(/^Mac/);\n}\nfunction isIPhone() {\n    return testPlatform(/^iPhone/);\n}\nfunction isSafari() {\n    return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n}\nfunction isIPad() {\n    return testPlatform(/^iPad/) || // iPadOS 13 lies and says it's a Mac, but we can distinguish by detecting touch support.\n    isMac() && navigator.maxTouchPoints > 1;\n}\nfunction isIOS() {\n    return isIPhone() || isIPad();\n}\nfunction testPlatform(re) {\n    return  false ? 0 : undefined;\n}\n// @ts-ignore\nconst visualViewport = typeof document !== \"undefined\" && window.visualViewport;\nfunction isScrollable(node) {\n    let style = window.getComputedStyle(node);\n    return /(auto|scroll)/.test(style.overflow + style.overflowX + style.overflowY);\n}\nfunction getScrollParent(node) {\n    if (isScrollable(node)) {\n        node = node.parentElement;\n    }\n    while(node && !isScrollable(node)){\n        node = node.parentElement;\n    }\n    return node || document.scrollingElement || document.documentElement;\n}\n// HTML input types that do not cause the software keyboard to appear.\nconst nonTextInputTypes = new Set([\n    \"checkbox\",\n    \"radio\",\n    \"range\",\n    \"color\",\n    \"file\",\n    \"image\",\n    \"button\",\n    \"submit\",\n    \"reset\"\n]);\n// The number of active usePreventScroll calls. Used to determine whether to revert back to the original page style/scroll position\nlet preventScrollCount = 0;\nlet restore;\n/**\n * Prevents scrolling on the document body on mount, and\n * restores it on unmount. Also ensures that content does not\n * shift due to the scrollbars disappearing.\n */ function usePreventScroll(options = {}) {\n    let { isDisabled } = options;\n    useIsomorphicLayoutEffect(()=>{\n        if (isDisabled) {\n            return;\n        }\n        preventScrollCount++;\n        if (preventScrollCount === 1) {\n            if (isIOS()) {\n                restore = preventScrollMobileSafari();\n            }\n        }\n        return ()=>{\n            preventScrollCount--;\n            if (preventScrollCount === 0) {\n                restore == null ? void 0 : restore();\n            }\n        };\n    }, [\n        isDisabled\n    ]);\n}\n// Mobile Safari is a whole different beast. Even with overflow: hidden,\n// it still scrolls the page in many situations:\n//\n// 1. When the bottom toolbar and address bar are collapsed, page scrolling is always allowed.\n// 2. When the keyboard is visible, the viewport does not resize. Instead, the keyboard covers part of\n//    it, so it becomes scrollable.\n// 3. When tapping on an input, the page always scrolls so that the input is centered in the visual viewport.\n//    This may cause even fixed position elements to scroll off the screen.\n// 4. When using the next/previous buttons in the keyboard to navigate between inputs, the whole page always\n//    scrolls, even if the input is inside a nested scrollable element that could be scrolled instead.\n//\n// In order to work around these cases, and prevent scrolling without jankiness, we do a few things:\n//\n// 1. Prevent default on `touchmove` events that are not in a scrollable element. This prevents touch scrolling\n//    on the window.\n// 2. Prevent default on `touchmove` events inside a scrollable element when the scroll position is at the\n//    top or bottom. This avoids the whole page scrolling instead, but does prevent overscrolling.\n// 3. Prevent default on `touchend` events on input elements and handle focusing the element ourselves.\n// 4. When focusing an input, apply a transform to trick Safari into thinking the input is at the top\n//    of the page, which prevents it from scrolling the page. After the input is focused, scroll the element\n//    into view ourselves, without scrolling the whole page.\n// 5. Offset the body by the scroll position using a negative margin and scroll to the top. This should appear the\n//    same visually, but makes the actual scroll position always zero. This is required to make all of the\n//    above work or Safari will still try to scroll the page when focusing an input.\n// 6. As a last resort, handle window scroll events, and scroll back to the top. This can happen when attempting\n//    to navigate to an input with the next/previous buttons that's outside a modal.\nfunction preventScrollMobileSafari() {\n    let scrollable;\n    let lastY = 0;\n    let onTouchStart = (e)=>{\n        // Store the nearest scrollable parent element from the element that the user touched.\n        scrollable = getScrollParent(e.target);\n        if (scrollable === document.documentElement && scrollable === document.body) {\n            return;\n        }\n        lastY = e.changedTouches[0].pageY;\n    };\n    let onTouchMove = (e)=>{\n        // Prevent scrolling the window.\n        if (!scrollable || scrollable === document.documentElement || scrollable === document.body) {\n            e.preventDefault();\n            return;\n        }\n        // Prevent scrolling up when at the top and scrolling down when at the bottom\n        // of a nested scrollable area, otherwise mobile Safari will start scrolling\n        // the window instead. Unfortunately, this disables bounce scrolling when at\n        // the top but it's the best we can do.\n        let y = e.changedTouches[0].pageY;\n        let scrollTop = scrollable.scrollTop;\n        let bottom = scrollable.scrollHeight - scrollable.clientHeight;\n        if (bottom === 0) {\n            return;\n        }\n        if (scrollTop <= 0 && y > lastY || scrollTop >= bottom && y < lastY) {\n            e.preventDefault();\n        }\n        lastY = y;\n    };\n    let onTouchEnd = (e)=>{\n        let target = e.target;\n        // Apply this change if we're not already focused on the target element\n        if (isInput(target) && target !== document.activeElement) {\n            e.preventDefault();\n            // Apply a transform to trick Safari into thinking the input is at the top of the page\n            // so it doesn't try to scroll it into view. When tapping on an input, this needs to\n            // be done before the \"focus\" event, so we have to focus the element ourselves.\n            target.style.transform = \"translateY(-2000px)\";\n            target.focus();\n            requestAnimationFrame(()=>{\n                target.style.transform = \"\";\n            });\n        }\n    };\n    let onFocus = (e)=>{\n        let target = e.target;\n        if (isInput(target)) {\n            // Transform also needs to be applied in the focus event in cases where focus moves\n            // other than tapping on an input directly, e.g. the next/previous buttons in the\n            // software keyboard. In these cases, it seems applying the transform in the focus event\n            // is good enough, whereas when tapping an input, it must be done before the focus event. 🤷‍♂️\n            target.style.transform = \"translateY(-2000px)\";\n            requestAnimationFrame(()=>{\n                target.style.transform = \"\";\n                // This will have prevented the browser from scrolling the focused element into view,\n                // so we need to do this ourselves in a way that doesn't cause the whole page to scroll.\n                if (visualViewport) {\n                    if (visualViewport.height < window.innerHeight) {\n                        // If the keyboard is already visible, do this after one additional frame\n                        // to wait for the transform to be removed.\n                        requestAnimationFrame(()=>{\n                            scrollIntoView(target);\n                        });\n                    } else {\n                        // Otherwise, wait for the visual viewport to resize before scrolling so we can\n                        // measure the correct position to scroll to.\n                        visualViewport.addEventListener(\"resize\", ()=>scrollIntoView(target), {\n                            once: true\n                        });\n                    }\n                }\n            });\n        }\n    };\n    let onWindowScroll = ()=>{\n        // Last resort. If the window scrolled, scroll it back to the top.\n        // It should always be at the top because the body will have a negative margin (see below).\n        window.scrollTo(0, 0);\n    };\n    // Record the original scroll position so we can restore it.\n    // Then apply a negative margin to the body to offset it by the scroll position. This will\n    // enable us to scroll the window to the top, which is required for the rest of this to work.\n    let scrollX = window.pageXOffset;\n    let scrollY = window.pageYOffset;\n    let restoreStyles = chain$1(setStyle(document.documentElement, \"paddingRight\", `${window.innerWidth - document.documentElement.clientWidth}px`));\n    // Scroll to the top. The negative margin on the body will make this appear the same.\n    window.scrollTo(0, 0);\n    let removeEvents = chain$1(addEvent(document, \"touchstart\", onTouchStart, {\n        passive: false,\n        capture: true\n    }), addEvent(document, \"touchmove\", onTouchMove, {\n        passive: false,\n        capture: true\n    }), addEvent(document, \"touchend\", onTouchEnd, {\n        passive: false,\n        capture: true\n    }), addEvent(document, \"focus\", onFocus, true), addEvent(window, \"scroll\", onWindowScroll));\n    return ()=>{\n        // Restore styles and scroll the page back to where it was.\n        restoreStyles();\n        removeEvents();\n        window.scrollTo(scrollX, scrollY);\n    };\n}\n// Sets a CSS property on an element, and returns a function to revert it to the previous value.\nfunction setStyle(element, style, value) {\n    let cur = element.style[style];\n    element.style[style] = value;\n    return ()=>{\n        element.style[style] = cur;\n    };\n}\n// Adds an event listener to an element, and returns a function to remove it.\nfunction addEvent(target, event, handler, options) {\n    // @ts-ignore\n    target.addEventListener(event, handler, options);\n    return ()=>{\n        // @ts-ignore\n        target.removeEventListener(event, handler, options);\n    };\n}\nfunction scrollIntoView(target) {\n    let root = document.scrollingElement || document.documentElement;\n    while(target && target !== root){\n        // Find the parent scrollable element and adjust the scroll position if the target is not already in view.\n        let scrollable = getScrollParent(target);\n        if (scrollable !== document.documentElement && scrollable !== document.body && scrollable !== target) {\n            let scrollableTop = scrollable.getBoundingClientRect().top;\n            let targetTop = target.getBoundingClientRect().top;\n            let targetBottom = target.getBoundingClientRect().bottom;\n            // Buffer is needed for some edge cases\n            const keyboardHeight = scrollable.getBoundingClientRect().bottom + KEYBOARD_BUFFER;\n            if (targetBottom > keyboardHeight) {\n                scrollable.scrollTop += targetTop - scrollableTop;\n            }\n        }\n        // @ts-ignore\n        target = scrollable.parentElement;\n    }\n}\nfunction isInput(target) {\n    return target instanceof HTMLInputElement && !nonTextInputTypes.has(target.type) || target instanceof HTMLTextAreaElement || target instanceof HTMLElement && target.isContentEditable;\n}\n// This code comes from https://github.com/radix-ui/primitives/tree/main/packages/react/compose-refs\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */ function setRef(ref, value) {\n    if (typeof ref === \"function\") {\n        ref(value);\n    } else if (ref !== null && ref !== undefined) {\n        ref.current = value;\n    }\n}\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */ function composeRefs(...refs) {\n    return (node)=>refs.forEach((ref)=>setRef(ref, node));\n}\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */ function useComposedRefs(...refs) {\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\nconst cache = new WeakMap();\nfunction set(el, styles, ignoreCache = false) {\n    if (!el || !(el instanceof HTMLElement)) return;\n    let originalStyles = {};\n    Object.entries(styles).forEach(([key, value])=>{\n        if (key.startsWith(\"--\")) {\n            el.style.setProperty(key, value);\n            return;\n        }\n        originalStyles[key] = el.style[key];\n        el.style[key] = value;\n    });\n    if (ignoreCache) return;\n    cache.set(el, originalStyles);\n}\nfunction reset(el, prop) {\n    if (!el || !(el instanceof HTMLElement)) return;\n    let originalStyles = cache.get(el);\n    if (!originalStyles) {\n        return;\n    }\n    {\n        el.style[prop] = originalStyles[prop];\n    }\n}\nconst isVertical = (direction)=>{\n    switch(direction){\n        case \"top\":\n        case \"bottom\":\n            return true;\n        case \"left\":\n        case \"right\":\n            return false;\n        default:\n            return direction;\n    }\n};\nfunction getTranslate(element, direction) {\n    if (!element) {\n        return null;\n    }\n    const style = window.getComputedStyle(element);\n    const transform = style.transform || style.webkitTransform || style.mozTransform;\n    let mat = transform.match(/^matrix3d\\((.+)\\)$/);\n    if (mat) {\n        // https://developer.mozilla.org/en-US/docs/Web/CSS/transform-function/matrix3d\n        return parseFloat(mat[1].split(\", \")[isVertical(direction) ? 13 : 12]);\n    }\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/transform-function/matrix\n    mat = transform.match(/^matrix\\((.+)\\)$/);\n    return mat ? parseFloat(mat[1].split(\", \")[isVertical(direction) ? 5 : 4]) : null;\n}\nfunction dampenValue(v) {\n    return 8 * (Math.log(v + 1) - 2);\n}\nfunction assignStyle(element, style) {\n    if (!element) return ()=>{};\n    const prevStyle = element.style.cssText;\n    Object.assign(element.style, style);\n    return ()=>{\n        element.style.cssText = prevStyle;\n    };\n}\n/**\n * Receives functions as arguments and returns a new function that calls all.\n */ function chain(...fns) {\n    return (...args)=>{\n        for (const fn of fns){\n            if (typeof fn === \"function\") {\n                // @ts-ignore\n                fn(...args);\n            }\n        }\n    };\n}\nconst TRANSITIONS = {\n    DURATION: 0.5,\n    EASE: [\n        0.32,\n        0.72,\n        0,\n        1\n    ]\n};\nconst VELOCITY_THRESHOLD = 0.4;\nconst CLOSE_THRESHOLD = 0.25;\nconst SCROLL_LOCK_TIMEOUT = 100;\nconst BORDER_RADIUS = 8;\nconst NESTED_DISPLACEMENT = 16;\nconst WINDOW_TOP_OFFSET = 26;\nconst DRAG_CLASS = \"vaul-dragging\";\n// This code comes from https://github.com/radix-ui/primitives/blob/main/packages/react/use-controllable-state/src/useControllableState.tsx\nfunction useCallbackRef(callback) {\n    const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        callbackRef.current = callback;\n    });\n    // https://github.com/facebook/react/issues/19240\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>(...args)=>callbackRef.current == null ? void 0 : callbackRef.current.call(callbackRef, ...args), []);\n}\nfunction useUncontrolledState({ defaultProp, onChange }) {\n    const uncontrolledState = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n    const [value] = uncontrolledState;\n    const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n    const handleChange = useCallbackRef(onChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (prevValueRef.current !== value) {\n            handleChange(value);\n            prevValueRef.current = value;\n        }\n    }, [\n        value,\n        prevValueRef,\n        handleChange\n    ]);\n    return uncontrolledState;\n}\nfunction useControllableState({ prop, defaultProp, onChange = ()=>{} }) {\n    const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({\n        defaultProp,\n        onChange\n    });\n    const isControlled = prop !== undefined;\n    const value = isControlled ? prop : uncontrolledProp;\n    const handleChange = useCallbackRef(onChange);\n    const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((nextValue)=>{\n        if (isControlled) {\n            const setter = nextValue;\n            const value = typeof nextValue === \"function\" ? setter(prop) : nextValue;\n            if (value !== prop) handleChange(value);\n        } else {\n            setUncontrolledProp(nextValue);\n        }\n    }, [\n        isControlled,\n        prop,\n        setUncontrolledProp,\n        handleChange\n    ]);\n    return [\n        value,\n        setValue\n    ];\n}\nfunction useSnapPoints({ activeSnapPointProp, setActiveSnapPointProp, snapPoints, drawerRef, overlayRef, fadeFromIndex, onSnapPointChange, direction = \"bottom\", container, snapToSequentialPoint }) {\n    const [activeSnapPoint, setActiveSnapPoint] = useControllableState({\n        prop: activeSnapPointProp,\n        defaultProp: snapPoints == null ? void 0 : snapPoints[0],\n        onChange: setActiveSnapPointProp\n    });\n    const [windowDimensions, setWindowDimensions] = react__WEBPACK_IMPORTED_MODULE_0__.useState( false ? 0 : undefined);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        function onResize() {\n            setWindowDimensions({\n                innerWidth: window.innerWidth,\n                innerHeight: window.innerHeight\n            });\n        }\n        window.addEventListener(\"resize\", onResize);\n        return ()=>window.removeEventListener(\"resize\", onResize);\n    }, []);\n    const isLastSnapPoint = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>activeSnapPoint === (snapPoints == null ? void 0 : snapPoints[snapPoints.length - 1]) || null, [\n        snapPoints,\n        activeSnapPoint\n    ]);\n    const activeSnapPointIndex = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>snapPoints == null ? void 0 : snapPoints.findIndex((snapPoint)=>snapPoint === activeSnapPoint), [\n        snapPoints,\n        activeSnapPoint\n    ]);\n    const shouldFade = snapPoints && snapPoints.length > 0 && (fadeFromIndex || fadeFromIndex === 0) && !Number.isNaN(fadeFromIndex) && snapPoints[fadeFromIndex] === activeSnapPoint || !snapPoints;\n    const snapPointsOffset = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const containerSize = container ? {\n            width: container.getBoundingClientRect().width,\n            height: container.getBoundingClientRect().height\n        } :  false ? 0 : {\n            width: 0,\n            height: 0\n        };\n        var _snapPoints_map;\n        return (_snapPoints_map = snapPoints == null ? void 0 : snapPoints.map((snapPoint)=>{\n            const isPx = typeof snapPoint === \"string\";\n            let snapPointAsNumber = 0;\n            if (isPx) {\n                snapPointAsNumber = parseInt(snapPoint, 10);\n            }\n            if (isVertical(direction)) {\n                const height = isPx ? snapPointAsNumber : windowDimensions ? snapPoint * containerSize.height : 0;\n                if (windowDimensions) {\n                    return direction === \"bottom\" ? containerSize.height - height : -containerSize.height + height;\n                }\n                return height;\n            }\n            const width = isPx ? snapPointAsNumber : windowDimensions ? snapPoint * containerSize.width : 0;\n            if (windowDimensions) {\n                return direction === \"right\" ? containerSize.width - width : -containerSize.width + width;\n            }\n            return width;\n        })) != null ? _snapPoints_map : [];\n    }, [\n        snapPoints,\n        windowDimensions,\n        container\n    ]);\n    const activeSnapPointOffset = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>activeSnapPointIndex !== null ? snapPointsOffset == null ? void 0 : snapPointsOffset[activeSnapPointIndex] : null, [\n        snapPointsOffset,\n        activeSnapPointIndex\n    ]);\n    const snapToPoint = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((dimension)=>{\n        var _snapPointsOffset_findIndex;\n        const newSnapPointIndex = (_snapPointsOffset_findIndex = snapPointsOffset == null ? void 0 : snapPointsOffset.findIndex((snapPointDim)=>snapPointDim === dimension)) != null ? _snapPointsOffset_findIndex : null;\n        onSnapPointChange(newSnapPointIndex);\n        set(drawerRef.current, {\n            transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`,\n            transform: isVertical(direction) ? `translate3d(0, ${dimension}px, 0)` : `translate3d(${dimension}px, 0, 0)`\n        });\n        if (snapPointsOffset && newSnapPointIndex !== snapPointsOffset.length - 1 && newSnapPointIndex !== fadeFromIndex && newSnapPointIndex < fadeFromIndex) {\n            set(overlayRef.current, {\n                transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`,\n                opacity: \"0\"\n            });\n        } else {\n            set(overlayRef.current, {\n                transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`,\n                opacity: \"1\"\n            });\n        }\n        setActiveSnapPoint(snapPoints == null ? void 0 : snapPoints[Math.max(newSnapPointIndex, 0)]);\n    }, [\n        drawerRef.current,\n        snapPoints,\n        snapPointsOffset,\n        fadeFromIndex,\n        overlayRef,\n        setActiveSnapPoint\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (activeSnapPoint || activeSnapPointProp) {\n            var _snapPoints_findIndex;\n            const newIndex = (_snapPoints_findIndex = snapPoints == null ? void 0 : snapPoints.findIndex((snapPoint)=>snapPoint === activeSnapPointProp || snapPoint === activeSnapPoint)) != null ? _snapPoints_findIndex : -1;\n            if (snapPointsOffset && newIndex !== -1 && typeof snapPointsOffset[newIndex] === \"number\") {\n                snapToPoint(snapPointsOffset[newIndex]);\n            }\n        }\n    }, [\n        activeSnapPoint,\n        activeSnapPointProp,\n        snapPoints,\n        snapPointsOffset,\n        snapToPoint\n    ]);\n    function onRelease({ draggedDistance, closeDrawer, velocity, dismissible }) {\n        if (fadeFromIndex === undefined) return;\n        const currentPosition = direction === \"bottom\" || direction === \"right\" ? (activeSnapPointOffset != null ? activeSnapPointOffset : 0) - draggedDistance : (activeSnapPointOffset != null ? activeSnapPointOffset : 0) + draggedDistance;\n        const isOverlaySnapPoint = activeSnapPointIndex === fadeFromIndex - 1;\n        const isFirst = activeSnapPointIndex === 0;\n        const hasDraggedUp = draggedDistance > 0;\n        if (isOverlaySnapPoint) {\n            set(overlayRef.current, {\n                transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`\n            });\n        }\n        if (!snapToSequentialPoint && velocity > 2 && !hasDraggedUp) {\n            if (dismissible) closeDrawer();\n            else snapToPoint(snapPointsOffset[0]); // snap to initial point\n            return;\n        }\n        if (!snapToSequentialPoint && velocity > 2 && hasDraggedUp && snapPointsOffset && snapPoints) {\n            snapToPoint(snapPointsOffset[snapPoints.length - 1]);\n            return;\n        }\n        // Find the closest snap point to the current position\n        const closestSnapPoint = snapPointsOffset == null ? void 0 : snapPointsOffset.reduce((prev, curr)=>{\n            if (typeof prev !== \"number\" || typeof curr !== \"number\") return prev;\n            return Math.abs(curr - currentPosition) < Math.abs(prev - currentPosition) ? curr : prev;\n        });\n        const dim = isVertical(direction) ? window.innerHeight : window.innerWidth;\n        if (velocity > VELOCITY_THRESHOLD && Math.abs(draggedDistance) < dim * 0.4) {\n            const dragDirection = hasDraggedUp ? 1 : -1; // 1 = up, -1 = down\n            // Don't do anything if we swipe upwards while being on the last snap point\n            if (dragDirection > 0 && isLastSnapPoint) {\n                snapToPoint(snapPointsOffset[snapPoints.length - 1]);\n                return;\n            }\n            if (isFirst && dragDirection < 0 && dismissible) {\n                closeDrawer();\n            }\n            if (activeSnapPointIndex === null) return;\n            snapToPoint(snapPointsOffset[activeSnapPointIndex + dragDirection]);\n            return;\n        }\n        snapToPoint(closestSnapPoint);\n    }\n    function onDrag({ draggedDistance }) {\n        if (activeSnapPointOffset === null) return;\n        const newValue = direction === \"bottom\" || direction === \"right\" ? activeSnapPointOffset - draggedDistance : activeSnapPointOffset + draggedDistance;\n        // Don't do anything if we exceed the last(biggest) snap point\n        if ((direction === \"bottom\" || direction === \"right\") && newValue < snapPointsOffset[snapPointsOffset.length - 1]) {\n            return;\n        }\n        if ((direction === \"top\" || direction === \"left\") && newValue > snapPointsOffset[snapPointsOffset.length - 1]) {\n            return;\n        }\n        set(drawerRef.current, {\n            transform: isVertical(direction) ? `translate3d(0, ${newValue}px, 0)` : `translate3d(${newValue}px, 0, 0)`\n        });\n    }\n    function getPercentageDragged(absDraggedDistance, isDraggingDown) {\n        if (!snapPoints || typeof activeSnapPointIndex !== \"number\" || !snapPointsOffset || fadeFromIndex === undefined) return null;\n        // If this is true we are dragging to a snap point that is supposed to have an overlay\n        const isOverlaySnapPoint = activeSnapPointIndex === fadeFromIndex - 1;\n        const isOverlaySnapPointOrHigher = activeSnapPointIndex >= fadeFromIndex;\n        if (isOverlaySnapPointOrHigher && isDraggingDown) {\n            return 0;\n        }\n        // Don't animate, but still use this one if we are dragging away from the overlaySnapPoint\n        if (isOverlaySnapPoint && !isDraggingDown) return 1;\n        if (!shouldFade && !isOverlaySnapPoint) return null;\n        // Either fadeFrom index or the one before\n        const targetSnapPointIndex = isOverlaySnapPoint ? activeSnapPointIndex + 1 : activeSnapPointIndex - 1;\n        // Get the distance from overlaySnapPoint to the one before or vice-versa to calculate the opacity percentage accordingly\n        const snapPointDistance = isOverlaySnapPoint ? snapPointsOffset[targetSnapPointIndex] - snapPointsOffset[targetSnapPointIndex - 1] : snapPointsOffset[targetSnapPointIndex + 1] - snapPointsOffset[targetSnapPointIndex];\n        const percentageDragged = absDraggedDistance / Math.abs(snapPointDistance);\n        if (isOverlaySnapPoint) {\n            return 1 - percentageDragged;\n        } else {\n            return percentageDragged;\n        }\n    }\n    return {\n        isLastSnapPoint,\n        activeSnapPoint,\n        shouldFade,\n        getPercentageDragged,\n        setActiveSnapPoint,\n        activeSnapPointIndex,\n        onRelease,\n        onDrag,\n        snapPointsOffset\n    };\n}\nconst noop = ()=>()=>{};\nfunction useScaleBackground() {\n    const { direction, isOpen, shouldScaleBackground, setBackgroundColorOnScale, noBodyStyles } = useDrawerContext();\n    const timeoutIdRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const initialBackgroundColor = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>document.body.style.backgroundColor, []);\n    function getScale() {\n        return (window.innerWidth - WINDOW_TOP_OFFSET) / window.innerWidth;\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (isOpen && shouldScaleBackground) {\n            if (timeoutIdRef.current) clearTimeout(timeoutIdRef.current);\n            const wrapper = document.querySelector(\"[data-vaul-drawer-wrapper]\") || document.querySelector(\"[vaul-drawer-wrapper]\");\n            if (!wrapper) return;\n            chain(setBackgroundColorOnScale && !noBodyStyles ? assignStyle(document.body, {\n                background: \"black\"\n            }) : noop, assignStyle(wrapper, {\n                transformOrigin: isVertical(direction) ? \"top\" : \"left\",\n                transitionProperty: \"transform, border-radius\",\n                transitionDuration: `${TRANSITIONS.DURATION}s`,\n                transitionTimingFunction: `cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`\n            }));\n            const wrapperStylesCleanup = assignStyle(wrapper, {\n                borderRadius: `${BORDER_RADIUS}px`,\n                overflow: \"hidden\",\n                ...isVertical(direction) ? {\n                    transform: `scale(${getScale()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`\n                } : {\n                    transform: `scale(${getScale()}) translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)`\n                }\n            });\n            return ()=>{\n                wrapperStylesCleanup();\n                timeoutIdRef.current = window.setTimeout(()=>{\n                    if (initialBackgroundColor) {\n                        document.body.style.background = initialBackgroundColor;\n                    } else {\n                        document.body.style.removeProperty(\"background\");\n                    }\n                }, TRANSITIONS.DURATION * 1000);\n            };\n        }\n    }, [\n        isOpen,\n        shouldScaleBackground,\n        initialBackgroundColor\n    ]);\n}\nlet previousBodyPosition = null;\n/**\n * This hook is necessary to prevent buggy behavior on iOS devices (need to test on Android).\n * I won't get into too much detail about what bugs it solves, but so far I've found that setting the body to `position: fixed` is the most reliable way to prevent those bugs.\n * Issues that this hook solves:\n * https://github.com/emilkowalski/vaul/issues/435\n * https://github.com/emilkowalski/vaul/issues/433\n * And more that I discovered, but were just not reported.\n */ function usePositionFixed({ isOpen, modal, nested, hasBeenOpened, preventScrollRestoration, noBodyStyles }) {\n    const [activeUrl, setActiveUrl] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=> false ? 0 : \"\");\n    const scrollPos = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const setPositionFixed = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        // All browsers on iOS will return true here.\n        if (!isSafari()) return;\n        // If previousBodyPosition is already set, don't set it again.\n        if (previousBodyPosition === null && isOpen && !noBodyStyles) {\n            previousBodyPosition = {\n                position: document.body.style.position,\n                top: document.body.style.top,\n                left: document.body.style.left,\n                height: document.body.style.height,\n                right: \"unset\"\n            };\n            // Update the dom inside an animation frame\n            const { scrollX, innerHeight } = window;\n            document.body.style.setProperty(\"position\", \"fixed\", \"important\");\n            Object.assign(document.body.style, {\n                top: `${-scrollPos.current}px`,\n                left: `${-scrollX}px`,\n                right: \"0px\",\n                height: \"auto\"\n            });\n            window.setTimeout(()=>window.requestAnimationFrame(()=>{\n                    // Attempt to check if the bottom bar appeared due to the position change\n                    const bottomBarHeight = innerHeight - window.innerHeight;\n                    if (bottomBarHeight && scrollPos.current >= innerHeight) {\n                        // Move the content further up so that the bottom bar doesn't hide it\n                        document.body.style.top = `${-(scrollPos.current + bottomBarHeight)}px`;\n                    }\n                }), 300);\n        }\n    }, [\n        isOpen\n    ]);\n    const restorePositionSetting = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        // All browsers on iOS will return true here.\n        if (!isSafari()) return;\n        if (previousBodyPosition !== null && !noBodyStyles) {\n            // Convert the position from \"px\" to Int\n            const y = -parseInt(document.body.style.top, 10);\n            const x = -parseInt(document.body.style.left, 10);\n            // Restore styles\n            Object.assign(document.body.style, previousBodyPosition);\n            window.requestAnimationFrame(()=>{\n                if (preventScrollRestoration && activeUrl !== window.location.href) {\n                    setActiveUrl(window.location.href);\n                    return;\n                }\n                window.scrollTo(x, y);\n            });\n            previousBodyPosition = null;\n        }\n    }, [\n        activeUrl\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        function onScroll() {\n            scrollPos.current = window.scrollY;\n        }\n        onScroll();\n        window.addEventListener(\"scroll\", onScroll);\n        return ()=>{\n            window.removeEventListener(\"scroll\", onScroll);\n        };\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (nested || !hasBeenOpened) return;\n        // This is needed to force Safari toolbar to show **before** the drawer starts animating to prevent a gnarly shift from happening\n        if (isOpen) {\n            // avoid for standalone mode (PWA)\n            const isStandalone = window.matchMedia(\"(display-mode: standalone)\").matches;\n            !isStandalone && setPositionFixed();\n            if (!modal) {\n                window.setTimeout(()=>{\n                    restorePositionSetting();\n                }, 500);\n            }\n        } else {\n            restorePositionSetting();\n        }\n    }, [\n        isOpen,\n        hasBeenOpened,\n        activeUrl,\n        modal,\n        nested,\n        setPositionFixed,\n        restorePositionSetting\n    ]);\n    return {\n        restorePositionSetting\n    };\n}\nfunction Root({ open: openProp, onOpenChange, children, onDrag: onDragProp, onRelease: onReleaseProp, snapPoints, shouldScaleBackground = false, setBackgroundColorOnScale = true, closeThreshold = CLOSE_THRESHOLD, scrollLockTimeout = SCROLL_LOCK_TIMEOUT, dismissible = true, handleOnly = false, fadeFromIndex = snapPoints && snapPoints.length - 1, activeSnapPoint: activeSnapPointProp, setActiveSnapPoint: setActiveSnapPointProp, fixed, modal = true, onClose, nested, noBodyStyles, direction = \"bottom\", defaultOpen = false, disablePreventScroll = true, snapToSequentialPoint = false, preventScrollRestoration = false, repositionInputs = true, onAnimationEnd, container, autoFocus = false }) {\n    var _drawerRef_current, _drawerRef_current1;\n    const [isOpen = false, setIsOpen] = useControllableState({\n        defaultProp: defaultOpen,\n        prop: openProp,\n        onChange: (o)=>{\n            onOpenChange == null ? void 0 : onOpenChange(o);\n            if (!o && !nested) {\n                restorePositionSetting();\n            }\n            setTimeout(()=>{\n                onAnimationEnd == null ? void 0 : onAnimationEnd(o);\n            }, TRANSITIONS.DURATION * 1000);\n            if (o && !modal) {\n                if (false) {}\n            }\n            if (!o) {\n                // This will be removed when the exit animation ends (`500ms`)\n                document.body.style.pointerEvents = \"auto\";\n            }\n        }\n    });\n    const [hasBeenOpened, setHasBeenOpened] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isDragging, setIsDragging] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [justReleased, setJustReleased] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const overlayRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const openTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const dragStartTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const dragEndTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const lastTimeDragPrevented = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isAllowedToDrag = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const nestedOpenChangeTimer = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const pointerStart = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const keyboardIsOpen = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const previousDiffFromInitial = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const drawerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const drawerHeightRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(((_drawerRef_current = drawerRef.current) == null ? void 0 : _drawerRef_current.getBoundingClientRect().height) || 0);\n    const drawerWidthRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(((_drawerRef_current1 = drawerRef.current) == null ? void 0 : _drawerRef_current1.getBoundingClientRect().width) || 0);\n    const initialDrawerHeight = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const onSnapPointChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((activeSnapPointIndex)=>{\n        // Change openTime ref when we reach the last snap point to prevent dragging for 500ms incase it's scrollable.\n        if (snapPoints && activeSnapPointIndex === snapPointsOffset.length - 1) openTime.current = new Date();\n    }, []);\n    const { activeSnapPoint, activeSnapPointIndex, setActiveSnapPoint, onRelease: onReleaseSnapPoints, snapPointsOffset, onDrag: onDragSnapPoints, shouldFade, getPercentageDragged: getSnapPointsPercentageDragged } = useSnapPoints({\n        snapPoints,\n        activeSnapPointProp,\n        setActiveSnapPointProp,\n        drawerRef,\n        fadeFromIndex,\n        overlayRef,\n        onSnapPointChange,\n        direction,\n        container,\n        snapToSequentialPoint\n    });\n    usePreventScroll({\n        isDisabled: !isOpen || isDragging || !modal || justReleased || !hasBeenOpened || !repositionInputs || !disablePreventScroll\n    });\n    const { restorePositionSetting } = usePositionFixed({\n        isOpen,\n        modal,\n        nested,\n        hasBeenOpened,\n        preventScrollRestoration,\n        noBodyStyles\n    });\n    function getScale() {\n        return (window.innerWidth - WINDOW_TOP_OFFSET) / window.innerWidth;\n    }\n    function onPress(event) {\n        var _drawerRef_current, _drawerRef_current1;\n        if (!dismissible && !snapPoints) return;\n        if (drawerRef.current && !drawerRef.current.contains(event.target)) return;\n        drawerHeightRef.current = ((_drawerRef_current = drawerRef.current) == null ? void 0 : _drawerRef_current.getBoundingClientRect().height) || 0;\n        drawerWidthRef.current = ((_drawerRef_current1 = drawerRef.current) == null ? void 0 : _drawerRef_current1.getBoundingClientRect().width) || 0;\n        setIsDragging(true);\n        dragStartTime.current = new Date();\n        // iOS doesn't trigger mouseUp after scrolling so we need to listen to touched in order to disallow dragging\n        if (isIOS()) {\n            window.addEventListener(\"touchend\", ()=>isAllowedToDrag.current = false, {\n                once: true\n            });\n        }\n        // Ensure we maintain correct pointer capture even when going outside of the drawer\n        event.target.setPointerCapture(event.pointerId);\n        pointerStart.current = isVertical(direction) ? event.pageY : event.pageX;\n    }\n    function shouldDrag(el, isDraggingInDirection) {\n        var _window_getSelection, _lastTimeDragPrevented_current;\n        let element = el;\n        const highlightedText = (_window_getSelection = window.getSelection()) == null ? void 0 : _window_getSelection.toString();\n        const swipeAmount = drawerRef.current ? getTranslate(drawerRef.current, direction) : null;\n        const date = new Date();\n        if (element.hasAttribute(\"data-vaul-no-drag\") || element.closest(\"[data-vaul-no-drag]\")) {\n            return false;\n        }\n        if (direction === \"right\" || direction === \"left\") {\n            return true;\n        }\n        // Allow scrolling when animating\n        if (openTime.current && date.getTime() - openTime.current.getTime() < 500) {\n            return false;\n        }\n        if (swipeAmount !== null) {\n            if (direction === \"bottom\" ? swipeAmount > 0 : swipeAmount < 0) {\n                return true;\n            }\n        }\n        // Don't drag if there's highlighted text\n        if (highlightedText && highlightedText.length > 0) {\n            return false;\n        }\n        // Disallow dragging if drawer was scrolled within `scrollLockTimeout`\n        if (date.getTime() - ((_lastTimeDragPrevented_current = lastTimeDragPrevented.current) == null ? void 0 : _lastTimeDragPrevented_current.getTime()) < scrollLockTimeout && swipeAmount === 0) {\n            lastTimeDragPrevented.current = date;\n            return false;\n        }\n        if (isDraggingInDirection) {\n            lastTimeDragPrevented.current = date;\n            // We are dragging down so we should allow scrolling\n            return false;\n        }\n        // Keep climbing up the DOM tree as long as there's a parent\n        while(element){\n            // Check if the element is scrollable\n            if (element.scrollHeight > element.clientHeight) {\n                if (element.scrollTop !== 0) {\n                    lastTimeDragPrevented.current = new Date();\n                    // The element is scrollable and not scrolled to the top, so don't drag\n                    return false;\n                }\n                if (element.getAttribute(\"role\") === \"dialog\") {\n                    return true;\n                }\n            }\n            // Move up to the parent element\n            element = element.parentNode;\n        }\n        // No scrollable parents not scrolled to the top found, so drag\n        return true;\n    }\n    function onDrag(event) {\n        if (!drawerRef.current) {\n            return;\n        }\n        // We need to know how much of the drawer has been dragged in percentages so that we can transform background accordingly\n        if (isDragging) {\n            const directionMultiplier = direction === \"bottom\" || direction === \"right\" ? 1 : -1;\n            const draggedDistance = (pointerStart.current - (isVertical(direction) ? event.pageY : event.pageX)) * directionMultiplier;\n            const isDraggingInDirection = draggedDistance > 0;\n            // Pre condition for disallowing dragging in the close direction.\n            const noCloseSnapPointsPreCondition = snapPoints && !dismissible && !isDraggingInDirection;\n            // Disallow dragging down to close when first snap point is the active one and dismissible prop is set to false.\n            if (noCloseSnapPointsPreCondition && activeSnapPointIndex === 0) return;\n            // We need to capture last time when drag with scroll was triggered and have a timeout between\n            const absDraggedDistance = Math.abs(draggedDistance);\n            const wrapper = document.querySelector(\"[data-vaul-drawer-wrapper]\");\n            const drawerDimension = direction === \"bottom\" || direction === \"top\" ? drawerHeightRef.current : drawerWidthRef.current;\n            // Calculate the percentage dragged, where 1 is the closed position\n            let percentageDragged = absDraggedDistance / drawerDimension;\n            const snapPointPercentageDragged = getSnapPointsPercentageDragged(absDraggedDistance, isDraggingInDirection);\n            if (snapPointPercentageDragged !== null) {\n                percentageDragged = snapPointPercentageDragged;\n            }\n            // Disallow close dragging beyond the smallest snap point.\n            if (noCloseSnapPointsPreCondition && percentageDragged >= 1) {\n                return;\n            }\n            if (!isAllowedToDrag.current && !shouldDrag(event.target, isDraggingInDirection)) return;\n            drawerRef.current.classList.add(DRAG_CLASS);\n            // If shouldDrag gave true once after pressing down on the drawer, we set isAllowedToDrag to true and it will remain true until we let go, there's no reason to disable dragging mid way, ever, and that's the solution to it\n            isAllowedToDrag.current = true;\n            set(drawerRef.current, {\n                transition: \"none\"\n            });\n            set(overlayRef.current, {\n                transition: \"none\"\n            });\n            if (snapPoints) {\n                onDragSnapPoints({\n                    draggedDistance\n                });\n            }\n            // Run this only if snapPoints are not defined or if we are at the last snap point (highest one)\n            if (isDraggingInDirection && !snapPoints) {\n                const dampenedDraggedDistance = dampenValue(draggedDistance);\n                const translateValue = Math.min(dampenedDraggedDistance * -1, 0) * directionMultiplier;\n                set(drawerRef.current, {\n                    transform: isVertical(direction) ? `translate3d(0, ${translateValue}px, 0)` : `translate3d(${translateValue}px, 0, 0)`\n                });\n                return;\n            }\n            const opacityValue = 1 - percentageDragged;\n            if (shouldFade || fadeFromIndex && activeSnapPointIndex === fadeFromIndex - 1) {\n                onDragProp == null ? void 0 : onDragProp(event, percentageDragged);\n                set(overlayRef.current, {\n                    opacity: `${opacityValue}`,\n                    transition: \"none\"\n                }, true);\n            }\n            if (wrapper && overlayRef.current && shouldScaleBackground) {\n                // Calculate percentageDragged as a fraction (0 to 1)\n                const scaleValue = Math.min(getScale() + percentageDragged * (1 - getScale()), 1);\n                const borderRadiusValue = 8 - percentageDragged * 8;\n                const translateValue = Math.max(0, 14 - percentageDragged * 14);\n                set(wrapper, {\n                    borderRadius: `${borderRadiusValue}px`,\n                    transform: isVertical(direction) ? `scale(${scaleValue}) translate3d(0, ${translateValue}px, 0)` : `scale(${scaleValue}) translate3d(${translateValue}px, 0, 0)`,\n                    transition: \"none\"\n                }, true);\n            }\n            if (!snapPoints) {\n                const translateValue = absDraggedDistance * directionMultiplier;\n                set(drawerRef.current, {\n                    transform: isVertical(direction) ? `translate3d(0, ${translateValue}px, 0)` : `translate3d(${translateValue}px, 0, 0)`\n                });\n            }\n        }\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        var _window_visualViewport;\n        function onVisualViewportChange() {\n            if (!drawerRef.current || !repositionInputs) return;\n            const focusedElement = document.activeElement;\n            if (isInput(focusedElement) || keyboardIsOpen.current) {\n                var _window_visualViewport;\n                const visualViewportHeight = ((_window_visualViewport = window.visualViewport) == null ? void 0 : _window_visualViewport.height) || 0;\n                const totalHeight = window.innerHeight;\n                // This is the height of the keyboard\n                let diffFromInitial = totalHeight - visualViewportHeight;\n                const drawerHeight = drawerRef.current.getBoundingClientRect().height || 0;\n                // Adjust drawer height only if it's tall enough\n                const isTallEnough = drawerHeight > totalHeight * 0.8;\n                if (!initialDrawerHeight.current) {\n                    initialDrawerHeight.current = drawerHeight;\n                }\n                const offsetFromTop = drawerRef.current.getBoundingClientRect().top;\n                // visualViewport height may change due to somq e subtle changes to the keyboard. Checking if the height changed by 60 or more will make sure that they keyboard really changed its open state.\n                if (Math.abs(previousDiffFromInitial.current - diffFromInitial) > 60) {\n                    keyboardIsOpen.current = !keyboardIsOpen.current;\n                }\n                if (snapPoints && snapPoints.length > 0 && snapPointsOffset && activeSnapPointIndex) {\n                    const activeSnapPointHeight = snapPointsOffset[activeSnapPointIndex] || 0;\n                    diffFromInitial += activeSnapPointHeight;\n                }\n                previousDiffFromInitial.current = diffFromInitial;\n                // We don't have to change the height if the input is in view, when we are here we are in the opened keyboard state so we can correctly check if the input is in view\n                if (drawerHeight > visualViewportHeight || keyboardIsOpen.current) {\n                    const height = drawerRef.current.getBoundingClientRect().height;\n                    let newDrawerHeight = height;\n                    if (height > visualViewportHeight) {\n                        newDrawerHeight = visualViewportHeight - (isTallEnough ? offsetFromTop : WINDOW_TOP_OFFSET);\n                    }\n                    // When fixed, don't move the drawer upwards if there's space, but rather only change it's height so it's fully scrollable when the keyboard is open\n                    if (fixed) {\n                        drawerRef.current.style.height = `${height - Math.max(diffFromInitial, 0)}px`;\n                    } else {\n                        drawerRef.current.style.height = `${Math.max(newDrawerHeight, visualViewportHeight - offsetFromTop)}px`;\n                    }\n                } else {\n                    drawerRef.current.style.height = `${initialDrawerHeight.current}px`;\n                }\n                if (snapPoints && snapPoints.length > 0 && !keyboardIsOpen.current) {\n                    drawerRef.current.style.bottom = `0px`;\n                } else {\n                    // Negative bottom value would never make sense\n                    drawerRef.current.style.bottom = `${Math.max(diffFromInitial, 0)}px`;\n                }\n            }\n        }\n        (_window_visualViewport = window.visualViewport) == null ? void 0 : _window_visualViewport.addEventListener(\"resize\", onVisualViewportChange);\n        return ()=>{\n            var _window_visualViewport;\n            return (_window_visualViewport = window.visualViewport) == null ? void 0 : _window_visualViewport.removeEventListener(\"resize\", onVisualViewportChange);\n        };\n    }, [\n        activeSnapPointIndex,\n        snapPoints,\n        snapPointsOffset\n    ]);\n    function closeDrawer(fromWithin) {\n        cancelDrag();\n        onClose == null ? void 0 : onClose();\n        if (!fromWithin) {\n            setIsOpen(false);\n        }\n        setTimeout(()=>{\n            if (snapPoints) {\n                setActiveSnapPoint(snapPoints[0]);\n            }\n        }, TRANSITIONS.DURATION * 1000); // seconds to ms\n    }\n    function resetDrawer() {\n        if (!drawerRef.current) return;\n        const wrapper = document.querySelector(\"[data-vaul-drawer-wrapper]\");\n        const currentSwipeAmount = getTranslate(drawerRef.current, direction);\n        set(drawerRef.current, {\n            transform: \"translate3d(0, 0, 0)\",\n            transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`\n        });\n        set(overlayRef.current, {\n            transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`,\n            opacity: \"1\"\n        });\n        // Don't reset background if swiped upwards\n        if (shouldScaleBackground && currentSwipeAmount && currentSwipeAmount > 0 && isOpen) {\n            set(wrapper, {\n                borderRadius: `${BORDER_RADIUS}px`,\n                overflow: \"hidden\",\n                ...isVertical(direction) ? {\n                    transform: `scale(${getScale()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`,\n                    transformOrigin: \"top\"\n                } : {\n                    transform: `scale(${getScale()}) translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)`,\n                    transformOrigin: \"left\"\n                },\n                transitionProperty: \"transform, border-radius\",\n                transitionDuration: `${TRANSITIONS.DURATION}s`,\n                transitionTimingFunction: `cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`\n            }, true);\n        }\n    }\n    function cancelDrag() {\n        if (!isDragging || !drawerRef.current) return;\n        drawerRef.current.classList.remove(DRAG_CLASS);\n        isAllowedToDrag.current = false;\n        setIsDragging(false);\n        dragEndTime.current = new Date();\n    }\n    function onRelease(event) {\n        if (!isDragging || !drawerRef.current) return;\n        drawerRef.current.classList.remove(DRAG_CLASS);\n        isAllowedToDrag.current = false;\n        setIsDragging(false);\n        dragEndTime.current = new Date();\n        const swipeAmount = getTranslate(drawerRef.current, direction);\n        if (!shouldDrag(event.target, false) || !swipeAmount || Number.isNaN(swipeAmount)) return;\n        if (dragStartTime.current === null) return;\n        const timeTaken = dragEndTime.current.getTime() - dragStartTime.current.getTime();\n        const distMoved = pointerStart.current - (isVertical(direction) ? event.pageY : event.pageX);\n        const velocity = Math.abs(distMoved) / timeTaken;\n        if (velocity > 0.05) {\n            // `justReleased` is needed to prevent the drawer from focusing on an input when the drag ends, as it's not the intent most of the time.\n            setJustReleased(true);\n            setTimeout(()=>{\n                setJustReleased(false);\n            }, 200);\n        }\n        if (snapPoints) {\n            const directionMultiplier = direction === \"bottom\" || direction === \"right\" ? 1 : -1;\n            onReleaseSnapPoints({\n                draggedDistance: distMoved * directionMultiplier,\n                closeDrawer,\n                velocity,\n                dismissible\n            });\n            onReleaseProp == null ? void 0 : onReleaseProp(event, true);\n            return;\n        }\n        // Moved upwards, don't do anything\n        if (direction === \"bottom\" || direction === \"right\" ? distMoved > 0 : distMoved < 0) {\n            resetDrawer();\n            onReleaseProp == null ? void 0 : onReleaseProp(event, true);\n            return;\n        }\n        if (velocity > VELOCITY_THRESHOLD) {\n            closeDrawer();\n            onReleaseProp == null ? void 0 : onReleaseProp(event, false);\n            return;\n        }\n        var _drawerRef_current_getBoundingClientRect_height;\n        const visibleDrawerHeight = Math.min((_drawerRef_current_getBoundingClientRect_height = drawerRef.current.getBoundingClientRect().height) != null ? _drawerRef_current_getBoundingClientRect_height : 0, window.innerHeight);\n        var _drawerRef_current_getBoundingClientRect_width;\n        const visibleDrawerWidth = Math.min((_drawerRef_current_getBoundingClientRect_width = drawerRef.current.getBoundingClientRect().width) != null ? _drawerRef_current_getBoundingClientRect_width : 0, window.innerWidth);\n        const isHorizontalSwipe = direction === \"left\" || direction === \"right\";\n        if (Math.abs(swipeAmount) >= (isHorizontalSwipe ? visibleDrawerWidth : visibleDrawerHeight) * closeThreshold) {\n            closeDrawer();\n            onReleaseProp == null ? void 0 : onReleaseProp(event, false);\n            return;\n        }\n        onReleaseProp == null ? void 0 : onReleaseProp(event, true);\n        resetDrawer();\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        // Trigger enter animation without using CSS animation\n        if (isOpen) {\n            set(document.documentElement, {\n                scrollBehavior: \"auto\"\n            });\n            openTime.current = new Date();\n        }\n        return ()=>{\n            reset(document.documentElement, \"scrollBehavior\");\n        };\n    }, [\n        isOpen\n    ]);\n    function onNestedOpenChange(o) {\n        const scale = o ? (window.innerWidth - NESTED_DISPLACEMENT) / window.innerWidth : 1;\n        const y = o ? -NESTED_DISPLACEMENT : 0;\n        if (nestedOpenChangeTimer.current) {\n            window.clearTimeout(nestedOpenChangeTimer.current);\n        }\n        set(drawerRef.current, {\n            transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`,\n            transform: `scale(${scale}) translate3d(0, ${y}px, 0)`\n        });\n        if (!o && drawerRef.current) {\n            nestedOpenChangeTimer.current = setTimeout(()=>{\n                const translateValue = getTranslate(drawerRef.current, direction);\n                set(drawerRef.current, {\n                    transition: \"none\",\n                    transform: isVertical(direction) ? `translate3d(0, ${translateValue}px, 0)` : `translate3d(${translateValue}px, 0, 0)`\n                });\n            }, 500);\n        }\n    }\n    function onNestedDrag(_event, percentageDragged) {\n        if (percentageDragged < 0) return;\n        const initialScale = (window.innerWidth - NESTED_DISPLACEMENT) / window.innerWidth;\n        const newScale = initialScale + percentageDragged * (1 - initialScale);\n        const newTranslate = -NESTED_DISPLACEMENT + percentageDragged * NESTED_DISPLACEMENT;\n        set(drawerRef.current, {\n            transform: isVertical(direction) ? `scale(${newScale}) translate3d(0, ${newTranslate}px, 0)` : `scale(${newScale}) translate3d(${newTranslate}px, 0, 0)`,\n            transition: \"none\"\n        });\n    }\n    function onNestedRelease(_event, o) {\n        const dim = isVertical(direction) ? window.innerHeight : window.innerWidth;\n        const scale = o ? (dim - NESTED_DISPLACEMENT) / dim : 1;\n        const translate = o ? -NESTED_DISPLACEMENT : 0;\n        if (o) {\n            set(drawerRef.current, {\n                transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`,\n                transform: isVertical(direction) ? `scale(${scale}) translate3d(0, ${translate}px, 0)` : `scale(${scale}) translate3d(${translate}px, 0, 0)`\n            });\n        }\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Root, {\n        defaultOpen: defaultOpen,\n        onOpenChange: (open)=>{\n            if (!dismissible && !open) return;\n            if (open) {\n                setHasBeenOpened(true);\n            } else {\n                closeDrawer(true);\n            }\n            setIsOpen(open);\n        },\n        open: isOpen\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(DrawerContext.Provider, {\n        value: {\n            activeSnapPoint,\n            snapPoints,\n            setActiveSnapPoint,\n            drawerRef,\n            overlayRef,\n            onOpenChange,\n            onPress,\n            onRelease,\n            onDrag,\n            dismissible,\n            handleOnly,\n            isOpen,\n            isDragging,\n            shouldFade,\n            closeDrawer,\n            onNestedDrag,\n            onNestedOpenChange,\n            onNestedRelease,\n            keyboardIsOpen,\n            modal,\n            snapPointsOffset,\n            direction,\n            shouldScaleBackground,\n            setBackgroundColorOnScale,\n            noBodyStyles,\n            container,\n            autoFocus\n        }\n    }, children));\n}\nconst Overlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function({ ...rest }, ref) {\n    const { overlayRef, snapPoints, onRelease, shouldFade, isOpen, modal } = useDrawerContext();\n    const composedRef = useComposedRefs(ref, overlayRef);\n    const hasSnapPoints = snapPoints && snapPoints.length > 0;\n    // Overlay is the component that is locking scroll, removing it will unlock the scroll without having to dig into Radix's Dialog library\n    if (!modal) {\n        // Need to do this manually unfortunately\n        if (false) {}\n        return null;\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Overlay, {\n        onMouseUp: onRelease,\n        ref: composedRef,\n        \"data-vaul-overlay\": \"\",\n        \"data-vaul-snap-points\": isOpen && hasSnapPoints ? \"true\" : \"false\",\n        \"data-vaul-snap-points-overlay\": isOpen && shouldFade ? \"true\" : \"false\",\n        ...rest\n    });\n});\nOverlay.displayName = \"Drawer.Overlay\";\nconst Content = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function({ onPointerDownOutside, style, onOpenAutoFocus, ...rest }, ref) {\n    const { drawerRef, onPress, onRelease, onDrag, keyboardIsOpen, snapPointsOffset, modal, isOpen, direction, snapPoints, container, handleOnly, autoFocus } = useDrawerContext();\n    // Needed to use transition instead of animations\n    const [delayedSnapPoints, setDelayedSnapPoints] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRef = useComposedRefs(ref, drawerRef);\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const lastKnownPointerEventRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const wasBeyondThePointRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasSnapPoints = snapPoints && snapPoints.length > 0;\n    useScaleBackground();\n    const isDeltaInDirection = (delta, direction, threshold = 0)=>{\n        if (wasBeyondThePointRef.current) return true;\n        const deltaY = Math.abs(delta.y);\n        const deltaX = Math.abs(delta.x);\n        const isDeltaX = deltaX > deltaY;\n        const dFactor = [\n            \"bottom\",\n            \"right\"\n        ].includes(direction) ? 1 : -1;\n        if (direction === \"left\" || direction === \"right\") {\n            const isReverseDirection = delta.x * dFactor < 0;\n            if (!isReverseDirection && deltaX >= 0 && deltaX <= threshold) {\n                return isDeltaX;\n            }\n        } else {\n            const isReverseDirection = delta.y * dFactor < 0;\n            if (!isReverseDirection && deltaY >= 0 && deltaY <= threshold) {\n                return !isDeltaX;\n            }\n        }\n        wasBeyondThePointRef.current = true;\n        return true;\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (hasSnapPoints) {\n            window.requestAnimationFrame(()=>{\n                setDelayedSnapPoints(true);\n            });\n        }\n    }, []);\n    function handleOnPointerUp(event) {\n        pointerStartRef.current = null;\n        wasBeyondThePointRef.current = false;\n        onRelease(event);\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Content, {\n        \"data-vaul-drawer-direction\": direction,\n        \"data-vaul-drawer\": \"\",\n        \"data-vaul-delayed-snap-points\": delayedSnapPoints ? \"true\" : \"false\",\n        \"data-vaul-snap-points\": isOpen && hasSnapPoints ? \"true\" : \"false\",\n        \"data-vaul-custom-container\": container ? \"true\" : \"false\",\n        ...rest,\n        ref: composedRef,\n        style: snapPointsOffset && snapPointsOffset.length > 0 ? {\n            \"--snap-point-height\": `${snapPointsOffset[0]}px`,\n            ...style\n        } : style,\n        onPointerDown: (event)=>{\n            if (handleOnly) return;\n            rest.onPointerDown == null ? void 0 : rest.onPointerDown.call(rest, event);\n            pointerStartRef.current = {\n                x: event.pageX,\n                y: event.pageY\n            };\n            onPress(event);\n        },\n        onOpenAutoFocus: (e)=>{\n            onOpenAutoFocus == null ? void 0 : onOpenAutoFocus(e);\n            if (!autoFocus) {\n                e.preventDefault();\n            }\n        },\n        onPointerDownOutside: (e)=>{\n            onPointerDownOutside == null ? void 0 : onPointerDownOutside(e);\n            if (!modal || e.defaultPrevented) {\n                e.preventDefault();\n                return;\n            }\n            if (keyboardIsOpen.current) {\n                keyboardIsOpen.current = false;\n            }\n        },\n        onFocusOutside: (e)=>{\n            if (!modal) {\n                e.preventDefault();\n                return;\n            }\n        },\n        onPointerMove: (event)=>{\n            lastKnownPointerEventRef.current = event;\n            if (handleOnly) return;\n            rest.onPointerMove == null ? void 0 : rest.onPointerMove.call(rest, event);\n            if (!pointerStartRef.current) return;\n            const yPosition = event.pageY - pointerStartRef.current.y;\n            const xPosition = event.pageX - pointerStartRef.current.x;\n            const swipeStartThreshold = event.pointerType === \"touch\" ? 10 : 2;\n            const delta = {\n                x: xPosition,\n                y: yPosition\n            };\n            const isAllowedToSwipe = isDeltaInDirection(delta, direction, swipeStartThreshold);\n            if (isAllowedToSwipe) onDrag(event);\n            else if (Math.abs(xPosition) > swipeStartThreshold || Math.abs(yPosition) > swipeStartThreshold) {\n                pointerStartRef.current = null;\n            }\n        },\n        onPointerUp: (event)=>{\n            rest.onPointerUp == null ? void 0 : rest.onPointerUp.call(rest, event);\n            pointerStartRef.current = null;\n            wasBeyondThePointRef.current = false;\n            onRelease(event);\n        },\n        onPointerOut: (event)=>{\n            rest.onPointerOut == null ? void 0 : rest.onPointerOut.call(rest, event);\n            handleOnPointerUp(lastKnownPointerEventRef.current);\n        },\n        onContextMenu: (event)=>{\n            rest.onContextMenu == null ? void 0 : rest.onContextMenu.call(rest, event);\n            handleOnPointerUp(lastKnownPointerEventRef.current);\n        }\n    });\n});\nContent.displayName = \"Drawer.Content\";\nconst LONG_HANDLE_PRESS_TIMEOUT = 250;\nconst DOUBLE_TAP_TIMEOUT = 120;\nconst Handle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function({ preventCycle = false, children, ...rest }, ref) {\n    const { closeDrawer, isDragging, snapPoints, activeSnapPoint, setActiveSnapPoint, dismissible, handleOnly, isOpen, onPress, onDrag } = useDrawerContext();\n    const closeTimeoutIdRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const shouldCancelInteractionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    function handleStartCycle() {\n        // Stop if this is the second click of a double click\n        if (shouldCancelInteractionRef.current) {\n            handleCancelInteraction();\n            return;\n        }\n        window.setTimeout(()=>{\n            handleCycleSnapPoints();\n        }, DOUBLE_TAP_TIMEOUT);\n    }\n    function handleCycleSnapPoints() {\n        // Prevent accidental taps while resizing drawer\n        if (isDragging || preventCycle || shouldCancelInteractionRef.current) {\n            handleCancelInteraction();\n            return;\n        }\n        // Make sure to clear the timeout id if the user releases the handle before the cancel timeout\n        handleCancelInteraction();\n        if ((!snapPoints || snapPoints.length === 0) && dismissible) {\n            closeDrawer();\n            return;\n        }\n        const isLastSnapPoint = activeSnapPoint === snapPoints[snapPoints.length - 1];\n        if (isLastSnapPoint && dismissible) {\n            closeDrawer();\n            return;\n        }\n        const currentSnapIndex = snapPoints.findIndex((point)=>point === activeSnapPoint);\n        if (currentSnapIndex === -1) return; // activeSnapPoint not found in snapPoints\n        const nextSnapPoint = snapPoints[currentSnapIndex + 1];\n        setActiveSnapPoint(nextSnapPoint);\n    }\n    function handleStartInteraction() {\n        closeTimeoutIdRef.current = window.setTimeout(()=>{\n            // Cancel click interaction on a long press\n            shouldCancelInteractionRef.current = true;\n        }, LONG_HANDLE_PRESS_TIMEOUT);\n    }\n    function handleCancelInteraction() {\n        window.clearTimeout(closeTimeoutIdRef.current);\n        shouldCancelInteractionRef.current = false;\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        onClick: handleStartCycle,\n        onPointerCancel: handleCancelInteraction,\n        onPointerDown: (e)=>{\n            if (handleOnly) onPress(e);\n            handleStartInteraction();\n        },\n        onPointerMove: (e)=>{\n            if (handleOnly) onDrag(e);\n        },\n        // onPointerUp is already handled by the content component\n        ref: ref,\n        \"data-vaul-drawer-visible\": isOpen ? \"true\" : \"false\",\n        \"data-vaul-handle\": \"\",\n        \"aria-hidden\": \"true\",\n        ...rest\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", {\n        \"data-vaul-handle-hitarea\": \"\",\n        \"aria-hidden\": \"true\"\n    }, children));\n});\nHandle.displayName = \"Drawer.Handle\";\nfunction NestedRoot({ onDrag, onOpenChange, ...rest }) {\n    const { onNestedDrag, onNestedOpenChange, onNestedRelease } = useDrawerContext();\n    if (!onNestedDrag) {\n        throw new Error(\"Drawer.NestedRoot must be placed in another drawer\");\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Root, {\n        nested: true,\n        onClose: ()=>{\n            onNestedOpenChange(false);\n        },\n        onDrag: (e, p)=>{\n            onNestedDrag(e, p);\n            onDrag == null ? void 0 : onDrag(e, p);\n        },\n        onOpenChange: (o)=>{\n            if (o) {\n                onNestedOpenChange(o);\n            }\n        },\n        onRelease: onNestedRelease,\n        ...rest\n    });\n}\nfunction Portal(props) {\n    const context = useDrawerContext();\n    const { container = context.container, ...portalProps } = props;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Portal, {\n        container: container,\n        ...portalProps\n    });\n}\nconst Drawer = {\n    Root,\n    NestedRoot,\n    Content,\n    Overlay,\n    Trigger: _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Trigger,\n    Portal,\n    Handle,\n    Close: _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Close,\n    Title: _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Title,\n    Description: _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Description\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vaul/dist/index.mjs\n");

/***/ })

};
;
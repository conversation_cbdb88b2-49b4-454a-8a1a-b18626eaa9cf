"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@vis.gl";
exports.ids = ["vendor-chunks/@vis.gl"];
exports.modules = {

/***/ "(ssr)/./node_modules/@vis.gl/react-google-maps/dist/index.modern.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@vis.gl/react-google-maps/dist/index.modern.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APILoadingStatus: () => (/* binding */ APILoadingStatus),\n/* harmony export */   APIProvider: () => (/* binding */ APIProvider),\n/* harmony export */   APIProviderContext: () => (/* binding */ APIProviderContext),\n/* harmony export */   AdvancedMarker: () => (/* binding */ AdvancedMarker),\n/* harmony export */   AdvancedMarkerAnchorPoint: () => (/* binding */ AdvancedMarkerAnchorPoint),\n/* harmony export */   AdvancedMarkerContext: () => (/* binding */ AdvancedMarkerContext),\n/* harmony export */   CollisionBehavior: () => (/* binding */ CollisionBehavior),\n/* harmony export */   ColorScheme: () => (/* binding */ ColorScheme),\n/* harmony export */   ControlPosition: () => (/* binding */ ControlPosition),\n/* harmony export */   GoogleMapsContext: () => (/* binding */ GoogleMapsContext),\n/* harmony export */   InfoWindow: () => (/* binding */ InfoWindow),\n/* harmony export */   Map: () => (/* binding */ Map),\n/* harmony export */   MapControl: () => (/* binding */ MapControl),\n/* harmony export */   Marker: () => (/* binding */ Marker),\n/* harmony export */   Pin: () => (/* binding */ Pin),\n/* harmony export */   RenderingType: () => (/* binding */ RenderingType),\n/* harmony export */   StaticMap: () => (/* binding */ StaticMap),\n/* harmony export */   createStaticMapsUrl: () => (/* binding */ createStaticMapsUrl),\n/* harmony export */   isAdvancedMarker: () => (/* binding */ isAdvancedMarker),\n/* harmony export */   isLatLngLiteral: () => (/* binding */ isLatLngLiteral),\n/* harmony export */   latLngEquals: () => (/* binding */ latLngEquals),\n/* harmony export */   limitTiltRange: () => (/* binding */ limitTiltRange),\n/* harmony export */   toLatLngLiteral: () => (/* binding */ toLatLngLiteral),\n/* harmony export */   useAdvancedMarkerRef: () => (/* binding */ useAdvancedMarkerRef),\n/* harmony export */   useApiIsLoaded: () => (/* binding */ useApiIsLoaded),\n/* harmony export */   useApiLoadingStatus: () => (/* binding */ useApiLoadingStatus),\n/* harmony export */   useMap: () => (/* binding */ useMap),\n/* harmony export */   useMapsLibrary: () => (/* binding */ useMapsLibrary),\n/* harmony export */   useMarkerRef: () => (/* binding */ useMarkerRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var fast_deep_equal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fast-deep-equal */ \"(ssr)/./node_modules/fast-deep-equal/index.js\");\n\n\n\n\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\n\nconst APILoadingStatus = {\n  NOT_LOADED: 'NOT_LOADED',\n  LOADING: 'LOADING',\n  LOADED: 'LOADED',\n  FAILED: 'FAILED',\n  AUTH_FAILURE: 'AUTH_FAILURE'\n};\n\nconst MAPS_API_BASE_URL = 'https://maps.googleapis.com/maps/api/js';\n/**\n * A GoogleMapsApiLoader to reliably load and unload the Google Maps JavaScript API.\n *\n * The actual loading and unloading is delayed into the microtask queue, to\n * allow using the API in an useEffect hook, without worrying about multiple API loads.\n */\nclass GoogleMapsApiLoader {\n  /**\n   * Loads the Maps JavaScript API with the specified parameters.\n   * Since the Maps library can only be loaded once per page, this will\n   * produce a warning when called multiple times with different\n   * parameters.\n   *\n   * The returned promise resolves when loading completes\n   * and rejects in case of an error or when the loading was aborted.\n   */\n  static async load(params, onLoadingStatusChange) {\n    var _window$google;\n    const libraries = params.libraries ? params.libraries.split(',') : [];\n    const serializedParams = this.serializeParams(params);\n    this.listeners.push(onLoadingStatusChange);\n    // Note: if `google.maps.importLibrary` has been defined externally, we\n    //   assume that loading is complete and successful.\n    //   If it was defined by a previous call to this method, a warning\n    //   message is logged if there are differences in api-parameters used\n    //   for both calls.\n    if ((_window$google = window.google) != null && (_window$google = _window$google.maps) != null && _window$google.importLibrary) {\n      // no serialized parameters means it was loaded externally\n      if (!this.serializedApiParams) {\n        this.loadingStatus = APILoadingStatus.LOADED;\n      }\n      this.notifyLoadingStatusListeners();\n    } else {\n      this.serializedApiParams = serializedParams;\n      this.initImportLibrary(params);\n    }\n    if (this.serializedApiParams && this.serializedApiParams !== serializedParams) {\n      console.warn(`[google-maps-api-loader] The maps API has already been loaded ` + `with different parameters and will not be loaded again. Refresh the ` + `page for new values to have effect.`);\n    }\n    const librariesToLoad = ['maps', ...libraries];\n    await Promise.all(librariesToLoad.map(name => google.maps.importLibrary(name)));\n  }\n  /**\n   * Serialize the parameters used to load the library for easier comparison.\n   */\n  static serializeParams(params) {\n    return [params.v, params.key, params.language, params.region, params.authReferrerPolicy, params.solutionChannel].join('/');\n  }\n  /**\n   * Creates the global `google.maps.importLibrary` function for bootstrapping.\n   * This is essentially a formatted version of the dynamic loading script\n   * from the official documentation with some minor adjustments.\n   *\n   * The created importLibrary function will load the Google Maps JavaScript API,\n   * which will then replace the `google.maps.importLibrary` function with the full\n   * implementation.\n   *\n   * @see https://developers.google.com/maps/documentation/javascript/load-maps-js-api#dynamic-library-import\n   */\n  static initImportLibrary(params) {\n    if (!window.google) window.google = {};\n    if (!window.google.maps) window.google.maps = {};\n    if (window.google.maps['importLibrary']) {\n      console.error('[google-maps-api-loader-internal]: initImportLibrary must only be called once');\n      return;\n    }\n    let apiPromise = null;\n    const loadApi = () => {\n      if (apiPromise) return apiPromise;\n      apiPromise = new Promise((resolve, reject) => {\n        var _document$querySelect;\n        const scriptElement = document.createElement('script');\n        const urlParams = new URLSearchParams();\n        for (const [key, value] of Object.entries(params)) {\n          const urlParamName = key.replace(/[A-Z]/g, t => '_' + t[0].toLowerCase());\n          urlParams.set(urlParamName, String(value));\n        }\n        urlParams.set('loading', 'async');\n        urlParams.set('callback', '__googleMapsCallback__');\n        scriptElement.async = true;\n        scriptElement.src = MAPS_API_BASE_URL + `?` + urlParams.toString();\n        scriptElement.nonce = ((_document$querySelect = document.querySelector('script[nonce]')) == null ? void 0 : _document$querySelect.nonce) || '';\n        scriptElement.onerror = () => {\n          this.loadingStatus = APILoadingStatus.FAILED;\n          this.notifyLoadingStatusListeners();\n          reject(new Error('The Google Maps JavaScript API could not load.'));\n        };\n        window.__googleMapsCallback__ = () => {\n          this.loadingStatus = APILoadingStatus.LOADED;\n          this.notifyLoadingStatusListeners();\n          resolve();\n        };\n        window.gm_authFailure = () => {\n          this.loadingStatus = APILoadingStatus.AUTH_FAILURE;\n          this.notifyLoadingStatusListeners();\n        };\n        this.loadingStatus = APILoadingStatus.LOADING;\n        this.notifyLoadingStatusListeners();\n        document.head.append(scriptElement);\n      });\n      return apiPromise;\n    };\n    // for the first load, we declare an importLibrary function that will\n    // be overwritten once the api is loaded.\n    google.maps.importLibrary = libraryName => loadApi().then(() => google.maps.importLibrary(libraryName));\n  }\n  /**\n   * Calls all registered loadingStatusListeners after a status update.\n   */\n  static notifyLoadingStatusListeners() {\n    for (const fn of this.listeners) {\n      fn(this.loadingStatus);\n    }\n  }\n}\n/**\n * The current loadingStatus of the API.\n */\nGoogleMapsApiLoader.loadingStatus = APILoadingStatus.NOT_LOADED;\n/**\n * The parameters used for first loading the API.\n */\nGoogleMapsApiLoader.serializedApiParams = void 0;\n/**\n * A list of functions to be notified when the loading status changes.\n */\nGoogleMapsApiLoader.listeners = [];\n\nconst _excluded$3 = [\"onLoad\", \"onError\", \"apiKey\", \"version\", \"libraries\"],\n  _excluded2$1 = [\"children\"];\nconst DEFAULT_SOLUTION_CHANNEL = 'GMP_visgl_rgmlibrary_v1_default';\nconst APIProviderContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n/**\n * local hook to set up the map-instance management context.\n */\nfunction useMapInstances() {\n  const [mapInstances, setMapInstances] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n  const addMapInstance = (mapInstance, id = 'default') => {\n    setMapInstances(instances => _extends({}, instances, {\n      [id]: mapInstance\n    }));\n  };\n  const removeMapInstance = (id = 'default') => {\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    setMapInstances(_ref => {\n      let remaining = _objectWithoutPropertiesLoose(_ref, [id].map(_toPropertyKey));\n      return remaining;\n    });\n  };\n  const clearMapInstances = () => {\n    setMapInstances({});\n  };\n  return {\n    mapInstances,\n    addMapInstance,\n    removeMapInstance,\n    clearMapInstances\n  };\n}\n/**\n * local hook to handle the loading of the maps API, returns the current loading status\n * @param props\n */\nfunction useGoogleMapsApiLoader(props) {\n  const {\n      onLoad,\n      onError,\n      apiKey,\n      version,\n      libraries = []\n    } = props,\n    otherApiParams = _objectWithoutPropertiesLoose(props, _excluded$3);\n  const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(GoogleMapsApiLoader.loadingStatus);\n  const [loadedLibraries, addLoadedLibrary] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)((loadedLibraries, action) => {\n    return loadedLibraries[action.name] ? loadedLibraries : _extends({}, loadedLibraries, {\n      [action.name]: action.value\n    });\n  }, {});\n  const librariesString = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => libraries == null ? void 0 : libraries.join(','), [libraries]);\n  const serializedParams = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => JSON.stringify(_extends({\n    apiKey,\n    version\n  }, otherApiParams)), [apiKey, version, otherApiParams]);\n  const importLibrary = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async name => {\n    var _google;\n    if (loadedLibraries[name]) {\n      return loadedLibraries[name];\n    }\n    if (!((_google = google) != null && (_google = _google.maps) != null && _google.importLibrary)) {\n      throw new Error('[api-provider-internal] importLibrary was called before ' + 'google.maps.importLibrary was defined.');\n    }\n    const res = await window.google.maps.importLibrary(name);\n    addLoadedLibrary({\n      name,\n      value: res\n    });\n    return res;\n  }, [loadedLibraries]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    (async () => {\n      try {\n        const params = _extends({\n          key: apiKey\n        }, otherApiParams);\n        if (version) params.v = version;\n        if ((librariesString == null ? void 0 : librariesString.length) > 0) params.libraries = librariesString;\n        if (params.channel === undefined || params.channel < 0 || params.channel > 999) delete params.channel;\n        if (params.solutionChannel === undefined) params.solutionChannel = DEFAULT_SOLUTION_CHANNEL;else if (params.solutionChannel === '') delete params.solutionChannel;\n        await GoogleMapsApiLoader.load(params, status => setStatus(status));\n        for (const name of ['core', 'maps', ...libraries]) {\n          await importLibrary(name);\n        }\n        if (onLoad) {\n          onLoad();\n        }\n      } catch (error) {\n        if (onError) {\n          onError(error);\n        } else {\n          console.error('<ApiProvider> failed to load the Google Maps JavaScript API', error);\n        }\n      }\n    })();\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [apiKey, librariesString, serializedParams]);\n  return {\n    status,\n    loadedLibraries,\n    importLibrary\n  };\n}\n/**\n * Component to wrap the components from this library and load the Google Maps JavaScript API\n */\nconst APIProvider = props => {\n  const {\n      children\n    } = props,\n    loaderProps = _objectWithoutPropertiesLoose(props, _excluded2$1);\n  const {\n    mapInstances,\n    addMapInstance,\n    removeMapInstance,\n    clearMapInstances\n  } = useMapInstances();\n  const {\n    status,\n    loadedLibraries,\n    importLibrary\n  } = useGoogleMapsApiLoader(loaderProps);\n  const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    mapInstances,\n    addMapInstance,\n    removeMapInstance,\n    clearMapInstances,\n    status,\n    loadedLibraries,\n    importLibrary\n  }), [mapInstances, addMapInstance, removeMapInstance, clearMapInstances, status, loadedLibraries, importLibrary]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(APIProviderContext.Provider, {\n    value: contextValue\n  }, children);\n};\n\n/**\n * Sets up effects to bind event-handlers for all event-props in MapEventProps.\n * @internal\n */\nfunction useMapEvents(map, props) {\n  // note: calling a useEffect hook from within a loop is prohibited by the\n  // rules of hooks, but it's ok here since it's unconditional and the number\n  // and order of iterations is always strictly the same.\n  // (see https://legacy.reactjs.org/docs/hooks-rules.html)\n  for (const propName of eventPropNames) {\n    // fixme: this cast is essentially a 'trust me, bro' for typescript, but\n    //   a proper solution seems way too complicated right now\n    const handler = props[propName];\n    const eventType = propNameToEventType[propName];\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n      if (!map) return;\n      if (!handler) return;\n      const listener = google.maps.event.addListener(map, eventType, ev => {\n        handler(createMapEvent(eventType, map, ev));\n      });\n      return () => listener.remove();\n    }, [map, eventType, handler]);\n  }\n}\n/**\n * Create the wrapped map-events used for the event-props.\n * @param type the event type as it is specified to the maps api\n * @param map the map instance the event originates from\n * @param srcEvent the source-event if there is one.\n */\nfunction createMapEvent(type, map, srcEvent) {\n  const ev = {\n    type,\n    map,\n    detail: {},\n    stoppable: false,\n    stop: () => {}\n  };\n  if (cameraEventTypes.includes(type)) {\n    const camEvent = ev;\n    const center = map.getCenter();\n    const zoom = map.getZoom();\n    const heading = map.getHeading() || 0;\n    const tilt = map.getTilt() || 0;\n    const bounds = map.getBounds();\n    if (!center || !bounds || !Number.isFinite(zoom)) {\n      console.warn('[createEvent] at least one of the values from the map ' + 'returned undefined. This is not expected to happen. Please ' + 'report an issue at https://github.com/visgl/react-google-maps/issues/new');\n    }\n    camEvent.detail = {\n      center: (center == null ? void 0 : center.toJSON()) || {\n        lat: 0,\n        lng: 0\n      },\n      zoom: zoom || 0,\n      heading: heading,\n      tilt: tilt,\n      bounds: (bounds == null ? void 0 : bounds.toJSON()) || {\n        north: 90,\n        east: 180,\n        south: -90,\n        west: -180\n      }\n    };\n    return camEvent;\n  } else if (mouseEventTypes.includes(type)) {\n    var _srcEvent$latLng;\n    if (!srcEvent) throw new Error('[createEvent] mouse events must provide a srcEvent');\n    const mouseEvent = ev;\n    mouseEvent.domEvent = srcEvent.domEvent;\n    mouseEvent.stoppable = true;\n    mouseEvent.stop = () => srcEvent.stop();\n    mouseEvent.detail = {\n      latLng: ((_srcEvent$latLng = srcEvent.latLng) == null ? void 0 : _srcEvent$latLng.toJSON()) || null,\n      placeId: srcEvent.placeId\n    };\n    return mouseEvent;\n  }\n  return ev;\n}\n/**\n * maps the camelCased names of event-props to the corresponding event-types\n * used in the maps API.\n */\nconst propNameToEventType = {\n  onBoundsChanged: 'bounds_changed',\n  onCenterChanged: 'center_changed',\n  onClick: 'click',\n  onContextmenu: 'contextmenu',\n  onDblclick: 'dblclick',\n  onDrag: 'drag',\n  onDragend: 'dragend',\n  onDragstart: 'dragstart',\n  onHeadingChanged: 'heading_changed',\n  onIdle: 'idle',\n  onIsFractionalZoomEnabledChanged: 'isfractionalzoomenabled_changed',\n  onMapCapabilitiesChanged: 'mapcapabilities_changed',\n  onMapTypeIdChanged: 'maptypeid_changed',\n  onMousemove: 'mousemove',\n  onMouseout: 'mouseout',\n  onMouseover: 'mouseover',\n  onProjectionChanged: 'projection_changed',\n  onRenderingTypeChanged: 'renderingtype_changed',\n  onTilesLoaded: 'tilesloaded',\n  onTiltChanged: 'tilt_changed',\n  onZoomChanged: 'zoom_changed',\n  // note: onCameraChanged is an alias for the bounds_changed event,\n  // since that is going to be fired in every situation where the camera is\n  // updated.\n  onCameraChanged: 'bounds_changed'\n};\nconst cameraEventTypes = ['bounds_changed', 'center_changed', 'heading_changed', 'tilt_changed', 'zoom_changed'];\nconst mouseEventTypes = ['click', 'contextmenu', 'dblclick', 'mousemove', 'mouseout', 'mouseover'];\nconst eventPropNames = Object.keys(propNameToEventType);\n\nfunction useMemoized(value, isEqual) {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(value);\n  if (!isEqual(value, ref.current)) {\n    ref.current = value;\n  }\n  return ref.current;\n}\n\nfunction useCustomCompareEffect(effect, dependencies, isEqual) {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(effect, [useMemoized(dependencies, isEqual)]);\n}\n\nfunction useDeepCompareEffect(effect, dependencies) {\n  useCustomCompareEffect(effect, dependencies, fast_deep_equal__WEBPACK_IMPORTED_MODULE_2__);\n}\n\nconst mapOptionKeys = new Set(['backgroundColor', 'clickableIcons', 'controlSize', 'disableDefaultUI', 'disableDoubleClickZoom', 'draggable', 'draggableCursor', 'draggingCursor', 'fullscreenControl', 'fullscreenControlOptions', 'gestureHandling', 'headingInteractionEnabled', 'isFractionalZoomEnabled', 'keyboardShortcuts', 'mapTypeControl', 'mapTypeControlOptions', 'mapTypeId', 'maxZoom', 'minZoom', 'noClear', 'panControl', 'panControlOptions', 'restriction', 'rotateControl', 'rotateControlOptions', 'scaleControl', 'scaleControlOptions', 'scrollwheel', 'streetView', 'streetViewControl', 'streetViewControlOptions', 'styles', 'tiltInteractionEnabled', 'zoomControl', 'zoomControlOptions']);\n/**\n * Internal hook to update the map-options when props are changed.\n *\n * @param map the map instance\n * @param mapProps the props to update the map-instance with\n * @internal\n */\nfunction useMapOptions(map, mapProps) {\n  /* eslint-disable react-hooks/exhaustive-deps --\n   *\n   * The following effects aren't triggered when the map is changed.\n   * In that case, the values will be or have been passed to the map\n   * constructor via mapOptions.\n   */\n  const mapOptions = {};\n  const keys = Object.keys(mapProps);\n  for (const key of keys) {\n    if (!mapOptionKeys.has(key)) continue;\n    mapOptions[key] = mapProps[key];\n  }\n  // update the map options when mapOptions is changed\n  // Note: due to the destructuring above, mapOptions will be seen as changed\n  //   with every re-render, so we're assuming the maps-api will properly\n  //   deal with unchanged option-values passed into setOptions.\n  useDeepCompareEffect(() => {\n    if (!map) return;\n    map.setOptions(mapOptions);\n  }, [mapOptions]);\n  /* eslint-enable react-hooks/exhaustive-deps */\n}\n\nfunction useApiLoadingStatus() {\n  var _useContext;\n  return ((_useContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(APIProviderContext)) == null ? void 0 : _useContext.status) || APILoadingStatus.NOT_LOADED;\n}\n\n/**\n * Internal hook that updates the camera when deck.gl viewState changes.\n * @internal\n */\nfunction useDeckGLCameraUpdate(map, props) {\n  const {\n    viewport,\n    viewState\n  } = props;\n  const isDeckGlControlled = !!viewport;\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    if (!map || !viewState) return;\n    const {\n      latitude,\n      longitude,\n      bearing: heading,\n      pitch: tilt,\n      zoom\n    } = viewState;\n    map.moveCamera({\n      center: {\n        lat: latitude,\n        lng: longitude\n      },\n      heading,\n      tilt,\n      zoom: zoom + 1\n    });\n  }, [map, viewState]);\n  return isDeckGlControlled;\n}\n\nfunction isLatLngLiteral(obj) {\n  if (!obj || typeof obj !== 'object') return false;\n  if (!('lat' in obj && 'lng' in obj)) return false;\n  return Number.isFinite(obj.lat) && Number.isFinite(obj.lng);\n}\nfunction latLngEquals(a, b) {\n  if (!a || !b) return false;\n  const A = toLatLngLiteral(a);\n  const B = toLatLngLiteral(b);\n  if (A.lat !== B.lat || A.lng !== B.lng) return false;\n  return true;\n}\nfunction toLatLngLiteral(obj) {\n  if (isLatLngLiteral(obj)) return obj;\n  return obj.toJSON();\n}\n\nfunction useMapCameraParams(map, cameraStateRef, mapProps) {\n  const center = mapProps.center ? toLatLngLiteral(mapProps.center) : null;\n  let lat = null;\n  let lng = null;\n  if (center && Number.isFinite(center.lat) && Number.isFinite(center.lng)) {\n    lat = center.lat;\n    lng = center.lng;\n  }\n  const zoom = Number.isFinite(mapProps.zoom) ? mapProps.zoom : null;\n  const heading = Number.isFinite(mapProps.heading) ? mapProps.heading : null;\n  const tilt = Number.isFinite(mapProps.tilt) ? mapProps.tilt : null;\n  // the following effect runs for every render of the map component and checks\n  // if there are differences between the known state of the map instance\n  // (cameraStateRef, which is updated by all bounds_changed events) and the\n  // desired state in the props.\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    if (!map) return;\n    const nextCamera = {};\n    let needsUpdate = false;\n    if (lat !== null && lng !== null && (cameraStateRef.current.center.lat !== lat || cameraStateRef.current.center.lng !== lng)) {\n      nextCamera.center = {\n        lat,\n        lng\n      };\n      needsUpdate = true;\n    }\n    if (zoom !== null && cameraStateRef.current.zoom !== zoom) {\n      nextCamera.zoom = zoom;\n      needsUpdate = true;\n    }\n    if (heading !== null && cameraStateRef.current.heading !== heading) {\n      nextCamera.heading = heading;\n      needsUpdate = true;\n    }\n    if (tilt !== null && cameraStateRef.current.tilt !== tilt) {\n      nextCamera.tilt = tilt;\n      needsUpdate = true;\n    }\n    if (needsUpdate) {\n      map.moveCamera(nextCamera);\n    }\n  });\n}\n\nconst AuthFailureMessage = () => {\n  const style = {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    bottom: 0,\n    right: 0,\n    zIndex: 999,\n    display: 'flex',\n    flexFlow: 'column nowrap',\n    textAlign: 'center',\n    justifyContent: 'center',\n    fontSize: '.8rem',\n    color: 'rgba(0,0,0,0.6)',\n    background: '#dddddd',\n    padding: '1rem 1.5rem'\n  };\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n    style: style\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"h2\", null, \"Error: AuthFailure\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"p\", null, \"A problem with your API key prevents the map from rendering correctly. Please make sure the value of the \", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"code\", null, \"APIProvider.apiKey\"), \" prop is correct. Check the error-message in the console for further details.\"));\n};\n\nfunction useCallbackRef() {\n  const [el, setEl] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(value => setEl(value), [setEl]);\n  return [el, ref];\n}\n\n/**\n * Hook to check if the Maps JavaScript API is loaded\n */\nfunction useApiIsLoaded() {\n  const status = useApiLoadingStatus();\n  return status === APILoadingStatus.LOADED;\n}\n\nfunction useForceUpdate() {\n  const [, forceUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(x => x + 1, 0);\n  return forceUpdate;\n}\n\nfunction handleBoundsChange(map, ref) {\n  const center = map.getCenter();\n  const zoom = map.getZoom();\n  const heading = map.getHeading() || 0;\n  const tilt = map.getTilt() || 0;\n  const bounds = map.getBounds();\n  if (!center || !bounds || !Number.isFinite(zoom)) {\n    console.warn('[useTrackedCameraState] at least one of the values from the map ' + 'returned undefined. This is not expected to happen. Please ' + 'report an issue at https://github.com/visgl/react-google-maps/issues/new');\n  }\n  // fixme: do we need the `undefined` cases for the camera-params? When are they used in the maps API?\n  Object.assign(ref.current, {\n    center: (center == null ? void 0 : center.toJSON()) || {\n      lat: 0,\n      lng: 0\n    },\n    zoom: zoom || 0,\n    heading: heading,\n    tilt: tilt\n  });\n}\n/**\n * Creates a mutable ref object to track the last known state of the map camera.\n * This is used in `useMapCameraParams` to reduce stuttering in normal operation\n * by avoiding updates of the map camera with values that have already been processed.\n */\nfunction useTrackedCameraStateRef(map) {\n  const forceUpdate = useForceUpdate();\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    center: {\n      lat: 0,\n      lng: 0\n    },\n    heading: 0,\n    tilt: 0,\n    zoom: 0\n  });\n  // Record camera state with every bounds_changed event dispatched by the map.\n  // This data is used to prevent feeding these values back to the\n  // map-instance when a typical \"controlled component\" setup (state variable is\n  // fed into and updated by the map).\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!map) return;\n    const listener = google.maps.event.addListener(map, 'bounds_changed', () => {\n      handleBoundsChange(map, ref);\n      // When an event is occured, we have to update during the next cycle.\n      // The application could decide to ignore the event and not update any\n      // camera props of the map, meaning that in that case we will have to\n      // 'undo' the change to the camera.\n      forceUpdate();\n    });\n    return () => listener.remove();\n  }, [map, forceUpdate]);\n  return ref;\n}\n\nconst _excluded$2 = [\"id\", \"defaultBounds\", \"defaultCenter\", \"defaultZoom\", \"defaultHeading\", \"defaultTilt\", \"reuseMaps\", \"renderingType\", \"colorScheme\"],\n  _excluded2 = [\"padding\"];\n/**\n * Stores a stack of map-instances for each mapId. Whenever an\n * instance is used, it is removed from the stack while in use,\n * and returned to the stack when the component unmounts.\n * This allows us to correctly implement caching for multiple\n * maps om the same page, while reusing as much as possible.\n *\n * FIXME: while it should in theory be possible to reuse maps solely\n *   based on the mapId (as all other parameters can be changed at\n *   runtime), we don't yet have good enough tracking of options to\n *   reliably unset all the options that have been set.\n */\nclass CachedMapStack {\n  static has(key) {\n    return this.entries[key] && this.entries[key].length > 0;\n  }\n  static pop(key) {\n    if (!this.entries[key]) return null;\n    return this.entries[key].pop() || null;\n  }\n  static push(key, value) {\n    if (!this.entries[key]) this.entries[key] = [];\n    this.entries[key].push(value);\n  }\n}\n/**\n * The main hook takes care of creating map-instances and registering them in\n * the api-provider context.\n * @return a tuple of the map-instance created (or null) and the callback\n *   ref that will be used to pass the map-container into this hook.\n * @internal\n */\nCachedMapStack.entries = {};\nfunction useMapInstance(props, context) {\n  const apiIsLoaded = useApiIsLoaded();\n  const [map, setMap] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [container, containerRef] = useCallbackRef();\n  const cameraStateRef = useTrackedCameraStateRef(map);\n  const {\n      id,\n      defaultBounds,\n      defaultCenter,\n      defaultZoom,\n      defaultHeading,\n      defaultTilt,\n      reuseMaps,\n      renderingType,\n      colorScheme\n    } = props,\n    mapOptions = _objectWithoutPropertiesLoose(props, _excluded$2);\n  const hasZoom = props.zoom !== undefined || props.defaultZoom !== undefined;\n  const hasCenter = props.center !== undefined || props.defaultCenter !== undefined;\n  if (!defaultBounds && (!hasZoom || !hasCenter)) {\n    console.warn('<Map> component is missing configuration. ' + 'You have to provide zoom and center (via the `zoom`/`defaultZoom` and ' + '`center`/`defaultCenter` props) or specify the region to show using ' + '`defaultBounds`. See ' + 'https://visgl.github.io/react-google-maps/docs/api-reference/components/map#required');\n  }\n  // apply default camera props if available and not overwritten by controlled props\n  if (!mapOptions.center && defaultCenter) mapOptions.center = defaultCenter;\n  if (!mapOptions.zoom && Number.isFinite(defaultZoom)) mapOptions.zoom = defaultZoom;\n  if (!mapOptions.heading && Number.isFinite(defaultHeading)) mapOptions.heading = defaultHeading;\n  if (!mapOptions.tilt && Number.isFinite(defaultTilt)) mapOptions.tilt = defaultTilt;\n  for (const key of Object.keys(mapOptions)) if (mapOptions[key] === undefined) delete mapOptions[key];\n  const savedMapStateRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(undefined);\n  // create the map instance and register it in the context\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!container || !apiIsLoaded) return;\n    const {\n      addMapInstance,\n      removeMapInstance\n    } = context;\n    // note: colorScheme (upcoming feature) isn't yet in the typings, remove once that is fixed:\n    const {\n      mapId\n    } = props;\n    const cacheKey = `${mapId || 'default'}:${renderingType || 'default'}:${colorScheme || 'LIGHT'}`;\n    let mapDiv;\n    let map;\n    if (reuseMaps && CachedMapStack.has(cacheKey)) {\n      map = CachedMapStack.pop(cacheKey);\n      mapDiv = map.getDiv();\n      container.appendChild(mapDiv);\n      map.setOptions(mapOptions);\n      // detaching the element from the DOM lets the map fall back to its default\n      // size, setting the center will trigger reloading the map.\n      setTimeout(() => map.setCenter(map.getCenter()), 0);\n    } else {\n      mapDiv = document.createElement('div');\n      mapDiv.style.height = '100%';\n      container.appendChild(mapDiv);\n      map = new google.maps.Map(mapDiv, _extends({}, mapOptions, renderingType ? {\n        renderingType: renderingType\n      } : {}, colorScheme ? {\n        colorScheme: colorScheme\n      } : {}));\n    }\n    setMap(map);\n    addMapInstance(map, id);\n    if (defaultBounds) {\n      const {\n          padding\n        } = defaultBounds,\n        defBounds = _objectWithoutPropertiesLoose(defaultBounds, _excluded2);\n      map.fitBounds(defBounds, padding);\n    }\n    // prevent map not rendering due to missing configuration\n    else if (!hasZoom || !hasCenter) {\n      map.fitBounds({\n        east: 180,\n        west: -180,\n        south: -90,\n        north: 90\n      });\n    }\n    // the savedMapState is used to restore the camera parameters when the mapId is changed\n    if (savedMapStateRef.current) {\n      const {\n        mapId: savedMapId,\n        cameraState: savedCameraState\n      } = savedMapStateRef.current;\n      if (savedMapId !== mapId) {\n        map.setOptions(savedCameraState);\n      }\n    }\n    return () => {\n      savedMapStateRef.current = {\n        mapId,\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        cameraState: cameraStateRef.current\n      };\n      // detach the map-div from the dom\n      mapDiv.remove();\n      if (reuseMaps) {\n        // push back on the stack\n        CachedMapStack.push(cacheKey, map);\n      } else {\n        // remove all event-listeners to minimize the possibility of memory-leaks\n        google.maps.event.clearInstanceListeners(map);\n      }\n      setMap(null);\n      removeMapInstance(id);\n    };\n  },\n  // some dependencies are ignored in the list below:\n  //  - defaultBounds and the default* camera props will only be used once, and\n  //    changes should be ignored\n  //  - mapOptions has special hooks that take care of updating the options\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [container, apiIsLoaded, id,\n  // these props can't be changed after initialization and require a new\n  // instance to be created\n  props.mapId, props.renderingType, props.colorScheme]);\n  return [map, containerRef, cameraStateRef];\n}\n\nconst GoogleMapsContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n// ColorScheme and RenderingType are redefined here to make them usable before the\n// maps API has been fully loaded.\nconst ColorScheme = {\n  DARK: 'DARK',\n  LIGHT: 'LIGHT',\n  FOLLOW_SYSTEM: 'FOLLOW_SYSTEM'\n};\nconst RenderingType = {\n  VECTOR: 'VECTOR',\n  RASTER: 'RASTER',\n  UNINITIALIZED: 'UNINITIALIZED'\n};\nconst Map = props => {\n  const {\n    children,\n    id,\n    className,\n    style\n  } = props;\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(APIProviderContext);\n  const loadingStatus = useApiLoadingStatus();\n  if (!context) {\n    throw new Error('<Map> can only be used inside an <ApiProvider> component.');\n  }\n  const [map, mapRef, cameraStateRef] = useMapInstance(props, context);\n  useMapCameraParams(map, cameraStateRef, props);\n  useMapEvents(map, props);\n  useMapOptions(map, props);\n  const isDeckGlControlled = useDeckGLCameraUpdate(map, props);\n  const isControlledExternally = !!props.controlled;\n  // disable interactions with the map for externally controlled maps\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!map) return;\n    // fixme: this doesn't seem to belong here (and it's mostly there for convenience anyway).\n    //   The reasoning is that a deck.gl canvas will be put on top of the map, rendering\n    //   any default map controls pretty much useless\n    if (isDeckGlControlled) {\n      map.setOptions({\n        disableDefaultUI: true\n      });\n    }\n    // disable all control-inputs when the map is controlled externally\n    if (isDeckGlControlled || isControlledExternally) {\n      map.setOptions({\n        gestureHandling: 'none',\n        keyboardShortcuts: false\n      });\n    }\n    return () => {\n      map.setOptions({\n        gestureHandling: props.gestureHandling,\n        keyboardShortcuts: props.keyboardShortcuts\n      });\n    };\n  }, [map, isDeckGlControlled, isControlledExternally, props.gestureHandling, props.keyboardShortcuts]);\n  // setup a stable cameraOptions object that can be used as dependency\n  const center = props.center ? toLatLngLiteral(props.center) : null;\n  let lat = null;\n  let lng = null;\n  if (center && Number.isFinite(center.lat) && Number.isFinite(center.lng)) {\n    lat = center.lat;\n    lng = center.lng;\n  }\n  const cameraOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    var _lat, _lng, _props$zoom, _props$heading, _props$tilt;\n    return {\n      center: {\n        lat: (_lat = lat) != null ? _lat : 0,\n        lng: (_lng = lng) != null ? _lng : 0\n      },\n      zoom: (_props$zoom = props.zoom) != null ? _props$zoom : 0,\n      heading: (_props$heading = props.heading) != null ? _props$heading : 0,\n      tilt: (_props$tilt = props.tilt) != null ? _props$tilt : 0\n    };\n  }, [lat, lng, props.zoom, props.heading, props.tilt]);\n  // externally controlled mode: reject all camera changes that don't correspond to changes in props\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    if (!map || !isControlledExternally) return;\n    map.moveCamera(cameraOptions);\n    const listener = map.addListener('bounds_changed', () => {\n      map.moveCamera(cameraOptions);\n    });\n    return () => listener.remove();\n  }, [map, isControlledExternally, cameraOptions]);\n  const combinedStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => _extends({\n    width: '100%',\n    height: '100%',\n    position: 'relative',\n    // when using deckgl, the map should be sent to the back\n    zIndex: isDeckGlControlled ? -1 : 0\n  }, style), [style, isDeckGlControlled]);\n  const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    map\n  }), [map]);\n  if (loadingStatus === APILoadingStatus.AUTH_FAILURE) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n      style: _extends({\n        position: 'relative'\n      }, className ? {} : combinedStyle),\n      className: className\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(AuthFailureMessage, null));\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", _extends({\n    ref: mapRef,\n    \"data-testid\": 'map',\n    style: className ? undefined : combinedStyle,\n    className: className\n  }, id ? {\n    id\n  } : {}), map ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(GoogleMapsContext.Provider, {\n    value: contextValue\n  }, children) : null);\n};\n// The deckGLViewProps flag here indicates to deck.gl that the Map component is\n// able to handle viewProps from deck.gl when deck.gl is used to control the map.\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nMap.deckGLViewProps = true;\n\nconst shownMessages = new Set();\nfunction logErrorOnce(...args) {\n  const key = JSON.stringify(args);\n  if (!shownMessages.has(key)) {\n    shownMessages.add(key);\n    console.error(...args);\n  }\n}\n\n/**\n * Retrieves a map-instance from the context. This is either an instance\n * identified by id or the parent map instance if no id is specified.\n * Returns null if neither can be found.\n */\nconst useMap = (id = null) => {\n  const ctx = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(APIProviderContext);\n  const {\n    map\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(GoogleMapsContext) || {};\n  if (ctx === null) {\n    logErrorOnce('useMap(): failed to retrieve APIProviderContext. ' + 'Make sure that the <APIProvider> component exists and that the ' + 'component you are calling `useMap()` from is a sibling of the ' + '<APIProvider>.');\n    return null;\n  }\n  const {\n    mapInstances\n  } = ctx;\n  // if an id is specified, the corresponding map or null is returned\n  if (id !== null) return mapInstances[id] || null;\n  // otherwise, return the closest ancestor\n  if (map) return map;\n  // finally, return the default map instance\n  return mapInstances['default'] || null;\n};\n\nfunction useMapsLibrary(name) {\n  const apiIsLoaded = useApiIsLoaded();\n  const ctx = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(APIProviderContext);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!apiIsLoaded || !ctx) return;\n    // Trigger loading the libraries via our proxy-method.\n    // The returned promise is ignored, since importLibrary will update loadedLibraries\n    // list in the context, triggering a re-render.\n    void ctx.importLibrary(name);\n  }, [apiIsLoaded, ctx, name]);\n  return (ctx == null ? void 0 : ctx.loadedLibraries[name]) || null;\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Internally used to bind events to Maps JavaScript API objects.\n * @internal\n */\nfunction useMapsEventListener(target, name, callback) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!target || !name || !callback) return;\n    const listener = google.maps.event.addListener(target, name, callback);\n    return () => listener.remove();\n  }, [target, name, callback]);\n}\n\n/**\n * Internally used to copy values from props into API-Objects\n * whenever they change.\n *\n * @example\n *   usePropBinding(marker, 'position', position);\n *\n * @internal\n */\nfunction usePropBinding(object, prop, value) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!object) return;\n    object[prop] = value;\n  }, [object, prop, value]);\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Internally used to bind events to DOM nodes.\n * @internal\n */\nfunction useDomEventListener(target, name, callback) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!target || !name || !callback) return;\n    target.addEventListener(name, callback);\n    return () => target.removeEventListener(name, callback);\n  }, [target, name, callback]);\n}\n\n/* eslint-disable complexity */\nfunction isAdvancedMarker(marker) {\n  return marker.content !== undefined;\n}\nfunction isElementNode(node) {\n  return node.nodeType === Node.ELEMENT_NODE;\n}\n/**\n * Copy of the `google.maps.CollisionBehavior` constants.\n * They have to be duplicated here since we can't wait for the maps API to load to be able to use them.\n */\nconst CollisionBehavior = {\n  REQUIRED: 'REQUIRED',\n  REQUIRED_AND_HIDES_OPTIONAL: 'REQUIRED_AND_HIDES_OPTIONAL',\n  OPTIONAL_AND_HIDES_LOWER_PRIORITY: 'OPTIONAL_AND_HIDES_LOWER_PRIORITY'\n};\nconst AdvancedMarkerContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n// [xPosition, yPosition] when the top left corner is [0, 0]\nconst AdvancedMarkerAnchorPoint = {\n  TOP_LEFT: ['0%', '0%'],\n  TOP_CENTER: ['50%', '0%'],\n  TOP: ['50%', '0%'],\n  TOP_RIGHT: ['100%', '0%'],\n  LEFT_CENTER: ['0%', '50%'],\n  LEFT_TOP: ['0%', '0%'],\n  LEFT: ['0%', '50%'],\n  LEFT_BOTTOM: ['0%', '100%'],\n  RIGHT_TOP: ['100%', '0%'],\n  RIGHT: ['100%', '50%'],\n  RIGHT_CENTER: ['100%', '50%'],\n  RIGHT_BOTTOM: ['100%', '100%'],\n  BOTTOM_LEFT: ['0%', '100%'],\n  BOTTOM_CENTER: ['50%', '100%'],\n  BOTTOM: ['50%', '100%'],\n  BOTTOM_RIGHT: ['100%', '100%'],\n  CENTER: ['50%', '50%']\n};\nconst MarkerContent = ({\n  children,\n  styles,\n  className,\n  anchorPoint\n}) => {\n  const [xTranslation, yTranslation] = anchorPoint != null ? anchorPoint : AdvancedMarkerAnchorPoint['BOTTOM'];\n  let xTranslationFlipped = `-${xTranslation}`;\n  let yTranslationFlipped = `-${yTranslation}`;\n  if (xTranslation.trimStart().startsWith('-')) {\n    xTranslationFlipped = xTranslation.substring(1);\n  }\n  if (yTranslation.trimStart().startsWith('-')) {\n    yTranslationFlipped = yTranslation.substring(1);\n  }\n  // The \"translate(50%, 100%)\" is here to counter and reset the default anchoring of the advanced marker element\n  // that comes from the api\n  const transformStyle = `translate(50%, 100%) translate(${xTranslationFlipped}, ${yTranslationFlipped})`;\n  return (\n    /*#__PURE__*/\n    // anchoring container\n    react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n      style: {\n        transform: transformStyle\n      }\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n      className: className,\n      style: styles\n    }, children))\n  );\n};\nfunction useAdvancedMarker(props) {\n  const [marker, setMarker] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [contentContainer, setContentContainer] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const map = useMap();\n  const markerLibrary = useMapsLibrary('marker');\n  const {\n    children,\n    onClick,\n    className,\n    onMouseEnter,\n    onMouseLeave,\n    onDrag,\n    onDragStart,\n    onDragEnd,\n    collisionBehavior,\n    clickable,\n    draggable,\n    position,\n    title,\n    zIndex\n  } = props;\n  const numChildren = react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children);\n  // create an AdvancedMarkerElement instance and add it to the map once available\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!map || !markerLibrary) return;\n    const newMarker = new markerLibrary.AdvancedMarkerElement();\n    newMarker.map = map;\n    setMarker(newMarker);\n    // create the container for marker content if there are children\n    let contentElement = null;\n    if (numChildren > 0) {\n      contentElement = document.createElement('div');\n      // We need some kind of flag to identify the custom marker content\n      // in the infowindow component. Choosing a custom property instead of a className\n      // to not encourage users to style the marker content directly.\n      contentElement.isCustomMarker = true;\n      newMarker.content = contentElement;\n      setContentContainer(contentElement);\n    }\n    return () => {\n      var _contentElement;\n      newMarker.map = null;\n      (_contentElement = contentElement) == null || _contentElement.remove();\n      setMarker(null);\n      setContentContainer(null);\n    };\n  }, [map, markerLibrary, numChildren]);\n  // When no children are present we don't have our own wrapper div\n  // which usually gets the user provided className. In this case\n  // we set the className directly on the marker.content element that comes\n  // with the AdvancedMarker.\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!(marker != null && marker.content) || !isElementNode(marker.content) || numChildren > 0) return;\n    marker.content.className = className != null ? className : '';\n  }, [marker, className, numChildren]);\n  // copy other props\n  usePropBinding(marker, 'position', position);\n  usePropBinding(marker, 'title', title != null ? title : '');\n  usePropBinding(marker, 'zIndex', zIndex);\n  usePropBinding(marker, 'collisionBehavior', collisionBehavior);\n  // set gmpDraggable from props (when unspecified, it's true if any drag-event\n  // callbacks are specified)\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!marker) return;\n    if (draggable !== undefined) marker.gmpDraggable = draggable;else if (onDrag || onDragStart || onDragEnd) marker.gmpDraggable = true;else marker.gmpDraggable = false;\n  }, [marker, draggable, onDrag, onDragEnd, onDragStart]);\n  // set gmpClickable from props (when unspecified, it's true if the onClick or one of\n  // the hover events callbacks are specified)\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!marker) return;\n    const gmpClickable = clickable !== undefined || Boolean(onClick) || Boolean(onMouseEnter) || Boolean(onMouseLeave);\n    // gmpClickable is only available in beta version of the\n    // maps api (as of 2024-10-10)\n    marker.gmpClickable = gmpClickable;\n    // enable pointer events for the markers with custom content\n    if (gmpClickable && marker != null && marker.content && isElementNode(marker.content)) {\n      marker.content.style.pointerEvents = 'none';\n      if (marker.content.firstElementChild) {\n        marker.content.firstElementChild.style.pointerEvents = 'all';\n      }\n    }\n  }, [marker, clickable, onClick, onMouseEnter, onMouseLeave]);\n  useMapsEventListener(marker, 'click', onClick);\n  useMapsEventListener(marker, 'drag', onDrag);\n  useMapsEventListener(marker, 'dragstart', onDragStart);\n  useMapsEventListener(marker, 'dragend', onDragEnd);\n  useDomEventListener(marker == null ? void 0 : marker.element, 'mouseenter', onMouseEnter);\n  useDomEventListener(marker == null ? void 0 : marker.element, 'mouseleave', onMouseLeave);\n  return [marker, contentContainer];\n}\nconst AdvancedMarker = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => {\n  const {\n    children,\n    style,\n    className,\n    anchorPoint\n  } = props;\n  const [marker, contentContainer] = useAdvancedMarker(props);\n  const advancedMarkerContextValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => marker ? {\n    marker\n  } : null, [marker]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => marker, [marker]);\n  if (!contentContainer) return null;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(AdvancedMarkerContext.Provider, {\n    value: advancedMarkerContextValue\n  }, (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(MarkerContent, {\n    anchorPoint: anchorPoint,\n    styles: style,\n    className: className\n  }, children), contentContainer));\n});\nfunction useAdvancedMarkerRef() {\n  const [marker, setMarker] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const refCallback = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(m => {\n    setMarker(m);\n  }, []);\n  return [refCallback, marker];\n}\n\nfunction setValueForStyles(element, styles, prevStyles) {\n  if (styles != null && typeof styles !== 'object') {\n    throw new Error('The `style` prop expects a mapping from style properties to values, ' + \"not a string. For example, style={{marginRight: spacing + 'em'}} when \" + 'using JSX.');\n  }\n  const elementStyle = element.style;\n  // without `prevStyles`, just set all values\n  if (prevStyles == null) {\n    if (styles == null) return;\n    for (const styleName in styles) {\n      if (!styles.hasOwnProperty(styleName)) continue;\n      setValueForStyle(elementStyle, styleName, styles[styleName]);\n    }\n    return;\n  }\n  // unset all styles in `prevStyles` that aren't in `styles`\n  for (const styleName in prevStyles) {\n    if (prevStyles.hasOwnProperty(styleName) && (styles == null || !styles.hasOwnProperty(styleName))) {\n      // Clear style\n      const isCustomProperty = styleName.indexOf('--') === 0;\n      if (isCustomProperty) {\n        elementStyle.setProperty(styleName, '');\n      } else if (styleName === 'float') {\n        elementStyle.cssFloat = '';\n      } else {\n        elementStyle[styleName] = '';\n      }\n    }\n  }\n  // only assign values from `styles` that are different from `prevStyles`\n  if (styles == null) return;\n  for (const styleName in styles) {\n    const value = styles[styleName];\n    if (styles.hasOwnProperty(styleName) && prevStyles[styleName] !== value) {\n      setValueForStyle(elementStyle, styleName, value);\n    }\n  }\n}\nfunction setValueForStyle(elementStyle, styleName, value) {\n  const isCustomProperty = styleName.indexOf('--') === 0;\n  // falsy values will unset the style property\n  if (value == null || typeof value === 'boolean' || value === '') {\n    if (isCustomProperty) {\n      elementStyle.setProperty(styleName, '');\n    } else if (styleName === 'float') {\n      elementStyle.cssFloat = '';\n    } else {\n      elementStyle[styleName] = '';\n    }\n  }\n  // custom properties can't be directly assigned\n  else if (isCustomProperty) {\n    elementStyle.setProperty(styleName, value);\n  }\n  // numeric values are treated as 'px' unless the style property expects unitless numbers\n  else if (typeof value === 'number' && value !== 0 && !isUnitlessNumber(styleName)) {\n    elementStyle[styleName] = value + 'px'; // Presumes implicit 'px' suffix for unitless numbers\n  }\n  // everything else can just be assigned\n  else {\n    if (styleName === 'float') {\n      elementStyle.cssFloat = value;\n    } else {\n      elementStyle[styleName] = ('' + value).trim();\n    }\n  }\n}\n// CSS properties which accept numbers but are not in units of \"px\".\nconst unitlessNumbers = new Set(['animationIterationCount', 'aspectRatio', 'borderImageOutset', 'borderImageSlice', 'borderImageWidth', 'boxFlex', 'boxFlexGroup', 'boxOrdinalGroup', 'columnCount', 'columns', 'flex', 'flexGrow', 'flexPositive', 'flexShrink', 'flexNegative', 'flexOrder', 'gridArea', 'gridRow', 'gridRowEnd', 'gridRowSpan', 'gridRowStart', 'gridColumn', 'gridColumnEnd', 'gridColumnSpan', 'gridColumnStart', 'fontWeight', 'lineClamp', 'lineHeight', 'opacity', 'order', 'orphans', 'scale', 'tabSize', 'widows', 'zIndex', 'zoom', 'fillOpacity',\n// SVG-related properties\n'floodOpacity', 'stopOpacity', 'strokeDasharray', 'strokeDashoffset', 'strokeMiterlimit', 'strokeOpacity', 'strokeWidth']);\nfunction isUnitlessNumber(name) {\n  return unitlessNumbers.has(name);\n}\n\nconst _excluded$1 = [\"children\", \"headerContent\", \"style\", \"className\", \"pixelOffset\", \"anchor\", \"shouldFocus\", \"onClose\", \"onCloseClick\"];\n/**\n * Component to render an Info Window with the Maps JavaScript API\n */\nconst InfoWindow = props => {\n  const {\n      // content options\n      children,\n      headerContent,\n      style,\n      className,\n      pixelOffset,\n      // open options\n      anchor,\n      shouldFocus,\n      // events\n      onClose,\n      onCloseClick\n      // other options\n    } = props,\n    volatileInfoWindowOptions = _objectWithoutPropertiesLoose(props, _excluded$1);\n  // ## create infowindow instance once the mapsLibrary is available.\n  const mapsLibrary = useMapsLibrary('maps');\n  const [infoWindow, setInfoWindow] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const contentContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const headerContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const infoWindowOptions = useMemoized(volatileInfoWindowOptions, fast_deep_equal__WEBPACK_IMPORTED_MODULE_2__);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!mapsLibrary) return;\n    contentContainerRef.current = document.createElement('div');\n    headerContainerRef.current = document.createElement('div');\n    const opts = infoWindowOptions;\n    if (pixelOffset) {\n      opts.pixelOffset = new google.maps.Size(pixelOffset[0], pixelOffset[1]);\n    }\n    if (headerContent) {\n      // if headerContent is specified as string we can directly forward it,\n      // otherwise we'll pass the element the portal will render into\n      opts.headerContent = typeof headerContent === 'string' ? headerContent : headerContainerRef.current;\n    }\n    // intentionally shadowing the state variables here\n    const infoWindow = new google.maps.InfoWindow(infoWindowOptions);\n    infoWindow.setContent(contentContainerRef.current);\n    setInfoWindow(infoWindow);\n    // unmount: remove infoWindow and content elements (note: close is called in a different effect-cleanup)\n    return () => {\n      var _contentContainerRef$, _headerContainerRef$c;\n      infoWindow.setContent(null);\n      (_contentContainerRef$ = contentContainerRef.current) == null || _contentContainerRef$.remove();\n      (_headerContainerRef$c = headerContainerRef.current) == null || _headerContainerRef$c.remove();\n      contentContainerRef.current = null;\n      headerContainerRef.current = null;\n      setInfoWindow(null);\n    };\n  },\n  // `infoWindowOptions` and other props are missing from dependencies:\n  //\n  // We don't want to re-create the infowindow instance\n  // when the options change.\n  // Updating the options is handled in the useEffect below.\n  //\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [mapsLibrary]);\n  // ---- update className and styles for `contentContainer`\n  // prevStyleRef stores previously applied style properties, so they can be\n  // removed when unset\n  const prevStyleRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!infoWindow || !contentContainerRef.current) return;\n    setValueForStyles(contentContainerRef.current, style || null, prevStyleRef.current);\n    prevStyleRef.current = style || null;\n    if (className !== contentContainerRef.current.className) contentContainerRef.current.className = className || '';\n  }, [infoWindow, className, style]);\n  // ---- update options\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!infoWindow) return;\n    const opts = infoWindowOptions;\n    if (!pixelOffset) {\n      opts.pixelOffset = null;\n    } else {\n      opts.pixelOffset = new google.maps.Size(pixelOffset[0], pixelOffset[1]);\n    }\n    if (!headerContent) {\n      opts.headerContent = null;\n    } else {\n      opts.headerContent = typeof headerContent === 'string' ? headerContent : headerContainerRef.current;\n    }\n    infoWindow.setOptions(infoWindowOptions);\n  },\n  // dependency `infoWindow` isn't needed since options are also passed\n  // to the constructor when a new infoWindow is created.\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [infoWindowOptions, pixelOffset, headerContent]);\n  // ## bind event handlers\n  useMapsEventListener(infoWindow, 'close', onClose);\n  useMapsEventListener(infoWindow, 'closeclick', onCloseClick);\n  // ---- open info window when content and map are available\n  const map = useMap();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    // `anchor === null` means an anchor is defined but not ready yet.\n    if (!map || !infoWindow || anchor === null) return;\n    const isOpenedWithAnchor = !!anchor;\n    const openOptions = {\n      map\n    };\n    if (anchor) {\n      openOptions.anchor = anchor;\n      // Only do the infowindow adjusting when dealing with an AdvancedMarker\n      if (isAdvancedMarker(anchor) && anchor.content instanceof Element) {\n        const wrapper = anchor.content;\n        const wrapperBcr = wrapper == null ? void 0 : wrapper.getBoundingClientRect();\n        // This checks whether or not the anchor has custom content with our own\n        // div wrapper. If not, that means we have a regular AdvancedMarker without any children.\n        // In that case we do not want to adjust the infowindow since it is all handled correctly\n        // by the Google Maps API.\n        if (wrapperBcr && wrapper != null && wrapper.isCustomMarker) {\n          var _anchor$content$first;\n          // We can safely typecast here since we control that element and we know that\n          // it is a div\n          const anchorDomContent = (_anchor$content$first = anchor.content.firstElementChild) == null ? void 0 : _anchor$content$first.firstElementChild;\n          const contentBcr = anchorDomContent == null ? void 0 : anchorDomContent.getBoundingClientRect();\n          // center infowindow above marker\n          const anchorOffsetX = contentBcr.x - wrapperBcr.x + (contentBcr.width - wrapperBcr.width) / 2;\n          const anchorOffsetY = contentBcr.y - wrapperBcr.y;\n          const opts = infoWindowOptions;\n          opts.pixelOffset = new google.maps.Size(pixelOffset ? pixelOffset[0] + anchorOffsetX : anchorOffsetX, pixelOffset ? pixelOffset[1] + anchorOffsetY : anchorOffsetY);\n          infoWindow.setOptions(opts);\n        }\n      }\n    }\n    if (shouldFocus !== undefined) {\n      openOptions.shouldFocus = shouldFocus;\n    }\n    infoWindow.open(openOptions);\n    return () => {\n      // Note: when the infowindow has an anchor, it will automatically show up again when the\n      // anchor was removed from the map before infoWindow.close() is called but the it gets\n      // added back to the map after that.\n      // More information here: https://issuetracker.google.com/issues/343750849\n      if (isOpenedWithAnchor) infoWindow.set('anchor', null);\n      infoWindow.close();\n    };\n  }, [infoWindow, anchor, map, shouldFocus, infoWindowOptions, pixelOffset]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, contentContainerRef.current && (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(children, contentContainerRef.current), headerContainerRef.current !== null && (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(headerContent, headerContainerRef.current));\n};\n\n/**\n * Formats a location into a string representation suitable for Google Static Maps API.\n *\n * @param location - The location to format, can be either a string or an object with lat/lng properties\n * @returns A string representation of the location in the format \"lat,lng\" or the original string\n *\n * @example\n * // Returns \"40.714728,-73.998672\"\n * formatLocation({ lat: 40.714728, lng: -73.998672 })\n *\n * @example\n * // Returns \"New York, NY\"\n * formatLocation(\"New York, NY\")\n */\nfunction formatLocation(location) {\n  return typeof location === 'string' ? location : `${location.lat},${location.lng}`;\n}\n// Used for removing the leading pipe from the param string\nfunction formatParam(string) {\n  return string.slice(1);\n}\n\n/**\n * Assembles marker parameters for static maps.\n *\n * This function takes an array of markers and groups them by their style properties.\n * It then creates a string representation of these markers, including their styles and locations,\n * which can be used as parameters for static map APIs.\n *\n * @param {StaticMapsMarker[]} [markers=[]] - An array of markers to be processed. Each marker can have properties such as color, label, size, scale, icon, anchor, and location.\n * @returns {string[]} An array of strings, each representing a group of markers with their styles and locations.\n *\n * @example\n * const markers = [\n *   { color: 'blue', label: 'A', size: 'mid', location: '40.714728,-73.998672' },\n *   { color: 'blue', label: 'B', size: 'mid', location: '40.714728,-73.998672' },\n *   { icon: 'http://example.com/icon.png', location: { lat: 40.714728, lng: -73.998672 } }\n * ];\n * const params = assembleMarkerParams(markers);\n * // Params will be an array of strings representing the marker parameters\n * Example output: [\n *   \"color:blue|label:A|size:mid|40.714728,-73.998672|40.714728,-73.998672\",\n *   \"color:blue|label:B|size:mid|40.714728,-73.998672|40.714728,-73.998672\",\n *   \"icon:http://example.com/icon.png|40.714728,-73.998672\"\n * ]\n */\nfunction assembleMarkerParams(markers = []) {\n  const markerParams = [];\n  // Group markers by style\n  const markersByStyle = markers == null ? void 0 : markers.reduce((styles, marker) => {\n    const {\n      color = 'red',\n      label,\n      size,\n      scale,\n      icon,\n      anchor\n    } = marker;\n    // Create a unique style key based on either icon properties or standard marker properties\n    const relevantProps = icon ? [icon, anchor, scale] : [color, label, size];\n    const key = relevantProps.filter(Boolean).join('-');\n    styles[key] = styles[key] || [];\n    styles[key].push(marker);\n    return styles;\n  }, {});\n  Object.values(markersByStyle != null ? markersByStyle : {}).forEach(markers => {\n    let markerParam = '';\n    const {\n      icon\n    } = markers[0];\n    // Create marker style from first marker in group since all markers share the same style.\n    Object.entries(markers[0]).forEach(([key, value]) => {\n      // Determine which properties to include based on whether marker uses custom icon\n      const relevantKeys = icon ? ['icon', 'anchor', 'scale'] : ['color', 'label', 'size'];\n      if (relevantKeys.includes(key)) {\n        markerParam += `|${key}:${value}`;\n      }\n    });\n    // Add location coordinates for each marker in the style group\n    // Handles both string locations and lat/lng object formats.\n    for (const marker of markers) {\n      const location = typeof marker.location === 'string' ? marker.location : `${marker.location.lat},${marker.location.lng}`;\n      markerParam += `|${location}`;\n    }\n    markerParams.push(markerParam);\n  });\n  return markerParams.map(formatParam);\n}\n\n/**\n * Assembles path parameters for the Static Maps Api from an array of paths.\n *\n * This function groups paths by their style properties (color, weight, fillcolor, geodesic)\n * and then constructs a string of path parameters for each group. Each path parameter string\n * includes the style properties and the coordinates of the paths.\n *\n * @param {Array<StaticMapsPath>} [paths=[]] - An array of paths to be assembled into path parameters.\n * @returns {Array<string>} An array of path parameter strings.\n *\n * @example\n * const paths = [\n *   {\n *     color: 'red',\n *     weight: 5,\n *     coordinates: [\n *       { lat: 40.714728, lng: -73.998672 },\n *       { lat: 40.718217, lng: -73.998284 }\n *     ]\n *   }\n * ];\n *\n * const pathParams = assemblePathParams(paths);\n * Output: [\n *    'color:red|weight:5|40.714728,-73.998672|40.718217,-73.998284'\n *  ]\n */\nfunction assemblePathParams(paths = []) {\n  const pathParams = [];\n  // Group paths by their style properties (color, weight, fillcolor, geodesic)\n  // to combine paths with identical styles into single parameter strings\n  const pathsByStyle = paths == null ? void 0 : paths.reduce((styles, path) => {\n    const {\n      color = 'default',\n      weight,\n      fillcolor,\n      geodesic\n    } = path;\n    // Create unique key for this style combination\n    const key = [color, weight, fillcolor, geodesic].filter(Boolean).join('-');\n    styles[key] = styles[key] || [];\n    styles[key].push(path);\n    return styles;\n  }, {});\n  // Process each group of paths with identical styles\n  Object.values(pathsByStyle != null ? pathsByStyle : {}).forEach(paths => {\n    let pathParam = '';\n    // Build style parameter string using properties from first path in group\n    // since all paths in this group share the same style\n    Object.entries(paths[0]).forEach(([key, value]) => {\n      if (['color', 'weight', 'fillcolor', 'geodesic'].includes(key)) {\n        pathParam += `|${key}:${value}`;\n      }\n    });\n    // Add location for all marker in style group\n    for (const path of paths) {\n      if (typeof path.coordinates === 'string') {\n        pathParam += `|${decodeURIComponent(path.coordinates)}`;\n      } else {\n        for (const location of path.coordinates) {\n          pathParam += `|${formatLocation(location)}`;\n        }\n      }\n    }\n    pathParams.push(pathParam);\n  });\n  return pathParams.map(formatParam);\n}\n\n/**\n * Converts an array of Google Maps style objects into an array of style strings\n * compatible with the Google Static Maps API.\n *\n * @param styles - An array of Google Maps MapTypeStyle objects that define the styling rules\n * @returns An array of formatted style strings ready to be used with the Static Maps API\n *\n * @example\n * const styles = [{\n *   featureType: \"road\",\n *   elementType: \"geometry\",\n *   stylers: [{color: \"#ff0000\"}, {weight: 1}]\n * }];\n *\n * const styleStrings = assembleMapTypeStyles(styles);\n * // Returns: [\"|feature:road|element:geometry|color:0xff0000|weight:1\"]\n *\n * Each style string follows the format:\n * \"feature:{featureType}|element:{elementType}|{stylerName}:{stylerValue}\"\n *\n * Note: Color values with hexadecimal notation (#) are automatically converted\n * to the required 0x format for the Static Maps API.\n */\nfunction assembleMapTypeStyles(styles) {\n  return styles.map(mapTypeStyle => {\n    const {\n      featureType,\n      elementType,\n      stylers = []\n    } = mapTypeStyle;\n    let styleString = '';\n    if (featureType) {\n      styleString += `|feature:${featureType}`;\n    }\n    if (elementType) {\n      styleString += `|element:${elementType}`;\n    }\n    for (const styler of stylers) {\n      Object.entries(styler).forEach(([name, value]) => {\n        styleString += `|${name}:${String(value).replace('#', '0x')}`;\n      });\n    }\n    return styleString;\n  }).map(formatParam);\n}\n\nconst STATIC_MAPS_BASE = 'https://maps.googleapis.com/maps/api/staticmap';\n/**\n * Creates a URL for the Google Static Maps API with the specified parameters.\n *\n * @param {Object} options - The configuration options for the static map\n * @param {string} options.apiKey - Your Google Maps API key (required)\n * @param {number} options.width - The width of the map image in pixels (required)\n * @param {number} options.height - The height of the map image in pixels (required)\n * @param {StaticMapsLocation} [options.center] - The center point of the map (lat/lng or address).\n *  Required if no markers or paths or \"visible locations\" are provided.\n * @param {number} [options.zoom] - The zoom level of the map. Required if no markers or paths or \"visible locations\" are provided.\n * @param {1|2|4} [options.scale] - The resolution of the map (1, 2, or 4)\n * @param {string} [options.format] - The image format (png, png8, png32, gif, jpg, jpg-baseline)\n * @param {string} [options.mapType] - The type of map (roadmap, satellite, terrain, hybrid)\n * @param {string} [options.language] - The language of the map labels\n * @param {string} [options.region] - The region code for the map\n * @param {string} [options.map_id] - The Cloud-based map style ID\n * @param {StaticMapsMarker[]} [options.markers=[]] - Array of markers to display on the map\n * @param {StaticMapsPath[]} [options.paths=[]] - Array of paths to display on the map\n * @param {StaticMapsLocation[]} [options.visible=[]] - Array of locations that should be visible on the map\n * @param {MapTypeStyle[]} [options.style=[]] - Array of style objects to customize the map appearance\n *\n * @returns {string} The complete Google Static Maps API URL\n *\n * @throws {Error} If API key is not provided\n * @throws {Error} If width or height is not provided\n *\n * @example\n * const url = createStaticMapsUrl({\n *   apiKey: 'YOUR_API_KEY',\n *   width: 600,\n *   height: 400,\n *   center: { lat: 40.714728, lng: -73.998672 },\n *   zoom: 12,\n *   markers: [\n *     {\n *       location: { lat: 40.714728, lng: -73.998672 },\n *       color: 'red',\n *       label: 'A'\n *     }\n *   ],\n *   paths: [\n *     {\n *       coordinates: [\n *         { lat: 40.714728, lng: -73.998672 },\n *         { lat: 40.719728, lng: -73.991672 }\n *       ],\n *       color: '0x0000ff',\n *       weight: 5\n *     }\n *   ],\n *   style: [\n *     {\n *       featureType: 'road',\n *       elementType: 'geometry',\n *       stylers: [{color: '#00ff00'}]\n *     }\n *   ]\n * });\n *\n * // Results in URL similar to:\n * // https://maps.googleapis.com/maps/api/staticmap?key=YOUR_API_KEY\n * // &size=600x400\n * // &center=40.714728,-73.998672&zoom=12\n * // &markers=color:red|label:A|40.714728,-73.998672\n * // &path=color:0x0000ff|weight:5|40.714728,-73.998672|40.719728,-73.991672\n * // &style=feature:road|element:geometry|color:0x00ff00\n */\nfunction createStaticMapsUrl({\n  apiKey,\n  width,\n  height,\n  center,\n  zoom,\n  scale,\n  format,\n  mapType,\n  language,\n  region,\n  mapId,\n  markers = [],\n  paths = [],\n  visible = [],\n  style = []\n}) {\n  if (!apiKey) {\n    console.warn('API key is required');\n  }\n  if (!width || !height) {\n    console.warn('Width and height are required');\n  }\n  const params = _extends({\n    key: apiKey,\n    size: `${width}x${height}`\n  }, center && {\n    center: formatLocation(center)\n  }, zoom && {\n    zoom\n  }, scale && {\n    scale\n  }, format && {\n    format\n  }, mapType && {\n    maptype: mapType\n  }, language && {\n    language\n  }, region && {\n    region\n  }, mapId && {\n    map_id: mapId\n  });\n  const url = new URL(STATIC_MAPS_BASE);\n  // Params that don't need special handling\n  Object.entries(params).forEach(([key, value]) => {\n    url.searchParams.append(key, String(value));\n  });\n  // Assemble Markers\n  for (const markerParam of assembleMarkerParams(markers)) {\n    url.searchParams.append('markers', markerParam);\n  }\n  // Assemble Paths\n  for (const pathParam of assemblePathParams(paths)) {\n    url.searchParams.append('path', pathParam);\n  }\n  // Assemble visible locations\n  if (visible.length) {\n    url.searchParams.append('visible', visible.map(location => formatLocation(location)).join('|'));\n  }\n  // Assemble Map Type Styles\n  for (const styleString of assembleMapTypeStyles(style)) {\n    url.searchParams.append('style', styleString);\n  }\n  return url.toString();\n}\n\nconst StaticMap = props => {\n  const {\n    url,\n    className\n  } = props;\n  if (!url) throw new Error('URL is required');\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"img\", {\n    className: className,\n    src: url,\n    width: \"100%\"\n  });\n};\n\n/**\n * Copy of the `google.maps.ControlPosition` constants.\n * They have to be duplicated here since we can't wait for the maps API to load to be able to use them.\n */\nconst ControlPosition = {\n  TOP_LEFT: 1,\n  TOP_CENTER: 2,\n  TOP: 2,\n  TOP_RIGHT: 3,\n  LEFT_CENTER: 4,\n  LEFT_TOP: 5,\n  LEFT: 5,\n  LEFT_BOTTOM: 6,\n  RIGHT_TOP: 7,\n  RIGHT: 7,\n  RIGHT_CENTER: 8,\n  RIGHT_BOTTOM: 9,\n  BOTTOM_LEFT: 10,\n  BOTTOM_CENTER: 11,\n  BOTTOM: 11,\n  BOTTOM_RIGHT: 12,\n  CENTER: 13,\n  BLOCK_START_INLINE_START: 14,\n  BLOCK_START_INLINE_CENTER: 15,\n  BLOCK_START_INLINE_END: 16,\n  INLINE_START_BLOCK_CENTER: 17,\n  INLINE_START_BLOCK_START: 18,\n  INLINE_START_BLOCK_END: 19,\n  INLINE_END_BLOCK_START: 20,\n  INLINE_END_BLOCK_CENTER: 21,\n  INLINE_END_BLOCK_END: 22,\n  BLOCK_END_INLINE_START: 23,\n  BLOCK_END_INLINE_CENTER: 24,\n  BLOCK_END_INLINE_END: 25\n};\nconst MapControl = ({\n  children,\n  position\n}) => {\n  const controlContainer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => document.createElement('div'), []);\n  const map = useMap();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!map) return;\n    const controls = map.controls[position];\n    controls.push(controlContainer);\n    return () => {\n      const controlsArray = controls.getArray();\n      // controlsArray could be undefined if the map is in an undefined state (e.g. invalid API-key, see #276\n      if (!controlsArray) return;\n      const index = controlsArray.indexOf(controlContainer);\n      controls.removeAt(index);\n    };\n  }, [controlContainer, map, position]);\n  return (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(children, controlContainer);\n};\n\nconst _excluded = [\"onClick\", \"onDrag\", \"onDragStart\", \"onDragEnd\", \"onMouseOver\", \"onMouseOut\"];\nfunction useMarker(props) {\n  const [marker, setMarker] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const map = useMap();\n  const {\n      onClick,\n      onDrag,\n      onDragStart,\n      onDragEnd,\n      onMouseOver,\n      onMouseOut\n    } = props,\n    markerOptions = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    position,\n    draggable\n  } = markerOptions;\n  // create marker instance and add to the map once the map is available\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!map) {\n      if (map === undefined) console.error('<Marker> has to be inside a Map component.');\n      return;\n    }\n    const newMarker = new google.maps.Marker(markerOptions);\n    newMarker.setMap(map);\n    setMarker(newMarker);\n    return () => {\n      newMarker.setMap(null);\n      setMarker(null);\n    };\n    // We do not want to re-render the whole marker when the options change.\n    // Marker options update is handled in a useEffect below.\n    // Excluding markerOptions from dependency array on purpose here.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [map]);\n  // attach and re-attach event-handlers when any of the properties change\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!marker) return;\n    const m = marker;\n    // Add event listeners\n    const gme = google.maps.event;\n    if (onClick) gme.addListener(m, 'click', onClick);\n    if (onDrag) gme.addListener(m, 'drag', onDrag);\n    if (onDragStart) gme.addListener(m, 'dragstart', onDragStart);\n    if (onDragEnd) gme.addListener(m, 'dragend', onDragEnd);\n    if (onMouseOver) gme.addListener(m, 'mouseover', onMouseOver);\n    if (onMouseOut) gme.addListener(m, 'mouseout', onMouseOut);\n    marker.setDraggable(Boolean(draggable));\n    return () => {\n      gme.clearInstanceListeners(m);\n    };\n  }, [marker, draggable, onClick, onDrag, onDragStart, onDragEnd, onMouseOver, onMouseOut]);\n  // update markerOptions (note the dependencies aren't properly checked\n  // here, we just assume that setOptions is smart enough to not waste a\n  // lot of time updating values that didn't change)\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!marker) return;\n    if (markerOptions) marker.setOptions(markerOptions);\n  }, [marker, markerOptions]);\n  // update position when changed\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    // Should not update position when draggable\n    if (draggable || !position || !marker) return;\n    marker.setPosition(position);\n  }, [draggable, position, marker]);\n  return marker;\n}\n/**\n * Component to render a marker on a map\n */\nconst Marker = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => {\n  const marker = useMarker(props);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => marker, [marker]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null);\n});\nfunction useMarkerRef() {\n  const [marker, setMarker] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const refCallback = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(m => {\n    setMarker(m);\n  }, []);\n  return [refCallback, marker];\n}\n\n/**\n * Component to configure the appearance of an AdvancedMarker\n */\nconst Pin = props => {\n  var _useContext;\n  const advancedMarker = (_useContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(AdvancedMarkerContext)) == null ? void 0 : _useContext.marker;\n  const glyphContainer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => document.createElement('div'), []);\n  // Create Pin View instance\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    var _advancedMarker$conte;\n    if (!advancedMarker) {\n      if (advancedMarker === undefined) {\n        console.error('The <Pin> component can only be used inside <AdvancedMarker>.');\n      }\n      return;\n    }\n    if (props.glyph && props.children) {\n      logErrorOnce('The <Pin> component only uses children to render the glyph if both the glyph property and children are present.');\n    }\n    if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(props.children) > 1) {\n      logErrorOnce('Passing multiple children to the <Pin> component might lead to unexpected results.');\n    }\n    const pinViewOptions = _extends({}, props);\n    const pinElement = new google.maps.marker.PinElement(pinViewOptions);\n    // Set glyph to glyph container if children are present (rendered via portal).\n    // If both props.glyph and props.children are present, props.children takes priority.\n    if (props.children) {\n      pinElement.glyph = glyphContainer;\n    }\n    // Set content of Advanced Marker View to the Pin View element\n    // Here we are selecting the anchor container.\n    // The hierarchy is as follows:\n    // \"advancedMarker.content\" (from google) -> \"pointer events reset div\" -> \"anchor container\"\n    const markerContent = (_advancedMarker$conte = advancedMarker.content) == null || (_advancedMarker$conte = _advancedMarker$conte.firstChild) == null ? void 0 : _advancedMarker$conte.firstChild;\n    while (markerContent != null && markerContent.firstChild) {\n      markerContent.removeChild(markerContent.firstChild);\n    }\n    if (markerContent) {\n      markerContent.appendChild(pinElement.element);\n    }\n  }, [advancedMarker, glyphContainer, props]);\n  return (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(props.children, glyphContainer);\n};\n\nconst mapLinear = (x, a1, a2, b1, b2) => b1 + (x - a1) * (b2 - b1) / (a2 - a1);\nconst getMapMaxTilt = zoom => {\n  if (zoom <= 10) {\n    return 30;\n  }\n  if (zoom >= 15.5) {\n    return 67.5;\n  }\n  // range [10...14]\n  if (zoom <= 14) {\n    return mapLinear(zoom, 10, 14, 30, 45);\n  }\n  // range [14...15.5]\n  return mapLinear(zoom, 14, 15.5, 45, 67.5);\n};\n/**\n * Function to limit the tilt range of the Google map when updating the view state\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst limitTiltRange = ({\n  viewState\n}) => {\n  const pitch = viewState.pitch;\n  const gmZoom = viewState.zoom + 1;\n  const maxTilt = getMapMaxTilt(gmZoom);\n  return _extends({}, viewState, {\n    fovy: 25,\n    pitch: Math.min(maxTilt, pitch)\n  });\n};\n\n\n//# sourceMappingURL=index.modern.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@vis.gl/react-google-maps/dist/index.modern.mjs\n");

/***/ })

};
;
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/xstate";
exports.ids = ["vendor-chunks/xstate"];
exports.modules = {

/***/ "(ssr)/./node_modules/xstate/actors/dist/xstate-actors.development.esm.js":
/*!**************************************************************************!*\
  !*** ./node_modules/xstate/actors/dist/xstate-actors.development.esm.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEmptyActor: () => (/* binding */ createEmptyActor),\n/* harmony export */   fromCallback: () => (/* binding */ fromCallback),\n/* harmony export */   fromEventObservable: () => (/* binding */ fromEventObservable),\n/* harmony export */   fromObservable: () => (/* binding */ fromObservable),\n/* harmony export */   fromPromise: () => (/* binding */ fromPromise),\n/* harmony export */   fromTransition: () => (/* binding */ fromTransition)\n/* harmony export */ });\n/* harmony import */ var _dist_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../dist/raise-78b8dcb8.development.esm.js */ \"(ssr)/./node_modules/xstate/dist/raise-78b8dcb8.development.esm.js\");\n/* harmony import */ var _dev_dist_xstate_dev_development_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../dev/dist/xstate-dev.development.esm.js */ \"(ssr)/./node_modules/xstate/dev/dist/xstate-dev.development.esm.js\");\n\n\n\n/**\n * Represents an actor created by `fromTransition`.\n *\n * The type of `self` within the actor's logic.\n *\n * @example\n *\n * ```ts\n * import {\n *   fromTransition,\n *   createActor,\n *   type AnyActorSystem\n * } from 'xstate';\n *\n * //* The actor's stored context.\n * type Context = {\n *   // The current count.\n *   count: number;\n *   // The amount to increase `count` by.\n *   step: number;\n * };\n * // The events the actor receives.\n * type Event = { type: 'increment' };\n * // The actor's input.\n * type Input = { step?: number };\n *\n * // Actor logic that increments `count` by `step` when it receives an event of\n * // type `increment`.\n * const logic = fromTransition<Context, Event, AnyActorSystem, Input>(\n *   (state, event, actorScope) => {\n *     actorScope.self;\n *     //         ^? TransitionActorRef<Context, Event>\n *\n *     if (event.type === 'increment') {\n *       return {\n *         ...state,\n *         count: state.count + state.step\n *       };\n *     }\n *     return state;\n *   },\n *   ({ input, self }) => {\n *     self;\n *     // ^? TransitionActorRef<Context, Event>\n *\n *     return {\n *       count: 0,\n *       step: input.step ?? 1\n *     };\n *   }\n * );\n *\n * const actor = createActor(logic, { input: { step: 10 } });\n * //    ^? TransitionActorRef<Context, Event>\n * ```\n *\n * @see {@link fromTransition}\n */\n\n/**\n * Returns actor logic given a transition function and its initial state.\n *\n * A “transition function” is a function that takes the current `state` and\n * received `event` object as arguments, and returns the next state, similar to\n * a reducer.\n *\n * Actors created from transition logic (“transition actors”) can:\n *\n * - Receive events\n * - Emit snapshots of its state\n *\n * The transition function’s `state` is used as its transition actor’s\n * `context`.\n *\n * Note that the \"state\" for a transition function is provided by the initial\n * state argument, and is not the same as the State object of an actor or a\n * state within a machine configuration.\n *\n * @example\n *\n * ```ts\n * const transitionLogic = fromTransition(\n *   (state, event) => {\n *     if (event.type === 'increment') {\n *       return {\n *         ...state,\n *         count: state.count + 1\n *       };\n *     }\n *     return state;\n *   },\n *   { count: 0 }\n * );\n *\n * const transitionActor = createActor(transitionLogic);\n * transitionActor.subscribe((snapshot) => {\n *   console.log(snapshot);\n * });\n * transitionActor.start();\n * // => {\n * //   status: 'active',\n * //   context: { count: 0 },\n * //   ...\n * // }\n *\n * transitionActor.send({ type: 'increment' });\n * // => {\n * //   status: 'active',\n * //   context: { count: 1 },\n * //   ...\n * // }\n * ```\n *\n * @param transition The transition function used to describe the transition\n *   logic. It should return the next state given the current state and event.\n *   It receives the following arguments:\n *\n *   - `state` - the current state.\n *   - `event` - the received event.\n *   - `actorScope` - the actor scope object, with properties like `self` and\n *       `system`.\n *\n * @param initialContext The initial state of the transition function, either an\n *   object representing the state, or a function which returns a state object.\n *   If a function, it will receive as its only argument an object with the\n *   following properties:\n *\n *   - `input` - the `input` provided to its parent transition actor.\n *   - `self` - a reference to its parent transition actor.\n *\n * @returns Actor logic\n * @see {@link https://stately.ai/docs/input | Input docs} for more information about how input is passed\n */\nfunction fromTransition(transition, initialContext) {\n  return {\n    config: transition,\n    transition: (snapshot, event, actorScope) => {\n      return {\n        ...snapshot,\n        context: transition(snapshot.context, event, actorScope)\n      };\n    },\n    getInitialSnapshot: (_, input) => {\n      return {\n        status: 'active',\n        output: undefined,\n        error: undefined,\n        context: typeof initialContext === 'function' ? initialContext({\n          input\n        }) : initialContext\n      };\n    },\n    getPersistedSnapshot: snapshot => snapshot,\n    restoreSnapshot: snapshot => snapshot\n  };\n}\n\nconst instanceStates = /* #__PURE__ */new WeakMap();\n\n/**\n * Represents an actor created by `fromCallback`.\n *\n * The type of `self` within the actor's logic.\n *\n * @example\n *\n * ```ts\n * import { fromCallback, createActor } from 'xstate';\n *\n * // The events the actor receives.\n * type Event = { type: 'someEvent' };\n * // The actor's input.\n * type Input = { name: string };\n *\n * // Actor logic that logs whenever it receives an event of type `someEvent`.\n * const logic = fromCallback<Event, Input>(({ self, input, receive }) => {\n *   self;\n *   // ^? CallbackActorRef<Event, Input>\n *\n *   receive((event) => {\n *     if (event.type === 'someEvent') {\n *       console.log(`${input.name}: received \"someEvent\" event`);\n *       // logs 'myActor: received \"someEvent\" event'\n *     }\n *   });\n * });\n *\n * const actor = createActor(logic, { input: { name: 'myActor' } });\n * //    ^? CallbackActorRef<Event, Input>\n * ```\n *\n * @see {@link fromCallback}\n */\n\n/**\n * An actor logic creator which returns callback logic as defined by a callback\n * function.\n *\n * @remarks\n * Useful for subscription-based or other free-form logic that can send events\n * back to the parent actor.\n *\n * Actors created from callback logic (“callback actors”) can:\n *\n * - Receive events via the `receive` function\n * - Send events to the parent actor via the `sendBack` function\n *\n * Callback actors are a bit different from other actors in that they:\n *\n * - Do not work with `onDone`\n * - Do not produce a snapshot using `.getSnapshot()`\n * - Do not emit values when used with `.subscribe()`\n * - Can not be stopped with `.stop()`\n *\n * @example\n *\n * ```typescript\n * const callbackLogic = fromCallback(({ sendBack, receive }) => {\n *   let lockStatus = 'unlocked';\n *\n *   const handler = (event) => {\n *     if (lockStatus === 'locked') {\n *       return;\n *     }\n *     sendBack(event);\n *   };\n *\n *   receive((event) => {\n *     if (event.type === 'lock') {\n *       lockStatus = 'locked';\n *     } else if (event.type === 'unlock') {\n *       lockStatus = 'unlocked';\n *     }\n *   });\n *\n *   document.body.addEventListener('click', handler);\n *\n *   return () => {\n *     document.body.removeEventListener('click', handler);\n *   };\n * });\n * ```\n *\n * @param callback - The callback function used to describe the callback logic\n *   The callback function is passed an object with the following properties:\n *\n *   - `receive` - A function that can send events back to the parent actor; the\n *       listener is then called whenever events are received by the callback\n *       actor\n *   - `sendBack` - A function that can send events back to the parent actor\n *   - `input` - Data that was provided to the callback actor\n *   - `self` - The parent actor of the callback actor\n *   - `system` - The actor system to which the callback actor belongs The callback\n *       function can (optionally) return a cleanup function, which is called\n *       when the actor is stopped.\n *\n * @returns Callback logic\n * @see {@link CallbackLogicFunction} for more information about the callback function and its object argument\n * @see {@link https://stately.ai/docs/input | Input docs} for more information about how input is passed\n */\nfunction fromCallback(callback) {\n  const logic = {\n    config: callback,\n    start: (state, actorScope) => {\n      const {\n        self,\n        system,\n        emit\n      } = actorScope;\n      const callbackState = {\n        receivers: undefined,\n        dispose: undefined\n      };\n      instanceStates.set(self, callbackState);\n      callbackState.dispose = callback({\n        input: state.input,\n        system,\n        self,\n        sendBack: event => {\n          if (self.getSnapshot().status === 'stopped') {\n            return;\n          }\n          if (self._parent) {\n            system._relay(self, self._parent, event);\n          }\n        },\n        receive: listener => {\n          callbackState.receivers ??= new Set();\n          callbackState.receivers.add(listener);\n        },\n        emit\n      });\n    },\n    transition: (state, event, actorScope) => {\n      const callbackState = instanceStates.get(actorScope.self);\n      if (event.type === _dist_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.X) {\n        state = {\n          ...state,\n          status: 'stopped',\n          error: undefined\n        };\n        callbackState.dispose?.();\n        return state;\n      }\n      callbackState.receivers?.forEach(receiver => receiver(event));\n      return state;\n    },\n    getInitialSnapshot: (_, input) => {\n      return {\n        status: 'active',\n        output: undefined,\n        error: undefined,\n        input\n      };\n    },\n    getPersistedSnapshot: snapshot => snapshot,\n    restoreSnapshot: snapshot => snapshot\n  };\n  return logic;\n}\n\nconst XSTATE_OBSERVABLE_NEXT = 'xstate.observable.next';\nconst XSTATE_OBSERVABLE_ERROR = 'xstate.observable.error';\nconst XSTATE_OBSERVABLE_COMPLETE = 'xstate.observable.complete';\n\n/**\n * Represents an actor created by `fromObservable` or `fromEventObservable`.\n *\n * The type of `self` within the actor's logic.\n *\n * @example\n *\n * ```ts\n * import { fromObservable, createActor } from 'xstate';\n * import { interval } from 'rxjs';\n *\n * // The type of the value observed by the actor's logic.\n * type Context = number;\n * // The actor's input.\n * type Input = { period?: number };\n *\n * // Actor logic that observes a number incremented every `input.period`\n * // milliseconds (default: 1_000).\n * const logic = fromObservable<Context, Input>(({ input, self }) => {\n *   self;\n *   // ^? ObservableActorRef<Event, Input>\n *\n *   return interval(input.period ?? 1_000);\n * });\n *\n * const actor = createActor(logic, { input: { period: 2_000 } });\n * //    ^? ObservableActorRef<Event, Input>\n * ```\n *\n * @see {@link fromObservable}\n * @see {@link fromEventObservable}\n */\n\n/**\n * Observable actor logic is described by an observable stream of values. Actors\n * created from observable logic (“observable actors”) can:\n *\n * - Emit snapshots of the observable’s emitted value\n *\n * The observable’s emitted value is used as its observable actor’s `context`.\n *\n * Sending events to observable actors will have no effect.\n *\n * @example\n *\n * ```ts\n * import { fromObservable, createActor } from 'xstate';\n * import { interval } from 'rxjs';\n *\n * const logic = fromObservable((obj) => interval(1000));\n *\n * const actor = createActor(logic);\n *\n * actor.subscribe((snapshot) => {\n *   console.log(snapshot.context);\n * });\n *\n * actor.start();\n * // At every second:\n * // Logs 0\n * // Logs 1\n * // Logs 2\n * // ...\n * ```\n *\n * @param observableCreator A function that creates an observable. It receives\n *   one argument, an object with the following properties:\n *\n *   - `input` - Data that was provided to the observable actor\n *   - `self` - The parent actor\n *   - `system` - The actor system to which the observable actor belongs\n *\n *   It should return a {@link Subscribable}, which is compatible with an RxJS\n *   Observable, although RxJS is not required to create them.\n * @see {@link https://rxjs.dev} for documentation on RxJS Observable and observable creators.\n * @see {@link Subscribable} interface in XState, which is based on and compatible with RxJS Observable.\n */\nfunction fromObservable(observableCreator) {\n  // TODO: add event types\n  const logic = {\n    config: observableCreator,\n    transition: (snapshot, event) => {\n      if (snapshot.status !== 'active') {\n        return snapshot;\n      }\n      switch (event.type) {\n        case XSTATE_OBSERVABLE_NEXT:\n          {\n            const newSnapshot = {\n              ...snapshot,\n              context: event.data\n            };\n            return newSnapshot;\n          }\n        case XSTATE_OBSERVABLE_ERROR:\n          return {\n            ...snapshot,\n            status: 'error',\n            error: event.data,\n            input: undefined,\n            _subscription: undefined\n          };\n        case XSTATE_OBSERVABLE_COMPLETE:\n          return {\n            ...snapshot,\n            status: 'done',\n            input: undefined,\n            _subscription: undefined\n          };\n        case _dist_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.X:\n          snapshot._subscription.unsubscribe();\n          return {\n            ...snapshot,\n            status: 'stopped',\n            input: undefined,\n            _subscription: undefined\n          };\n        default:\n          return snapshot;\n      }\n    },\n    getInitialSnapshot: (_, input) => {\n      return {\n        status: 'active',\n        output: undefined,\n        error: undefined,\n        context: undefined,\n        input,\n        _subscription: undefined\n      };\n    },\n    start: (state, {\n      self,\n      system,\n      emit\n    }) => {\n      if (state.status === 'done') {\n        // Do not restart a completed observable\n        return;\n      }\n      state._subscription = observableCreator({\n        input: state.input,\n        system,\n        self,\n        emit\n      }).subscribe({\n        next: value => {\n          system._relay(self, self, {\n            type: XSTATE_OBSERVABLE_NEXT,\n            data: value\n          });\n        },\n        error: err => {\n          system._relay(self, self, {\n            type: XSTATE_OBSERVABLE_ERROR,\n            data: err\n          });\n        },\n        complete: () => {\n          system._relay(self, self, {\n            type: XSTATE_OBSERVABLE_COMPLETE\n          });\n        }\n      });\n    },\n    getPersistedSnapshot: ({\n      _subscription,\n      ...state\n    }) => state,\n    restoreSnapshot: state => ({\n      ...state,\n      _subscription: undefined\n    })\n  };\n  return logic;\n}\n\n/**\n * Creates event observable logic that listens to an observable that delivers\n * event objects.\n *\n * Event observable actor logic is described by an observable stream of\n * {@link https://stately.ai/docs/transitions#event-objects | event objects}.\n * Actors created from event observable logic (“event observable actors”) can:\n *\n * - Implicitly send events to its parent actor\n * - Emit snapshots of its emitted event objects\n *\n * Sending events to event observable actors will have no effect.\n *\n * @example\n *\n * ```ts\n * import {\n *   fromEventObservable,\n *   Subscribable,\n *   EventObject,\n *   createMachine,\n *   createActor\n * } from 'xstate';\n * import { fromEvent } from 'rxjs';\n *\n * const mouseClickLogic = fromEventObservable(\n *   () => fromEvent(document.body, 'click') as Subscribable<EventObject>\n * );\n *\n * const canvasMachine = createMachine({\n *   invoke: {\n *     // Will send mouse `click` events to the canvas actor\n *     src: mouseClickLogic\n *   }\n * });\n *\n * const canvasActor = createActor(canvasMachine);\n * canvasActor.start();\n * ```\n *\n * @param lazyObservable A function that creates an observable that delivers\n *   event objects. It receives one argument, an object with the following\n *   properties:\n *\n *   - `input` - Data that was provided to the event observable actor\n *   - `self` - The parent actor\n *   - `system` - The actor system to which the event observable actor belongs.\n *\n *   It should return a {@link Subscribable}, which is compatible with an RxJS\n *   Observable, although RxJS is not required to create them.\n */\nfunction fromEventObservable(lazyObservable) {\n  // TODO: event types\n  const logic = {\n    config: lazyObservable,\n    transition: (state, event) => {\n      if (state.status !== 'active') {\n        return state;\n      }\n      switch (event.type) {\n        case XSTATE_OBSERVABLE_ERROR:\n          return {\n            ...state,\n            status: 'error',\n            error: event.data,\n            input: undefined,\n            _subscription: undefined\n          };\n        case XSTATE_OBSERVABLE_COMPLETE:\n          return {\n            ...state,\n            status: 'done',\n            input: undefined,\n            _subscription: undefined\n          };\n        case _dist_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.X:\n          state._subscription.unsubscribe();\n          return {\n            ...state,\n            status: 'stopped',\n            input: undefined,\n            _subscription: undefined\n          };\n        default:\n          return state;\n      }\n    },\n    getInitialSnapshot: (_, input) => {\n      return {\n        status: 'active',\n        output: undefined,\n        error: undefined,\n        context: undefined,\n        input,\n        _subscription: undefined\n      };\n    },\n    start: (state, {\n      self,\n      system,\n      emit\n    }) => {\n      if (state.status === 'done') {\n        // Do not restart a completed observable\n        return;\n      }\n      state._subscription = lazyObservable({\n        input: state.input,\n        system,\n        self,\n        emit\n      }).subscribe({\n        next: value => {\n          if (self._parent) {\n            system._relay(self, self._parent, value);\n          }\n        },\n        error: err => {\n          system._relay(self, self, {\n            type: XSTATE_OBSERVABLE_ERROR,\n            data: err\n          });\n        },\n        complete: () => {\n          system._relay(self, self, {\n            type: XSTATE_OBSERVABLE_COMPLETE\n          });\n        }\n      });\n    },\n    getPersistedSnapshot: ({\n      _subscription,\n      ...snapshot\n    }) => snapshot,\n    restoreSnapshot: snapshot => ({\n      ...snapshot,\n      _subscription: undefined\n    })\n  };\n  return logic;\n}\n\nconst XSTATE_PROMISE_RESOLVE = 'xstate.promise.resolve';\nconst XSTATE_PROMISE_REJECT = 'xstate.promise.reject';\n\n/**\n * Represents an actor created by `fromPromise`.\n *\n * The type of `self` within the actor's logic.\n *\n * @example\n *\n * ```ts\n * import { fromPromise, createActor } from 'xstate';\n *\n * // The actor's resolved output\n * type Output = string;\n * // The actor's input.\n * type Input = { message: string };\n *\n * // Actor logic that fetches the url of an image of a cat saying `input.message`.\n * const logic = fromPromise<Output, Input>(async ({ input, self }) => {\n *   self;\n *   // ^? PromiseActorRef<Output, Input>\n *\n *   const data = await fetch(\n *     `https://cataas.com/cat/says/${input.message}`\n *   );\n *   const url = await data.json();\n *   return url;\n * });\n *\n * const actor = createActor(logic, { input: { message: 'hello world' } });\n * //    ^? PromiseActorRef<Output, Input>\n * ```\n *\n * @see {@link fromPromise}\n */\n\nconst controllerMap = new WeakMap();\n\n/**\n * An actor logic creator which returns promise logic as defined by an async\n * process that resolves or rejects after some time.\n *\n * Actors created from promise actor logic (“promise actors”) can:\n *\n * - Emit the resolved value of the promise\n * - Output the resolved value of the promise\n *\n * Sending events to promise actors will have no effect.\n *\n * @example\n *\n * ```ts\n * const promiseLogic = fromPromise(async () => {\n *   const result = await fetch('https://example.com/...').then((data) =>\n *     data.json()\n *   );\n *\n *   return result;\n * });\n *\n * const promiseActor = createActor(promiseLogic);\n * promiseActor.subscribe((snapshot) => {\n *   console.log(snapshot);\n * });\n * promiseActor.start();\n * // => {\n * //   output: undefined,\n * //   status: 'active'\n * //   ...\n * // }\n *\n * // After promise resolves\n * // => {\n * //   output: { ... },\n * //   status: 'done',\n * //   ...\n * // }\n * ```\n *\n * @param promiseCreator A function which returns a Promise, and accepts an\n *   object with the following properties:\n *\n *   - `input` - Data that was provided to the promise actor\n *   - `self` - The parent actor of the promise actor\n *   - `system` - The actor system to which the promise actor belongs\n *\n * @see {@link https://stately.ai/docs/input | Input docs} for more information about how input is passed\n */\nfunction fromPromise(promiseCreator) {\n  const logic = {\n    config: promiseCreator,\n    transition: (state, event, scope) => {\n      if (state.status !== 'active') {\n        return state;\n      }\n      switch (event.type) {\n        case XSTATE_PROMISE_RESOLVE:\n          {\n            const resolvedValue = event.data;\n            return {\n              ...state,\n              status: 'done',\n              output: resolvedValue,\n              input: undefined\n            };\n          }\n        case XSTATE_PROMISE_REJECT:\n          return {\n            ...state,\n            status: 'error',\n            error: event.data,\n            input: undefined\n          };\n        case _dist_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.X:\n          {\n            controllerMap.get(scope.self)?.abort();\n            return {\n              ...state,\n              status: 'stopped',\n              input: undefined\n            };\n          }\n        default:\n          return state;\n      }\n    },\n    start: (state, {\n      self,\n      system,\n      emit\n    }) => {\n      // TODO: determine how to allow customizing this so that promises\n      // can be restarted if necessary\n      if (state.status !== 'active') {\n        return;\n      }\n      const controller = new AbortController();\n      controllerMap.set(self, controller);\n      const resolvedPromise = Promise.resolve(promiseCreator({\n        input: state.input,\n        system,\n        self,\n        signal: controller.signal,\n        emit\n      }));\n      resolvedPromise.then(response => {\n        if (self.getSnapshot().status !== 'active') {\n          return;\n        }\n        controllerMap.delete(self);\n        system._relay(self, self, {\n          type: XSTATE_PROMISE_RESOLVE,\n          data: response\n        });\n      }, errorData => {\n        if (self.getSnapshot().status !== 'active') {\n          return;\n        }\n        controllerMap.delete(self);\n        system._relay(self, self, {\n          type: XSTATE_PROMISE_REJECT,\n          data: errorData\n        });\n      });\n    },\n    getInitialSnapshot: (_, input) => {\n      return {\n        status: 'active',\n        output: undefined,\n        error: undefined,\n        input\n      };\n    },\n    getPersistedSnapshot: snapshot => snapshot,\n    restoreSnapshot: snapshot => snapshot\n  };\n  return logic;\n}\n\nconst emptyLogic = fromTransition(_ => undefined, undefined);\nfunction createEmptyActor() {\n  return (0,_dist_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.c)(emptyLogic);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xstate/actors/dist/xstate-actors.development.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xstate/dev/dist/xstate-dev.development.esm.js":
/*!********************************************************************!*\
  !*** ./node_modules/xstate/dev/dist/xstate-dev.development.esm.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   devToolsAdapter: () => (/* binding */ devToolsAdapter),\n/* harmony export */   getGlobal: () => (/* binding */ getGlobal),\n/* harmony export */   registerService: () => (/* binding */ registerService)\n/* harmony export */ });\n// From https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/globalThis\nfunction getGlobal() {\n  if (typeof globalThis !== 'undefined') {\n    return globalThis;\n  }\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  {\n    console.warn('XState could not find a global object in this environment. Please let the maintainers know and raise an issue here: https://github.com/statelyai/xstate/issues');\n  }\n}\nfunction getDevTools() {\n  const w = getGlobal();\n  if (w.__xstate__) {\n    return w.__xstate__;\n  }\n  return undefined;\n}\nfunction registerService(service) {\n  if (typeof window === 'undefined') {\n    return;\n  }\n  const devTools = getDevTools();\n  if (devTools) {\n    devTools.register(service);\n  }\n}\nconst devToolsAdapter = service => {\n  if (typeof window === 'undefined') {\n    return;\n  }\n  const devTools = getDevTools();\n  if (devTools) {\n    devTools.register(service);\n  }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xstate/dev/dist/xstate-dev.development.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xstate/dist/StateMachine-b4e94439.development.esm.js":
/*!***************************************************************************!*\
  !*** ./node_modules/xstate/dist/StateMachine-b4e94439.development.esm.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   S: () => (/* binding */ StateMachine),\n/* harmony export */   a: () => (/* binding */ StateNode)\n/* harmony export */ });\n/* harmony import */ var _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./raise-78b8dcb8.development.esm.js */ \"(ssr)/./node_modules/xstate/dist/raise-78b8dcb8.development.esm.js\");\n/* harmony import */ var _assign_6313ccb3_development_esm_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./assign-6313ccb3.development.esm.js */ \"(ssr)/./node_modules/xstate/dist/assign-6313ccb3.development.esm.js\");\n\n\n\nconst cache = new WeakMap();\nfunction memo(object, key, fn) {\n  let memoizedData = cache.get(object);\n  if (!memoizedData) {\n    memoizedData = {\n      [key]: fn()\n    };\n    cache.set(object, memoizedData);\n  } else if (!(key in memoizedData)) {\n    memoizedData[key] = fn();\n  }\n  return memoizedData[key];\n}\n\nconst EMPTY_OBJECT = {};\nconst toSerializableAction = action => {\n  if (typeof action === 'string') {\n    return {\n      type: action\n    };\n  }\n  if (typeof action === 'function') {\n    if ('resolve' in action) {\n      return {\n        type: action.type\n      };\n    }\n    return {\n      type: action.name\n    };\n  }\n  return action;\n};\nclass StateNode {\n  constructor(/** The raw config used to create the machine. */\n  config, options) {\n    this.config = config;\n    /**\n     * The relative key of the state node, which represents its location in the\n     * overall state value.\n     */\n    this.key = void 0;\n    /** The unique ID of the state node. */\n    this.id = void 0;\n    /**\n     * The type of this state node:\n     *\n     * - `'atomic'` - no child state nodes\n     * - `'compound'` - nested child state nodes (XOR)\n     * - `'parallel'` - orthogonal nested child state nodes (AND)\n     * - `'history'` - history state node\n     * - `'final'` - final state node\n     */\n    this.type = void 0;\n    /** The string path from the root machine node to this node. */\n    this.path = void 0;\n    /** The child state nodes. */\n    this.states = void 0;\n    /**\n     * The type of history on this state node. Can be:\n     *\n     * - `'shallow'` - recalls only top-level historical state value\n     * - `'deep'` - recalls historical state value at all levels\n     */\n    this.history = void 0;\n    /** The action(s) to be executed upon entering the state node. */\n    this.entry = void 0;\n    /** The action(s) to be executed upon exiting the state node. */\n    this.exit = void 0;\n    /** The parent state node. */\n    this.parent = void 0;\n    /** The root machine node. */\n    this.machine = void 0;\n    /**\n     * The meta data associated with this state node, which will be returned in\n     * State instances.\n     */\n    this.meta = void 0;\n    /**\n     * The output data sent with the \"xstate.done.state._id_\" event if this is a\n     * final state node.\n     */\n    this.output = void 0;\n    /**\n     * The order this state node appears. Corresponds to the implicit document\n     * order.\n     */\n    this.order = -1;\n    this.description = void 0;\n    this.tags = [];\n    this.transitions = void 0;\n    this.always = void 0;\n    this.parent = options._parent;\n    this.key = options._key;\n    this.machine = options._machine;\n    this.path = this.parent ? this.parent.path.concat(this.key) : [];\n    this.id = this.config.id || [this.machine.id, ...this.path].join(_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.S);\n    this.type = this.config.type || (this.config.states && Object.keys(this.config.states).length ? 'compound' : this.config.history ? 'history' : 'atomic');\n    this.description = this.config.description;\n    this.order = this.machine.idMap.size;\n    this.machine.idMap.set(this.id, this);\n    this.states = this.config.states ? (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.l)(this.config.states, (stateConfig, key) => {\n      const stateNode = new StateNode(stateConfig, {\n        _parent: this,\n        _key: key,\n        _machine: this.machine\n      });\n      return stateNode;\n    }) : EMPTY_OBJECT;\n    if (this.type === 'compound' && !this.config.initial) {\n      throw new Error(`No initial state specified for compound state node \"#${this.id}\". Try adding { initial: \"${Object.keys(this.states)[0]}\" } to the state config.`);\n    }\n\n    // History config\n    this.history = this.config.history === true ? 'shallow' : this.config.history || false;\n    this.entry = (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.t)(this.config.entry).slice();\n    this.exit = (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.t)(this.config.exit).slice();\n    this.meta = this.config.meta;\n    this.output = this.type === 'final' || !this.parent ? this.config.output : undefined;\n    this.tags = (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.t)(config.tags).slice();\n  }\n\n  /** @internal */\n  _initialize() {\n    this.transitions = (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.q)(this);\n    if (this.config.always) {\n      this.always = (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.u)(this.config.always).map(t => (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.v)(this, _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.N, t));\n    }\n    Object.keys(this.states).forEach(key => {\n      this.states[key]._initialize();\n    });\n  }\n\n  /** The well-structured state node definition. */\n  get definition() {\n    return {\n      id: this.id,\n      key: this.key,\n      version: this.machine.version,\n      type: this.type,\n      initial: this.initial ? {\n        target: this.initial.target,\n        source: this,\n        actions: this.initial.actions.map(toSerializableAction),\n        eventType: null,\n        reenter: false,\n        toJSON: () => ({\n          target: this.initial.target.map(t => `#${t.id}`),\n          source: `#${this.id}`,\n          actions: this.initial.actions.map(toSerializableAction),\n          eventType: null\n        })\n      } : undefined,\n      history: this.history,\n      states: (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.l)(this.states, state => {\n        return state.definition;\n      }),\n      on: this.on,\n      transitions: [...this.transitions.values()].flat().map(t => ({\n        ...t,\n        actions: t.actions.map(toSerializableAction)\n      })),\n      entry: this.entry.map(toSerializableAction),\n      exit: this.exit.map(toSerializableAction),\n      meta: this.meta,\n      order: this.order || -1,\n      output: this.output,\n      invoke: this.invoke,\n      description: this.description,\n      tags: this.tags\n    };\n  }\n\n  /** @internal */\n  toJSON() {\n    return this.definition;\n  }\n\n  /** The logic invoked as actors by this state node. */\n  get invoke() {\n    return memo(this, 'invoke', () => (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.t)(this.config.invoke).map((invokeConfig, i) => {\n      const {\n        src,\n        systemId\n      } = invokeConfig;\n      const resolvedId = invokeConfig.id ?? (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.x)(this.id, i);\n      const sourceName = typeof src === 'string' ? src : `xstate.invoke.${(0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.x)(this.id, i)}`;\n      return {\n        ...invokeConfig,\n        src: sourceName,\n        id: resolvedId,\n        systemId: systemId,\n        toJSON() {\n          const {\n            onDone,\n            onError,\n            ...invokeDefValues\n          } = invokeConfig;\n          return {\n            ...invokeDefValues,\n            type: 'xstate.invoke',\n            src: sourceName,\n            id: resolvedId\n          };\n        }\n      };\n    }));\n  }\n\n  /** The mapping of events to transitions. */\n  get on() {\n    return memo(this, 'on', () => {\n      const transitions = this.transitions;\n      return [...transitions].flatMap(([descriptor, t]) => t.map(t => [descriptor, t])).reduce((map, [descriptor, transition]) => {\n        map[descriptor] = map[descriptor] || [];\n        map[descriptor].push(transition);\n        return map;\n      }, {});\n    });\n  }\n  get after() {\n    return memo(this, 'delayedTransitions', () => (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.y)(this));\n  }\n  get initial() {\n    return memo(this, 'initial', () => (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.z)(this, this.config.initial));\n  }\n\n  /** @internal */\n  next(snapshot, event) {\n    const eventType = event.type;\n    const actions = [];\n    let selectedTransition;\n    const candidates = memo(this, `candidates-${eventType}`, () => (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.B)(this, eventType));\n    for (const candidate of candidates) {\n      const {\n        guard\n      } = candidate;\n      const resolvedContext = snapshot.context;\n      let guardPassed = false;\n      try {\n        guardPassed = !guard || (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.w)(guard, resolvedContext, event, snapshot);\n      } catch (err) {\n        const guardType = typeof guard === 'string' ? guard : typeof guard === 'object' ? guard.type : undefined;\n        throw new Error(`Unable to evaluate guard ${guardType ? `'${guardType}' ` : ''}in transition for event '${eventType}' in state node '${this.id}':\\n${err.message}`);\n      }\n      if (guardPassed) {\n        actions.push(...candidate.actions);\n        selectedTransition = candidate;\n        break;\n      }\n    }\n    return selectedTransition ? [selectedTransition] : undefined;\n  }\n\n  /** All the event types accepted by this state node and its descendants. */\n  get events() {\n    return memo(this, 'events', () => {\n      const {\n        states\n      } = this;\n      const events = new Set(this.ownEvents);\n      if (states) {\n        for (const stateId of Object.keys(states)) {\n          const state = states[stateId];\n          if (state.states) {\n            for (const event of state.events) {\n              events.add(`${event}`);\n            }\n          }\n        }\n      }\n      return Array.from(events);\n    });\n  }\n\n  /**\n   * All the events that have transitions directly from this state node.\n   *\n   * Excludes any inert events.\n   */\n  get ownEvents() {\n    const events = new Set([...this.transitions.keys()].filter(descriptor => {\n      return this.transitions.get(descriptor).some(transition => !(!transition.target && !transition.actions.length && !transition.reenter));\n    }));\n    return Array.from(events);\n  }\n}\n\nconst STATE_IDENTIFIER = '#';\nclass StateMachine {\n  constructor(/** The raw config used to create the machine. */\n  config, implementations) {\n    this.config = config;\n    /** The machine's own version. */\n    this.version = void 0;\n    this.schemas = void 0;\n    this.implementations = void 0;\n    /** @internal */\n    this.__xstatenode = true;\n    /** @internal */\n    this.idMap = new Map();\n    this.root = void 0;\n    this.id = void 0;\n    this.states = void 0;\n    this.events = void 0;\n    this.id = config.id || '(machine)';\n    this.implementations = {\n      actors: implementations?.actors ?? {},\n      actions: implementations?.actions ?? {},\n      delays: implementations?.delays ?? {},\n      guards: implementations?.guards ?? {}\n    };\n    this.version = this.config.version;\n    this.schemas = this.config.schemas;\n    this.transition = this.transition.bind(this);\n    this.getInitialSnapshot = this.getInitialSnapshot.bind(this);\n    this.getPersistedSnapshot = this.getPersistedSnapshot.bind(this);\n    this.restoreSnapshot = this.restoreSnapshot.bind(this);\n    this.start = this.start.bind(this);\n    this.root = new StateNode(config, {\n      _key: this.id,\n      _machine: this\n    });\n    this.root._initialize();\n    this.states = this.root.states; // TODO: remove!\n    this.events = this.root.events;\n    if (!('output' in this.root) && Object.values(this.states).some(state => state.type === 'final' && 'output' in state)) {\n      console.warn('Missing `machine.output` declaration (top-level final state with output detected)');\n    }\n  }\n\n  /**\n   * Clones this state machine with the provided implementations.\n   *\n   * @param implementations Options (`actions`, `guards`, `actors`, `delays`) to\n   *   recursively merge with the existing options.\n   * @returns A new `StateMachine` instance with the provided implementations.\n   */\n  provide(implementations) {\n    const {\n      actions,\n      guards,\n      actors,\n      delays\n    } = this.implementations;\n    return new StateMachine(this.config, {\n      actions: {\n        ...actions,\n        ...implementations.actions\n      },\n      guards: {\n        ...guards,\n        ...implementations.guards\n      },\n      actors: {\n        ...actors,\n        ...implementations.actors\n      },\n      delays: {\n        ...delays,\n        ...implementations.delays\n      }\n    });\n  }\n  resolveState(config) {\n    const resolvedStateValue = (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.C)(this.root, config.value);\n    const nodeSet = (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.D)((0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.g)(this.root, resolvedStateValue));\n    return (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.E)({\n      _nodes: [...nodeSet],\n      context: config.context || {},\n      children: {},\n      status: (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.F)(nodeSet, this.root) ? 'done' : config.status || 'active',\n      output: config.output,\n      error: config.error,\n      historyValue: config.historyValue\n    }, this);\n  }\n\n  /**\n   * Determines the next snapshot given the current `snapshot` and received\n   * `event`. Calculates a full macrostep from all microsteps.\n   *\n   * @param snapshot The current snapshot\n   * @param event The received event\n   */\n  transition(snapshot, event, actorScope) {\n    return (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.G)(snapshot, event, actorScope, []).snapshot;\n  }\n\n  /**\n   * Determines the next state given the current `state` and `event`. Calculates\n   * a microstep.\n   *\n   * @param state The current state\n   * @param event The received event\n   */\n  microstep(snapshot, event, actorScope) {\n    return (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.G)(snapshot, event, actorScope, []).microstates;\n  }\n  getTransitionData(snapshot, event) {\n    return (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.H)(this.root, snapshot.value, snapshot, event) || [];\n  }\n\n  /**\n   * The initial state _before_ evaluating any microsteps. This \"pre-initial\"\n   * state is provided to initial actions executed in the initial state.\n   */\n  getPreInitialState(actorScope, initEvent, internalQueue) {\n    const {\n      context\n    } = this.config;\n    const preInitial = (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.E)({\n      context: typeof context !== 'function' && context ? context : {},\n      _nodes: [this.root],\n      children: {},\n      status: 'active'\n    }, this);\n    if (typeof context === 'function') {\n      const assignment = ({\n        spawn,\n        event,\n        self\n      }) => context({\n        spawn,\n        input: event.input,\n        self\n      });\n      return (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.I)(preInitial, initEvent, actorScope, [(0,_assign_6313ccb3_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.a)(assignment)], internalQueue, undefined);\n    }\n    return preInitial;\n  }\n\n  /**\n   * Returns the initial `State` instance, with reference to `self` as an\n   * `ActorRef`.\n   */\n  getInitialSnapshot(actorScope, input) {\n    const initEvent = (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.J)(input); // TODO: fix;\n    const internalQueue = [];\n    const preInitialState = this.getPreInitialState(actorScope, initEvent, internalQueue);\n    const nextState = (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.K)([{\n      target: [...(0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.L)(this.root)],\n      source: this.root,\n      reenter: true,\n      actions: [],\n      eventType: null,\n      toJSON: null // TODO: fix\n    }], preInitialState, actorScope, initEvent, true, internalQueue);\n    const {\n      snapshot: macroState\n    } = (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.G)(nextState, initEvent, actorScope, internalQueue);\n    return macroState;\n  }\n  start(snapshot) {\n    Object.values(snapshot.children).forEach(child => {\n      if (child.getSnapshot().status === 'active') {\n        child.start();\n      }\n    });\n  }\n  getStateNodeById(stateId) {\n    const fullPath = (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.M)(stateId);\n    const relativePath = fullPath.slice(1);\n    const resolvedStateId = (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.O)(fullPath[0]) ? fullPath[0].slice(STATE_IDENTIFIER.length) : fullPath[0];\n    const stateNode = this.idMap.get(resolvedStateId);\n    if (!stateNode) {\n      throw new Error(`Child state node '#${resolvedStateId}' does not exist on machine '${this.id}'`);\n    }\n    return (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.P)(stateNode, relativePath);\n  }\n  get definition() {\n    return this.root.definition;\n  }\n  toJSON() {\n    return this.definition;\n  }\n  getPersistedSnapshot(snapshot, options) {\n    return (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.Q)(snapshot, options);\n  }\n  restoreSnapshot(snapshot, _actorScope) {\n    const children = {};\n    const snapshotChildren = snapshot.children;\n    Object.keys(snapshotChildren).forEach(actorId => {\n      const actorData = snapshotChildren[actorId];\n      const childState = actorData.snapshot;\n      const src = actorData.src;\n      const logic = typeof src === 'string' ? (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.R)(this, src) : src;\n      if (!logic) {\n        return;\n      }\n      const actorRef = (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.c)(logic, {\n        id: actorId,\n        parent: _actorScope.self,\n        syncSnapshot: actorData.syncSnapshot,\n        snapshot: childState,\n        src,\n        systemId: actorData.systemId\n      });\n      children[actorId] = actorRef;\n    });\n    function resolveHistoryReferencedState(root, referenced) {\n      if (referenced instanceof StateNode) {\n        return referenced;\n      }\n      try {\n        return root.machine.getStateNodeById(referenced.id);\n      } catch {\n        {\n          console.warn(`Could not resolve StateNode for id: ${referenced.id}`);\n        }\n      }\n    }\n    function reviveHistoryValue(root, historyValue) {\n      if (!historyValue || typeof historyValue !== 'object') {\n        return {};\n      }\n      const revived = {};\n      for (const key in historyValue) {\n        const arr = historyValue[key];\n        for (const item of arr) {\n          const resolved = resolveHistoryReferencedState(root, item);\n          if (!resolved) {\n            continue;\n          }\n          revived[key] ??= [];\n          revived[key].push(resolved);\n        }\n      }\n      return revived;\n    }\n    const revivedHistoryValue = reviveHistoryValue(this.root, snapshot.historyValue);\n    const restoredSnapshot = (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.E)({\n      ...snapshot,\n      children,\n      _nodes: Array.from((0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.D)((0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.g)(this.root, snapshot.value))),\n      historyValue: revivedHistoryValue\n    }, this);\n    const seen = new Set();\n    function reviveContext(contextPart, children) {\n      if (seen.has(contextPart)) {\n        return;\n      }\n      seen.add(contextPart);\n      for (const key in contextPart) {\n        const value = contextPart[key];\n        if (value && typeof value === 'object') {\n          if ('xstate$$type' in value && value.xstate$$type === _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.$) {\n            contextPart[key] = children[value.id];\n            continue;\n          }\n          reviveContext(value, children);\n        }\n      }\n    }\n    reviveContext(restoredSnapshot.context, children);\n    return restoredSnapshot;\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xstate/dist/StateMachine-b4e94439.development.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xstate/dist/assign-6313ccb3.development.esm.js":
/*!*********************************************************************!*\
  !*** ./node_modules/xstate/dist/assign-6313ccb3.development.esm.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ assign)\n/* harmony export */ });\n/* harmony import */ var _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./raise-78b8dcb8.development.esm.js */ \"(ssr)/./node_modules/xstate/dist/raise-78b8dcb8.development.esm.js\");\n\n\nfunction createSpawner(actorScope, {\n  machine,\n  context\n}, event, spawnedChildren) {\n  const spawn = (src, options) => {\n    if (typeof src === 'string') {\n      const logic = (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.R)(machine, src);\n      if (!logic) {\n        throw new Error(`Actor logic '${src}' not implemented in machine '${machine.id}'`);\n      }\n      const actorRef = (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.c)(logic, {\n        id: options?.id,\n        parent: actorScope.self,\n        syncSnapshot: options?.syncSnapshot,\n        input: typeof options?.input === 'function' ? options.input({\n          context,\n          event,\n          self: actorScope.self\n        }) : options?.input,\n        src,\n        systemId: options?.systemId\n      });\n      spawnedChildren[actorRef.id] = actorRef;\n      return actorRef;\n    } else {\n      const actorRef = (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.c)(src, {\n        id: options?.id,\n        parent: actorScope.self,\n        syncSnapshot: options?.syncSnapshot,\n        input: options?.input,\n        src,\n        systemId: options?.systemId\n      });\n      return actorRef;\n    }\n  };\n  return (src, options) => {\n    const actorRef = spawn(src, options); // TODO: fix types\n    spawnedChildren[actorRef.id] = actorRef;\n    actorScope.defer(() => {\n      if (actorRef._processingStatus === _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.W.Stopped) {\n        return;\n      }\n      actorRef.start();\n    });\n    return actorRef;\n  };\n}\n\nfunction resolveAssign(actorScope, snapshot, actionArgs, actionParams, {\n  assignment\n}) {\n  if (!snapshot.context) {\n    throw new Error('Cannot assign to undefined `context`. Ensure that `context` is defined in the machine config.');\n  }\n  const spawnedChildren = {};\n  const assignArgs = {\n    context: snapshot.context,\n    event: actionArgs.event,\n    spawn: createSpawner(actorScope, snapshot, actionArgs.event, spawnedChildren),\n    self: actorScope.self,\n    system: actorScope.system\n  };\n  let partialUpdate = {};\n  if (typeof assignment === 'function') {\n    partialUpdate = assignment(assignArgs, actionParams);\n  } else {\n    for (const key of Object.keys(assignment)) {\n      const propAssignment = assignment[key];\n      partialUpdate[key] = typeof propAssignment === 'function' ? propAssignment(assignArgs, actionParams) : propAssignment;\n    }\n  }\n  const updatedContext = Object.assign({}, snapshot.context, partialUpdate);\n  return [(0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.Y)(snapshot, {\n    context: updatedContext,\n    children: Object.keys(spawnedChildren).length ? {\n      ...snapshot.children,\n      ...spawnedChildren\n    } : snapshot.children\n  }), undefined, undefined];\n}\n/**\n * Updates the current context of the machine.\n *\n * @example\n *\n * ```ts\n * import { createMachine, assign } from 'xstate';\n *\n * const countMachine = createMachine({\n *   context: {\n *     count: 0,\n *     message: ''\n *   },\n *   on: {\n *     inc: {\n *       actions: assign({\n *         count: ({ context }) => context.count + 1\n *       })\n *     },\n *     updateMessage: {\n *       actions: assign(({ context, event }) => {\n *         return {\n *           message: event.message.trim()\n *         };\n *       })\n *     }\n *   }\n * });\n * ```\n *\n * @param assignment An object that represents the partial context to update, or\n *   a function that returns an object that represents the partial context to\n *   update.\n */\nfunction assign(assignment) {\n  if (_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.T) {\n    console.warn('Custom actions should not call `assign()` directly, as it is not imperative. See https://stately.ai/docs/actions#built-in-actions for more details.');\n  }\n  function assign(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  assign.type = 'xstate.assign';\n  assign.assignment = assignment;\n  assign.resolve = resolveAssign;\n  return assign;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xstate/dist/assign-6313ccb3.development.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xstate/dist/log-ef959da6.development.esm.js":
/*!******************************************************************!*\
  !*** ./node_modules/xstate/dist/log-ef959da6.development.esm.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   S: () => (/* binding */ SpecialTargets),\n/* harmony export */   a: () => (/* binding */ enqueueActions),\n/* harmony export */   b: () => (/* binding */ sendTo),\n/* harmony export */   e: () => (/* binding */ emit),\n/* harmony export */   f: () => (/* binding */ forwardTo),\n/* harmony export */   l: () => (/* binding */ log),\n/* harmony export */   s: () => (/* binding */ sendParent)\n/* harmony export */ });\n/* harmony import */ var _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./raise-78b8dcb8.development.esm.js */ \"(ssr)/./node_modules/xstate/dist/raise-78b8dcb8.development.esm.js\");\n/* harmony import */ var _assign_6313ccb3_development_esm_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./assign-6313ccb3.development.esm.js */ \"(ssr)/./node_modules/xstate/dist/assign-6313ccb3.development.esm.js\");\n\n\n\nfunction resolveEmit(_, snapshot, args, actionParams, {\n  event: eventOrExpr\n}) {\n  const resolvedEvent = typeof eventOrExpr === 'function' ? eventOrExpr(args, actionParams) : eventOrExpr;\n  return [snapshot, {\n    event: resolvedEvent\n  }, undefined];\n}\nfunction executeEmit(actorScope, {\n  event\n}) {\n  actorScope.defer(() => actorScope.emit(event));\n}\n/**\n * Emits an event to event handlers registered on the actor via `actor.on(event,\n * handler)`.\n *\n * @example\n *\n * ```ts\n * import { emit } from 'xstate';\n *\n * const machine = createMachine({\n *   // ...\n *   on: {\n *     something: {\n *       actions: emit({\n *         type: 'emitted',\n *         some: 'data'\n *       })\n *     }\n *   }\n *   // ...\n * });\n *\n * const actor = createActor(machine).start();\n *\n * actor.on('emitted', (event) => {\n *   console.log(event);\n * });\n *\n * actor.send({ type: 'something' });\n * // logs:\n * // {\n * //   type: 'emitted',\n * //   some: 'data'\n * // }\n * ```\n */\nfunction emit(/** The event to emit, or an expression that returns an event to emit. */\neventOrExpr) {\n  if (_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.T) {\n    console.warn('Custom actions should not call `emit()` directly, as it is not imperative. See https://stately.ai/docs/actions#built-in-actions for more details.');\n  }\n  function emit(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  emit.type = 'xstate.emit';\n  emit.event = eventOrExpr;\n  emit.resolve = resolveEmit;\n  emit.execute = executeEmit;\n  return emit;\n}\n\n// this is needed to make JSDoc `@link` work properly\n\n/**\n * @remarks\n * `T | unknown` reduces to `unknown` and that can be problematic when it comes\n * to contextual typing. It especially is a problem when the union has a\n * function member, like here:\n *\n * ```ts\n * declare function test(\n *   cbOrVal: ((arg: number) => unknown) | unknown\n * ): void;\n * test((arg) => {}); // oops, implicit any\n * ```\n *\n * This type can be used to avoid this problem. This union represents the same\n * value space as `unknown`.\n */\n\n// https://github.com/microsoft/TypeScript/issues/23182#issuecomment-379091887\n\n// @TODO: we can't use native `NoInfer` as we need those:\n// https://github.com/microsoft/TypeScript/pull/61092\n// https://github.com/microsoft/TypeScript/pull/61077\n// but even with those fixes native NoInfer still doesn't work - further issues have to be reproduced and fixed\n\n/** @deprecated Use the built-in `NoInfer` type instead */\n\n/** The full definition of an event, with a string `type`. */\n\n/**\n * The string or object representing the state value relative to the parent\n * state node.\n *\n * @remarks\n * - For a child atomic state node, this is a string, e.g., `\"pending\"`.\n * - For complex state nodes, this is an object, e.g., `{ success:\n *   \"someChildState\" }`.\n */\n\n/** @deprecated Use `AnyMachineSnapshot` instead */\n\n// TODO: possibly refactor this somehow, use even a simpler type, and maybe even make `machine.options` private or something\n/** @ignore */\n\nlet SpecialTargets = /*#__PURE__*/function (SpecialTargets) {\n  SpecialTargets[\"Parent\"] = \"#_parent\";\n  SpecialTargets[\"Internal\"] = \"#_internal\";\n  return SpecialTargets;\n}({});\n\n/** @deprecated Use `AnyActor` instead. */\n\n// Based on RxJS types\n\n// TODO: in v6, this should only accept AnyActorLogic, like ActorRefFromLogic\n\n/** @deprecated Use `Actor<T>` instead. */\n\n/**\n * Represents logic which can be used by an actor.\n *\n * @template TSnapshot - The type of the snapshot.\n * @template TEvent - The type of the event object.\n * @template TInput - The type of the input.\n * @template TSystem - The type of the actor system.\n */\n\n/** @deprecated */\n\n// TODO: cover all that can be actually returned\n\nfunction resolveSendTo(actorScope, snapshot, args, actionParams, {\n  to,\n  event: eventOrExpr,\n  id,\n  delay\n}, extra) {\n  const delaysMap = snapshot.machine.implementations.delays;\n  if (typeof eventOrExpr === 'string') {\n    throw new Error(\n    // eslint-disable-next-line @typescript-eslint/restrict-template-expressions\n    `Only event objects may be used with sendTo; use sendTo({ type: \"${eventOrExpr}\" }) instead`);\n  }\n  const resolvedEvent = typeof eventOrExpr === 'function' ? eventOrExpr(args, actionParams) : eventOrExpr;\n  let resolvedDelay;\n  if (typeof delay === 'string') {\n    const configDelay = delaysMap && delaysMap[delay];\n    resolvedDelay = typeof configDelay === 'function' ? configDelay(args, actionParams) : configDelay;\n  } else {\n    resolvedDelay = typeof delay === 'function' ? delay(args, actionParams) : delay;\n  }\n  const resolvedTarget = typeof to === 'function' ? to(args, actionParams) : to;\n  let targetActorRef;\n  if (typeof resolvedTarget === 'string') {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison\n    if (resolvedTarget === SpecialTargets.Parent) {\n      targetActorRef = actorScope.self._parent;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison\n    else if (resolvedTarget === SpecialTargets.Internal) {\n      targetActorRef = actorScope.self;\n    } else if (resolvedTarget.startsWith('#_')) {\n      // SCXML compatibility: https://www.w3.org/TR/scxml/#SCXMLEventProcessor\n      // #_invokeid. If the target is the special term '#_invokeid', where invokeid is the invokeid of an SCXML session that the sending session has created by <invoke>, the Processor must add the event to the external queue of that session.\n      targetActorRef = snapshot.children[resolvedTarget.slice(2)];\n    } else {\n      targetActorRef = extra.deferredActorIds?.includes(resolvedTarget) ? resolvedTarget : snapshot.children[resolvedTarget];\n    }\n    if (!targetActorRef) {\n      throw new Error(`Unable to send event to actor '${resolvedTarget}' from machine '${snapshot.machine.id}'.`);\n    }\n  } else {\n    targetActorRef = resolvedTarget || actorScope.self;\n  }\n  return [snapshot, {\n    to: targetActorRef,\n    targetId: typeof resolvedTarget === 'string' ? resolvedTarget : undefined,\n    event: resolvedEvent,\n    id,\n    delay: resolvedDelay\n  }, undefined];\n}\nfunction retryResolveSendTo(_, snapshot, params) {\n  if (typeof params.to === 'string') {\n    params.to = snapshot.children[params.to];\n  }\n}\nfunction executeSendTo(actorScope, params) {\n  // this forms an outgoing events queue\n  // thanks to that the recipient actors are able to read the *updated* snapshot value of the sender\n  actorScope.defer(() => {\n    const {\n      to,\n      event,\n      delay,\n      id\n    } = params;\n    if (typeof delay === 'number') {\n      actorScope.system.scheduler.schedule(actorScope.self, to, event, delay, id);\n      return;\n    }\n    actorScope.system._relay(actorScope.self,\n    // at this point, in a deferred task, it should already be mutated by retryResolveSendTo\n    // if it initially started as a string\n    to, event.type === _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.U ? (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.V)(actorScope.self.id, event.data) : event);\n  });\n}\n/**\n * Sends an event to an actor.\n *\n * @param actor The `ActorRef` to send the event to.\n * @param event The event to send, or an expression that evaluates to the event\n *   to send\n * @param options Send action options\n *\n *   - `id` - The unique send event identifier (used with `cancel()`).\n *   - `delay` - The number of milliseconds to delay the sending of the event.\n */\nfunction sendTo(to, eventOrExpr, options) {\n  if (_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.T) {\n    console.warn('Custom actions should not call `sendTo()` directly, as it is not imperative. See https://stately.ai/docs/actions#built-in-actions for more details.');\n  }\n  function sendTo(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  sendTo.type = 'xstate.sendTo';\n  sendTo.to = to;\n  sendTo.event = eventOrExpr;\n  sendTo.id = options?.id;\n  sendTo.delay = options?.delay;\n  sendTo.resolve = resolveSendTo;\n  sendTo.retryResolve = retryResolveSendTo;\n  sendTo.execute = executeSendTo;\n  return sendTo;\n}\n\n/**\n * Sends an event to this machine's parent.\n *\n * @param event The event to send to the parent machine.\n * @param options Options to pass into the send event.\n */\nfunction sendParent(event, options) {\n  return sendTo(SpecialTargets.Parent, event, options);\n}\n/**\n * Forwards (sends) an event to the `target` actor.\n *\n * @param target The target actor to forward the event to.\n * @param options Options to pass into the send action creator.\n */\nfunction forwardTo(target, options) {\n  if ((!target || typeof target === 'function')) {\n    const originalTarget = target;\n    target = (...args) => {\n      const resolvedTarget = typeof originalTarget === 'function' ? originalTarget(...args) : originalTarget;\n      if (!resolvedTarget) {\n        throw new Error(`Attempted to forward event to undefined actor. This risks an infinite loop in the sender.`);\n      }\n      return resolvedTarget;\n    };\n  }\n  return sendTo(target, ({\n    event\n  }) => event, options);\n}\n\nfunction resolveEnqueueActions(actorScope, snapshot, args, actionParams, {\n  collect\n}) {\n  const actions = [];\n  const enqueue = function enqueue(action) {\n    actions.push(action);\n  };\n  enqueue.assign = (...args) => {\n    actions.push((0,_assign_6313ccb3_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.a)(...args));\n  };\n  enqueue.cancel = (...args) => {\n    actions.push((0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.f)(...args));\n  };\n  enqueue.raise = (...args) => {\n    // for some reason it fails to infer `TDelay` from `...args` here and picks its default (`never`)\n    // then it fails to typecheck that because `...args` use `string` in place of `TDelay`\n    actions.push((0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.r)(...args));\n  };\n  enqueue.sendTo = (...args) => {\n    // for some reason it fails to infer `TDelay` from `...args` here and picks its default (`never`)\n    // then it fails to typecheck that because `...args` use `string` in place of `TDelay\n    actions.push(sendTo(...args));\n  };\n  enqueue.sendParent = (...args) => {\n    actions.push(sendParent(...args));\n  };\n  enqueue.spawnChild = (...args) => {\n    actions.push((0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.h)(...args));\n  };\n  enqueue.stopChild = (...args) => {\n    actions.push((0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.k)(...args));\n  };\n  enqueue.emit = (...args) => {\n    actions.push(emit(...args));\n  };\n  collect({\n    context: args.context,\n    event: args.event,\n    enqueue,\n    check: guard => (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.w)(guard, snapshot.context, args.event, snapshot),\n    self: actorScope.self,\n    system: actorScope.system\n  }, actionParams);\n  return [snapshot, undefined, actions];\n}\n/**\n * Creates an action object that will execute actions that are queued by the\n * `enqueue(action)` function.\n *\n * @example\n *\n * ```ts\n * import { createMachine, enqueueActions } from 'xstate';\n *\n * const machine = createMachine({\n *   entry: enqueueActions(({ enqueue, check }) => {\n *     enqueue.assign({ count: 0 });\n *\n *     if (check('someGuard')) {\n *       enqueue.assign({ count: 1 });\n *     }\n *\n *     enqueue('someAction');\n *   })\n * });\n * ```\n */\nfunction enqueueActions(collect) {\n  function enqueueActions(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  enqueueActions.type = 'xstate.enqueueActions';\n  enqueueActions.collect = collect;\n  enqueueActions.resolve = resolveEnqueueActions;\n  return enqueueActions;\n}\n\nfunction resolveLog(_, snapshot, actionArgs, actionParams, {\n  value,\n  label\n}) {\n  return [snapshot, {\n    value: typeof value === 'function' ? value(actionArgs, actionParams) : value,\n    label\n  }, undefined];\n}\nfunction executeLog({\n  logger\n}, {\n  value,\n  label\n}) {\n  if (label) {\n    logger(label, value);\n  } else {\n    logger(value);\n  }\n}\n/**\n * @param expr The expression function to evaluate which will be logged. Takes\n *   in 2 arguments:\n *\n *   - `ctx` - the current state context\n *   - `event` - the event that caused this action to be executed.\n *\n * @param label The label to give to the logged expression.\n */\nfunction log(value = ({\n  context,\n  event\n}) => ({\n  context,\n  event\n}), label) {\n  function log(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  log.type = 'xstate.log';\n  log.value = value;\n  log.label = label;\n  log.resolve = resolveLog;\n  log.execute = executeLog;\n  return log;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xstate/dist/log-ef959da6.development.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xstate/dist/raise-78b8dcb8.development.esm.js":
/*!********************************************************************!*\
  !*** ./node_modules/xstate/dist/raise-78b8dcb8.development.esm.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $: () => (/* binding */ $$ACTOR_TYPE),\n/* harmony export */   A: () => (/* binding */ Actor),\n/* harmony export */   B: () => (/* binding */ getCandidates),\n/* harmony export */   C: () => (/* binding */ resolveStateValue),\n/* harmony export */   D: () => (/* binding */ getAllStateNodes),\n/* harmony export */   E: () => (/* binding */ createMachineSnapshot),\n/* harmony export */   F: () => (/* binding */ isInFinalState),\n/* harmony export */   G: () => (/* binding */ macrostep),\n/* harmony export */   H: () => (/* binding */ transitionNode),\n/* harmony export */   I: () => (/* binding */ resolveActionsAndContext),\n/* harmony export */   J: () => (/* binding */ createInitEvent),\n/* harmony export */   K: () => (/* binding */ microstep),\n/* harmony export */   L: () => (/* binding */ getInitialStateNodes),\n/* harmony export */   M: () => (/* binding */ toStatePath),\n/* harmony export */   N: () => (/* binding */ NULL_EVENT),\n/* harmony export */   O: () => (/* binding */ isStateId),\n/* harmony export */   P: () => (/* binding */ getStateNodeByPath),\n/* harmony export */   Q: () => (/* binding */ getPersistedSnapshot),\n/* harmony export */   R: () => (/* binding */ resolveReferencedActor),\n/* harmony export */   S: () => (/* binding */ STATE_DELIMITER),\n/* harmony export */   T: () => (/* binding */ executingCustomAction),\n/* harmony export */   U: () => (/* binding */ XSTATE_ERROR),\n/* harmony export */   V: () => (/* binding */ createErrorActorEvent),\n/* harmony export */   W: () => (/* binding */ ProcessingStatus),\n/* harmony export */   X: () => (/* binding */ XSTATE_STOP),\n/* harmony export */   Y: () => (/* binding */ cloneMachineSnapshot),\n/* harmony export */   a: () => (/* binding */ and),\n/* harmony export */   b: () => (/* binding */ isMachineSnapshot),\n/* harmony export */   c: () => (/* binding */ createActor),\n/* harmony export */   d: () => (/* binding */ getAllOwnEventDescriptors),\n/* harmony export */   e: () => (/* binding */ toObserver),\n/* harmony export */   f: () => (/* binding */ cancel),\n/* harmony export */   g: () => (/* binding */ getStateNodes),\n/* harmony export */   h: () => (/* binding */ spawnChild),\n/* harmony export */   i: () => (/* binding */ interpret),\n/* harmony export */   j: () => (/* binding */ stop),\n/* harmony export */   k: () => (/* binding */ stopChild),\n/* harmony export */   l: () => (/* binding */ mapValues),\n/* harmony export */   m: () => (/* binding */ matchesState),\n/* harmony export */   n: () => (/* binding */ not),\n/* harmony export */   o: () => (/* binding */ or),\n/* harmony export */   p: () => (/* binding */ pathToStateValue),\n/* harmony export */   q: () => (/* binding */ formatTransitions),\n/* harmony export */   r: () => (/* binding */ raise),\n/* harmony export */   s: () => (/* binding */ stateIn),\n/* harmony export */   t: () => (/* binding */ toArray),\n/* harmony export */   u: () => (/* binding */ toTransitionConfigArray),\n/* harmony export */   v: () => (/* binding */ formatTransition),\n/* harmony export */   w: () => (/* binding */ evaluateGuard),\n/* harmony export */   x: () => (/* binding */ createInvokeId),\n/* harmony export */   y: () => (/* binding */ getDelayedTransitions),\n/* harmony export */   z: () => (/* binding */ formatInitialTransition)\n/* harmony export */ });\n/* harmony import */ var _dev_dist_xstate_dev_development_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../dev/dist/xstate-dev.development.esm.js */ \"(ssr)/./node_modules/xstate/dev/dist/xstate-dev.development.esm.js\");\n\n\nclass Mailbox {\n  constructor(_process) {\n    this._process = _process;\n    this._active = false;\n    this._current = null;\n    this._last = null;\n  }\n  start() {\n    this._active = true;\n    this.flush();\n  }\n  clear() {\n    // we can't set _current to null because we might be currently processing\n    // and enqueue following clear shouldn't start processing the enqueued item immediately\n    if (this._current) {\n      this._current.next = null;\n      this._last = this._current;\n    }\n  }\n  enqueue(event) {\n    const enqueued = {\n      value: event,\n      next: null\n    };\n    if (this._current) {\n      this._last.next = enqueued;\n      this._last = enqueued;\n      return;\n    }\n    this._current = enqueued;\n    this._last = enqueued;\n    if (this._active) {\n      this.flush();\n    }\n  }\n  flush() {\n    while (this._current) {\n      // atm the given _process is responsible for implementing proper try/catch handling\n      // we assume here that this won't throw in a way that can affect this mailbox\n      const consumed = this._current;\n      this._process(consumed.value);\n      this._current = consumed.next;\n    }\n    this._last = null;\n  }\n}\n\nconst STATE_DELIMITER = '.';\nconst TARGETLESS_KEY = '';\nconst NULL_EVENT = '';\nconst STATE_IDENTIFIER = '#';\nconst WILDCARD = '*';\nconst XSTATE_INIT = 'xstate.init';\nconst XSTATE_ERROR = 'xstate.error';\nconst XSTATE_STOP = 'xstate.stop';\n\n/**\n * Returns an event that represents an implicit event that is sent after the\n * specified `delay`.\n *\n * @param delayRef The delay in milliseconds\n * @param id The state node ID where this event is handled\n */\nfunction createAfterEvent(delayRef, id) {\n  return {\n    type: `xstate.after.${delayRef}.${id}`\n  };\n}\n\n/**\n * Returns an event that represents that a final state node has been reached in\n * the parent state node.\n *\n * @param id The final state node's parent state node `id`\n * @param output The data to pass into the event\n */\nfunction createDoneStateEvent(id, output) {\n  return {\n    type: `xstate.done.state.${id}`,\n    output\n  };\n}\n\n/**\n * Returns an event that represents that an invoked service has terminated.\n *\n * An invoked service is terminated when it has reached a top-level final state\n * node, but not when it is canceled.\n *\n * @param invokeId The invoked service ID\n * @param output The data to pass into the event\n */\nfunction createDoneActorEvent(invokeId, output) {\n  return {\n    type: `xstate.done.actor.${invokeId}`,\n    output,\n    actorId: invokeId\n  };\n}\nfunction createErrorActorEvent(id, error) {\n  return {\n    type: `xstate.error.actor.${id}`,\n    error,\n    actorId: id\n  };\n}\nfunction createInitEvent(input) {\n  return {\n    type: XSTATE_INIT,\n    input\n  };\n}\n\n/**\n * This function makes sure that unhandled errors are thrown in a separate\n * macrotask. It allows those errors to be detected by global error handlers and\n * reported to bug tracking services without interrupting our own stack of\n * execution.\n *\n * @param err Error to be thrown\n */\nfunction reportUnhandledError(err) {\n  setTimeout(() => {\n    throw err;\n  });\n}\n\nconst symbolObservable = (() => typeof Symbol === 'function' && Symbol.observable || '@@observable')();\n\nfunction matchesState(parentStateId, childStateId) {\n  const parentStateValue = toStateValue(parentStateId);\n  const childStateValue = toStateValue(childStateId);\n  if (typeof childStateValue === 'string') {\n    if (typeof parentStateValue === 'string') {\n      return childStateValue === parentStateValue;\n    }\n\n    // Parent more specific than child\n    return false;\n  }\n  if (typeof parentStateValue === 'string') {\n    return parentStateValue in childStateValue;\n  }\n  return Object.keys(parentStateValue).every(key => {\n    if (!(key in childStateValue)) {\n      return false;\n    }\n    return matchesState(parentStateValue[key], childStateValue[key]);\n  });\n}\nfunction toStatePath(stateId) {\n  if (isArray(stateId)) {\n    return stateId;\n  }\n  const result = [];\n  let segment = '';\n  for (let i = 0; i < stateId.length; i++) {\n    const char = stateId.charCodeAt(i);\n    switch (char) {\n      // \\\n      case 92:\n        // consume the next character\n        segment += stateId[i + 1];\n        // and skip over it\n        i++;\n        continue;\n      // .\n      case 46:\n        result.push(segment);\n        segment = '';\n        continue;\n    }\n    segment += stateId[i];\n  }\n  result.push(segment);\n  return result;\n}\nfunction toStateValue(stateValue) {\n  if (isMachineSnapshot(stateValue)) {\n    return stateValue.value;\n  }\n  if (typeof stateValue !== 'string') {\n    return stateValue;\n  }\n  const statePath = toStatePath(stateValue);\n  return pathToStateValue(statePath);\n}\nfunction pathToStateValue(statePath) {\n  if (statePath.length === 1) {\n    return statePath[0];\n  }\n  const value = {};\n  let marker = value;\n  for (let i = 0; i < statePath.length - 1; i++) {\n    if (i === statePath.length - 2) {\n      marker[statePath[i]] = statePath[i + 1];\n    } else {\n      const previous = marker;\n      marker = {};\n      previous[statePath[i]] = marker;\n    }\n  }\n  return value;\n}\nfunction mapValues(collection, iteratee) {\n  const result = {};\n  const collectionKeys = Object.keys(collection);\n  for (let i = 0; i < collectionKeys.length; i++) {\n    const key = collectionKeys[i];\n    result[key] = iteratee(collection[key], key, collection, i);\n  }\n  return result;\n}\nfunction toArrayStrict(value) {\n  if (isArray(value)) {\n    return value;\n  }\n  return [value];\n}\nfunction toArray(value) {\n  if (value === undefined) {\n    return [];\n  }\n  return toArrayStrict(value);\n}\nfunction resolveOutput(mapper, context, event, self) {\n  if (typeof mapper === 'function') {\n    return mapper({\n      context,\n      event,\n      self\n    });\n  }\n  if (!!mapper && typeof mapper === 'object' && Object.values(mapper).some(val => typeof val === 'function')) {\n    console.warn(`Dynamically mapping values to individual properties is deprecated. Use a single function that returns the mapped object instead.\\nFound object containing properties whose values are possibly mapping functions: ${Object.entries(mapper).filter(([, value]) => typeof value === 'function').map(([key, value]) => `\\n - ${key}: ${value.toString().replace(/\\n\\s*/g, '')}`).join('')}`);\n  }\n  return mapper;\n}\nfunction isArray(value) {\n  return Array.isArray(value);\n}\nfunction isErrorActorEvent(event) {\n  return event.type.startsWith('xstate.error.actor');\n}\nfunction toTransitionConfigArray(configLike) {\n  return toArrayStrict(configLike).map(transitionLike => {\n    if (typeof transitionLike === 'undefined' || typeof transitionLike === 'string') {\n      return {\n        target: transitionLike\n      };\n    }\n    return transitionLike;\n  });\n}\nfunction normalizeTarget(target) {\n  if (target === undefined || target === TARGETLESS_KEY) {\n    return undefined;\n  }\n  return toArray(target);\n}\nfunction toObserver(nextHandler, errorHandler, completionHandler) {\n  const isObserver = typeof nextHandler === 'object';\n  const self = isObserver ? nextHandler : undefined;\n  return {\n    next: (isObserver ? nextHandler.next : nextHandler)?.bind(self),\n    error: (isObserver ? nextHandler.error : errorHandler)?.bind(self),\n    complete: (isObserver ? nextHandler.complete : completionHandler)?.bind(self)\n  };\n}\nfunction createInvokeId(stateNodeId, index) {\n  return `${index}.${stateNodeId}`;\n}\nfunction resolveReferencedActor(machine, src) {\n  const match = src.match(/^xstate\\.invoke\\.(\\d+)\\.(.*)/);\n  if (!match) {\n    return machine.implementations.actors[src];\n  }\n  const [, indexStr, nodeId] = match;\n  const node = machine.getStateNodeById(nodeId);\n  const invokeConfig = node.config.invoke;\n  return (Array.isArray(invokeConfig) ? invokeConfig[indexStr] : invokeConfig).src;\n}\nfunction getAllOwnEventDescriptors(snapshot) {\n  return [...new Set([...snapshot._nodes.flatMap(sn => sn.ownEvents)])];\n}\n\nfunction createScheduledEventId(actorRef, id) {\n  return `${actorRef.sessionId}.${id}`;\n}\nlet idCounter = 0;\nfunction createSystem(rootActor, options) {\n  const children = new Map();\n  const keyedActors = new Map();\n  const reverseKeyedActors = new WeakMap();\n  const inspectionObservers = new Set();\n  const timerMap = {};\n  const {\n    clock,\n    logger\n  } = options;\n  const scheduler = {\n    schedule: (source, target, event, delay, id = Math.random().toString(36).slice(2)) => {\n      const scheduledEvent = {\n        source,\n        target,\n        event,\n        delay,\n        id,\n        startedAt: Date.now()\n      };\n      const scheduledEventId = createScheduledEventId(source, id);\n      system._snapshot._scheduledEvents[scheduledEventId] = scheduledEvent;\n      const timeout = clock.setTimeout(() => {\n        delete timerMap[scheduledEventId];\n        delete system._snapshot._scheduledEvents[scheduledEventId];\n        system._relay(source, target, event);\n      }, delay);\n      timerMap[scheduledEventId] = timeout;\n    },\n    cancel: (source, id) => {\n      const scheduledEventId = createScheduledEventId(source, id);\n      const timeout = timerMap[scheduledEventId];\n      delete timerMap[scheduledEventId];\n      delete system._snapshot._scheduledEvents[scheduledEventId];\n      if (timeout !== undefined) {\n        clock.clearTimeout(timeout);\n      }\n    },\n    cancelAll: actorRef => {\n      for (const scheduledEventId in system._snapshot._scheduledEvents) {\n        const scheduledEvent = system._snapshot._scheduledEvents[scheduledEventId];\n        if (scheduledEvent.source === actorRef) {\n          scheduler.cancel(actorRef, scheduledEvent.id);\n        }\n      }\n    }\n  };\n  const sendInspectionEvent = event => {\n    if (!inspectionObservers.size) {\n      return;\n    }\n    const resolvedInspectionEvent = {\n      ...event,\n      rootId: rootActor.sessionId\n    };\n    inspectionObservers.forEach(observer => observer.next?.(resolvedInspectionEvent));\n  };\n  const system = {\n    _snapshot: {\n      _scheduledEvents: (options?.snapshot && options.snapshot.scheduler) ?? {}\n    },\n    _bookId: () => `x:${idCounter++}`,\n    _register: (sessionId, actorRef) => {\n      children.set(sessionId, actorRef);\n      return sessionId;\n    },\n    _unregister: actorRef => {\n      children.delete(actorRef.sessionId);\n      const systemId = reverseKeyedActors.get(actorRef);\n      if (systemId !== undefined) {\n        keyedActors.delete(systemId);\n        reverseKeyedActors.delete(actorRef);\n      }\n    },\n    get: systemId => {\n      return keyedActors.get(systemId);\n    },\n    _set: (systemId, actorRef) => {\n      const existing = keyedActors.get(systemId);\n      if (existing && existing !== actorRef) {\n        throw new Error(`Actor with system ID '${systemId}' already exists.`);\n      }\n      keyedActors.set(systemId, actorRef);\n      reverseKeyedActors.set(actorRef, systemId);\n    },\n    inspect: observerOrFn => {\n      const observer = toObserver(observerOrFn);\n      inspectionObservers.add(observer);\n      return {\n        unsubscribe() {\n          inspectionObservers.delete(observer);\n        }\n      };\n    },\n    _sendInspectionEvent: sendInspectionEvent,\n    _relay: (source, target, event) => {\n      system._sendInspectionEvent({\n        type: '@xstate.event',\n        sourceRef: source,\n        actorRef: target,\n        event\n      });\n      target._send(event);\n    },\n    scheduler,\n    getSnapshot: () => {\n      return {\n        _scheduledEvents: {\n          ...system._snapshot._scheduledEvents\n        }\n      };\n    },\n    start: () => {\n      const scheduledEvents = system._snapshot._scheduledEvents;\n      system._snapshot._scheduledEvents = {};\n      for (const scheduledId in scheduledEvents) {\n        const {\n          source,\n          target,\n          event,\n          delay,\n          id\n        } = scheduledEvents[scheduledId];\n        scheduler.schedule(source, target, event, delay, id);\n      }\n    },\n    _clock: clock,\n    _logger: logger\n  };\n  return system;\n}\n\n// those are needed to make JSDoc `@link` work properly\n\nlet executingCustomAction = false;\nconst $$ACTOR_TYPE = 1;\n\n// those values are currently used by @xstate/react directly so it's important to keep the assigned values in sync\nlet ProcessingStatus = /*#__PURE__*/function (ProcessingStatus) {\n  ProcessingStatus[ProcessingStatus[\"NotStarted\"] = 0] = \"NotStarted\";\n  ProcessingStatus[ProcessingStatus[\"Running\"] = 1] = \"Running\";\n  ProcessingStatus[ProcessingStatus[\"Stopped\"] = 2] = \"Stopped\";\n  return ProcessingStatus;\n}({});\nconst defaultOptions = {\n  clock: {\n    setTimeout: (fn, ms) => {\n      return setTimeout(fn, ms);\n    },\n    clearTimeout: id => {\n      return clearTimeout(id);\n    }\n  },\n  logger: console.log.bind(console),\n  devTools: false\n};\n\n/**\n * An Actor is a running process that can receive events, send events and change\n * its behavior based on the events it receives, which can cause effects outside\n * of the actor. When you run a state machine, it becomes an actor.\n */\nclass Actor {\n  /**\n   * Creates a new actor instance for the given logic with the provided options,\n   * if any.\n   *\n   * @param logic The logic to create an actor from\n   * @param options Actor options\n   */\n  constructor(logic, options) {\n    this.logic = logic;\n    /** The current internal state of the actor. */\n    this._snapshot = void 0;\n    /**\n     * The clock that is responsible for setting and clearing timeouts, such as\n     * delayed events and transitions.\n     */\n    this.clock = void 0;\n    this.options = void 0;\n    /** The unique identifier for this actor relative to its parent. */\n    this.id = void 0;\n    this.mailbox = new Mailbox(this._process.bind(this));\n    this.observers = new Set();\n    this.eventListeners = new Map();\n    this.logger = void 0;\n    /** @internal */\n    this._processingStatus = ProcessingStatus.NotStarted;\n    // Actor Ref\n    this._parent = void 0;\n    /** @internal */\n    this._syncSnapshot = void 0;\n    this.ref = void 0;\n    // TODO: add typings for system\n    this._actorScope = void 0;\n    this._systemId = void 0;\n    /** The globally unique process ID for this invocation. */\n    this.sessionId = void 0;\n    /** The system to which this actor belongs. */\n    this.system = void 0;\n    this._doneEvent = void 0;\n    this.src = void 0;\n    // array of functions to defer\n    this._deferred = [];\n    const resolvedOptions = {\n      ...defaultOptions,\n      ...options\n    };\n    const {\n      clock,\n      logger,\n      parent,\n      syncSnapshot,\n      id,\n      systemId,\n      inspect\n    } = resolvedOptions;\n    this.system = parent ? parent.system : createSystem(this, {\n      clock,\n      logger\n    });\n    if (inspect && !parent) {\n      // Always inspect at the system-level\n      this.system.inspect(toObserver(inspect));\n    }\n    this.sessionId = this.system._bookId();\n    this.id = id ?? this.sessionId;\n    this.logger = options?.logger ?? this.system._logger;\n    this.clock = options?.clock ?? this.system._clock;\n    this._parent = parent;\n    this._syncSnapshot = syncSnapshot;\n    this.options = resolvedOptions;\n    this.src = resolvedOptions.src ?? logic;\n    this.ref = this;\n    this._actorScope = {\n      self: this,\n      id: this.id,\n      sessionId: this.sessionId,\n      logger: this.logger,\n      defer: fn => {\n        this._deferred.push(fn);\n      },\n      system: this.system,\n      stopChild: child => {\n        if (child._parent !== this) {\n          throw new Error(`Cannot stop child actor ${child.id} of ${this.id} because it is not a child`);\n        }\n        child._stop();\n      },\n      emit: emittedEvent => {\n        const listeners = this.eventListeners.get(emittedEvent.type);\n        const wildcardListener = this.eventListeners.get('*');\n        if (!listeners && !wildcardListener) {\n          return;\n        }\n        const allListeners = [...(listeners ? listeners.values() : []), ...(wildcardListener ? wildcardListener.values() : [])];\n        for (const handler of allListeners) {\n          handler(emittedEvent);\n        }\n      },\n      actionExecutor: action => {\n        const exec = () => {\n          this._actorScope.system._sendInspectionEvent({\n            type: '@xstate.action',\n            actorRef: this,\n            action: {\n              type: action.type,\n              params: action.params\n            }\n          });\n          if (!action.exec) {\n            return;\n          }\n          const saveExecutingCustomAction = executingCustomAction;\n          try {\n            executingCustomAction = true;\n            action.exec(action.info, action.params);\n          } finally {\n            executingCustomAction = saveExecutingCustomAction;\n          }\n        };\n        if (this._processingStatus === ProcessingStatus.Running) {\n          exec();\n        } else {\n          this._deferred.push(exec);\n        }\n      }\n    };\n\n    // Ensure that the send method is bound to this Actor instance\n    // if destructured\n    this.send = this.send.bind(this);\n    this.system._sendInspectionEvent({\n      type: '@xstate.actor',\n      actorRef: this\n    });\n    if (systemId) {\n      this._systemId = systemId;\n      this.system._set(systemId, this);\n    }\n    this._initState(options?.snapshot ?? options?.state);\n    if (systemId && this._snapshot.status !== 'active') {\n      this.system._unregister(this);\n    }\n  }\n  _initState(persistedState) {\n    try {\n      this._snapshot = persistedState ? this.logic.restoreSnapshot ? this.logic.restoreSnapshot(persistedState, this._actorScope) : persistedState : this.logic.getInitialSnapshot(this._actorScope, this.options?.input);\n    } catch (err) {\n      // if we get here then it means that we assign a value to this._snapshot that is not of the correct type\n      // we can't get the true `TSnapshot & { status: 'error'; }`, it's impossible\n      // so right now this is a lie of sorts\n      this._snapshot = {\n        status: 'error',\n        output: undefined,\n        error: err\n      };\n    }\n  }\n  update(snapshot, event) {\n    // Update state\n    this._snapshot = snapshot;\n\n    // Execute deferred effects\n    let deferredFn;\n    while (deferredFn = this._deferred.shift()) {\n      try {\n        deferredFn();\n      } catch (err) {\n        // this error can only be caught when executing *initial* actions\n        // it's the only time when we call actions provided by the user through those deferreds\n        // when the actor is already running we always execute them synchronously while transitioning\n        // no \"builtin deferred\" should actually throw an error since they are either safe\n        // or the control flow is passed through the mailbox and errors should be caught by the `_process` used by the mailbox\n        this._deferred.length = 0;\n        this._snapshot = {\n          ...snapshot,\n          status: 'error',\n          error: err\n        };\n      }\n    }\n    switch (this._snapshot.status) {\n      case 'active':\n        for (const observer of this.observers) {\n          try {\n            observer.next?.(snapshot);\n          } catch (err) {\n            reportUnhandledError(err);\n          }\n        }\n        break;\n      case 'done':\n        // next observers are meant to be notified about done snapshots\n        // this can be seen as something that is different from how observable work\n        // but with observables `complete` callback is called without any arguments\n        // it's more ergonomic for XState to treat a done snapshot as a \"next\" value\n        // and the completion event as something that is separate,\n        // something that merely follows emitting that done snapshot\n        for (const observer of this.observers) {\n          try {\n            observer.next?.(snapshot);\n          } catch (err) {\n            reportUnhandledError(err);\n          }\n        }\n        this._stopProcedure();\n        this._complete();\n        this._doneEvent = createDoneActorEvent(this.id, this._snapshot.output);\n        if (this._parent) {\n          this.system._relay(this, this._parent, this._doneEvent);\n        }\n        break;\n      case 'error':\n        this._error(this._snapshot.error);\n        break;\n    }\n    this.system._sendInspectionEvent({\n      type: '@xstate.snapshot',\n      actorRef: this,\n      event,\n      snapshot\n    });\n  }\n\n  /**\n   * Subscribe an observer to an actor’s snapshot values.\n   *\n   * @remarks\n   * The observer will receive the actor’s snapshot value when it is emitted.\n   * The observer can be:\n   *\n   * - A plain function that receives the latest snapshot, or\n   * - An observer object whose `.next(snapshot)` method receives the latest\n   *   snapshot\n   *\n   * @example\n   *\n   * ```ts\n   * // Observer as a plain function\n   * const subscription = actor.subscribe((snapshot) => {\n   *   console.log(snapshot);\n   * });\n   * ```\n   *\n   * @example\n   *\n   * ```ts\n   * // Observer as an object\n   * const subscription = actor.subscribe({\n   *   next(snapshot) {\n   *     console.log(snapshot);\n   *   },\n   *   error(err) {\n   *     // ...\n   *   },\n   *   complete() {\n   *     // ...\n   *   }\n   * });\n   * ```\n   *\n   * The return value of `actor.subscribe(observer)` is a subscription object\n   * that has an `.unsubscribe()` method. You can call\n   * `subscription.unsubscribe()` to unsubscribe the observer:\n   *\n   * @example\n   *\n   * ```ts\n   * const subscription = actor.subscribe((snapshot) => {\n   *   // ...\n   * });\n   *\n   * // Unsubscribe the observer\n   * subscription.unsubscribe();\n   * ```\n   *\n   * When the actor is stopped, all of its observers will automatically be\n   * unsubscribed.\n   *\n   * @param observer - Either a plain function that receives the latest\n   *   snapshot, or an observer object whose `.next(snapshot)` method receives\n   *   the latest snapshot\n   */\n\n  subscribe(nextListenerOrObserver, errorListener, completeListener) {\n    const observer = toObserver(nextListenerOrObserver, errorListener, completeListener);\n    if (this._processingStatus !== ProcessingStatus.Stopped) {\n      this.observers.add(observer);\n    } else {\n      switch (this._snapshot.status) {\n        case 'done':\n          try {\n            observer.complete?.();\n          } catch (err) {\n            reportUnhandledError(err);\n          }\n          break;\n        case 'error':\n          {\n            const err = this._snapshot.error;\n            if (!observer.error) {\n              reportUnhandledError(err);\n            } else {\n              try {\n                observer.error(err);\n              } catch (err) {\n                reportUnhandledError(err);\n              }\n            }\n            break;\n          }\n      }\n    }\n    return {\n      unsubscribe: () => {\n        this.observers.delete(observer);\n      }\n    };\n  }\n  on(type, handler) {\n    let listeners = this.eventListeners.get(type);\n    if (!listeners) {\n      listeners = new Set();\n      this.eventListeners.set(type, listeners);\n    }\n    const wrappedHandler = handler.bind(undefined);\n    listeners.add(wrappedHandler);\n    return {\n      unsubscribe: () => {\n        listeners.delete(wrappedHandler);\n      }\n    };\n  }\n\n  /** Starts the Actor from the initial state */\n  start() {\n    if (this._processingStatus === ProcessingStatus.Running) {\n      // Do not restart the service if it is already started\n      return this;\n    }\n    if (this._syncSnapshot) {\n      this.subscribe({\n        next: snapshot => {\n          if (snapshot.status === 'active') {\n            this.system._relay(this, this._parent, {\n              type: `xstate.snapshot.${this.id}`,\n              snapshot\n            });\n          }\n        },\n        error: () => {}\n      });\n    }\n    this.system._register(this.sessionId, this);\n    if (this._systemId) {\n      this.system._set(this._systemId, this);\n    }\n    this._processingStatus = ProcessingStatus.Running;\n\n    // TODO: this isn't correct when rehydrating\n    const initEvent = createInitEvent(this.options.input);\n    this.system._sendInspectionEvent({\n      type: '@xstate.event',\n      sourceRef: this._parent,\n      actorRef: this,\n      event: initEvent\n    });\n    const status = this._snapshot.status;\n    switch (status) {\n      case 'done':\n        // a state machine can be \"done\" upon initialization (it could reach a final state using initial microsteps)\n        // we still need to complete observers, flush deferreds etc\n        this.update(this._snapshot, initEvent);\n        // TODO: rethink cleanup of observers, mailbox, etc\n        return this;\n      case 'error':\n        this._error(this._snapshot.error);\n        return this;\n    }\n    if (!this._parent) {\n      this.system.start();\n    }\n    if (this.logic.start) {\n      try {\n        this.logic.start(this._snapshot, this._actorScope);\n      } catch (err) {\n        this._snapshot = {\n          ...this._snapshot,\n          status: 'error',\n          error: err\n        };\n        this._error(err);\n        return this;\n      }\n    }\n\n    // TODO: this notifies all subscribers but usually this is redundant\n    // there is no real change happening here\n    // we need to rethink if this needs to be refactored\n    this.update(this._snapshot, initEvent);\n    if (this.options.devTools) {\n      this.attachDevTools();\n    }\n    this.mailbox.start();\n    return this;\n  }\n  _process(event) {\n    let nextState;\n    let caughtError;\n    try {\n      nextState = this.logic.transition(this._snapshot, event, this._actorScope);\n    } catch (err) {\n      // we wrap it in a box so we can rethrow it later even if falsy value gets caught here\n      caughtError = {\n        err\n      };\n    }\n    if (caughtError) {\n      const {\n        err\n      } = caughtError;\n      this._snapshot = {\n        ...this._snapshot,\n        status: 'error',\n        error: err\n      };\n      this._error(err);\n      return;\n    }\n    this.update(nextState, event);\n    if (event.type === XSTATE_STOP) {\n      this._stopProcedure();\n      this._complete();\n    }\n  }\n  _stop() {\n    if (this._processingStatus === ProcessingStatus.Stopped) {\n      return this;\n    }\n    this.mailbox.clear();\n    if (this._processingStatus === ProcessingStatus.NotStarted) {\n      this._processingStatus = ProcessingStatus.Stopped;\n      return this;\n    }\n    this.mailbox.enqueue({\n      type: XSTATE_STOP\n    });\n    return this;\n  }\n\n  /** Stops the Actor and unsubscribe all listeners. */\n  stop() {\n    if (this._parent) {\n      throw new Error('A non-root actor cannot be stopped directly.');\n    }\n    return this._stop();\n  }\n  _complete() {\n    for (const observer of this.observers) {\n      try {\n        observer.complete?.();\n      } catch (err) {\n        reportUnhandledError(err);\n      }\n    }\n    this.observers.clear();\n  }\n  _reportError(err) {\n    if (!this.observers.size) {\n      if (!this._parent) {\n        reportUnhandledError(err);\n      }\n      return;\n    }\n    let reportError = false;\n    for (const observer of this.observers) {\n      const errorListener = observer.error;\n      reportError ||= !errorListener;\n      try {\n        errorListener?.(err);\n      } catch (err2) {\n        reportUnhandledError(err2);\n      }\n    }\n    this.observers.clear();\n    if (reportError) {\n      reportUnhandledError(err);\n    }\n  }\n  _error(err) {\n    this._stopProcedure();\n    this._reportError(err);\n    if (this._parent) {\n      this.system._relay(this, this._parent, createErrorActorEvent(this.id, err));\n    }\n  }\n  // TODO: atm children don't belong entirely to the actor so\n  // in a way - it's not even super aware of them\n  // so we can't stop them from here but we really should!\n  // right now, they are being stopped within the machine's transition\n  // but that could throw and leave us with \"orphaned\" active actors\n  _stopProcedure() {\n    if (this._processingStatus !== ProcessingStatus.Running) {\n      // Actor already stopped; do nothing\n      return this;\n    }\n\n    // Cancel all delayed events\n    this.system.scheduler.cancelAll(this);\n\n    // TODO: mailbox.reset\n    this.mailbox.clear();\n    // TODO: after `stop` we must prepare ourselves for receiving events again\n    // events sent *after* stop signal must be queued\n    // it seems like this should be the common behavior for all of our consumers\n    // so perhaps this should be unified somehow for all of them\n    this.mailbox = new Mailbox(this._process.bind(this));\n    this._processingStatus = ProcessingStatus.Stopped;\n    this.system._unregister(this);\n    return this;\n  }\n\n  /** @internal */\n  _send(event) {\n    if (this._processingStatus === ProcessingStatus.Stopped) {\n      // do nothing\n      {\n        const eventString = JSON.stringify(event);\n        console.warn(`Event \"${event.type}\" was sent to stopped actor \"${this.id} (${this.sessionId})\". This actor has already reached its final state, and will not transition.\\nEvent: ${eventString}`);\n      }\n      return;\n    }\n    this.mailbox.enqueue(event);\n  }\n\n  /**\n   * Sends an event to the running Actor to trigger a transition.\n   *\n   * @param event The event to send\n   */\n  send(event) {\n    if (typeof event === 'string') {\n      throw new Error(`Only event objects may be sent to actors; use .send({ type: \"${event}\" }) instead`);\n    }\n    this.system._relay(undefined, this, event);\n  }\n  attachDevTools() {\n    const {\n      devTools\n    } = this.options;\n    if (devTools) {\n      const resolvedDevToolsAdapter = typeof devTools === 'function' ? devTools : _dev_dist_xstate_dev_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.devToolsAdapter;\n      resolvedDevToolsAdapter(this);\n    }\n  }\n  toJSON() {\n    return {\n      xstate$$type: $$ACTOR_TYPE,\n      id: this.id\n    };\n  }\n\n  /**\n   * Obtain the internal state of the actor, which can be persisted.\n   *\n   * @remarks\n   * The internal state can be persisted from any actor, not only machines.\n   *\n   * Note that the persisted state is not the same as the snapshot from\n   * {@link Actor.getSnapshot}. Persisted state represents the internal state of\n   * the actor, while snapshots represent the actor's last emitted value.\n   *\n   * Can be restored with {@link ActorOptions.state}\n   * @see https://stately.ai/docs/persistence\n   */\n\n  getPersistedSnapshot(options) {\n    return this.logic.getPersistedSnapshot(this._snapshot, options);\n  }\n  [symbolObservable]() {\n    return this;\n  }\n\n  /**\n   * Read an actor’s snapshot synchronously.\n   *\n   * @remarks\n   * The snapshot represent an actor's last emitted value.\n   *\n   * When an actor receives an event, its internal state may change. An actor\n   * may emit a snapshot when a state transition occurs.\n   *\n   * Note that some actors, such as callback actors generated with\n   * `fromCallback`, will not emit snapshots.\n   * @see {@link Actor.subscribe} to subscribe to an actor’s snapshot values.\n   * @see {@link Actor.getPersistedSnapshot} to persist the internal state of an actor (which is more than just a snapshot).\n   */\n  getSnapshot() {\n    if (!this._snapshot) {\n      throw new Error(`Snapshot can't be read while the actor initializes itself`);\n    }\n    return this._snapshot;\n  }\n}\n/**\n * Creates a new actor instance for the given actor logic with the provided\n * options, if any.\n *\n * @remarks\n * When you create an actor from actor logic via `createActor(logic)`, you\n * implicitly create an actor system where the created actor is the root actor.\n * Any actors spawned from this root actor and its descendants are part of that\n * actor system.\n * @example\n *\n * ```ts\n * import { createActor } from 'xstate';\n * import { someActorLogic } from './someActorLogic.ts';\n *\n * // Creating the actor, which implicitly creates an actor system with itself as the root actor\n * const actor = createActor(someActorLogic);\n *\n * actor.subscribe((snapshot) => {\n *   console.log(snapshot);\n * });\n *\n * // Actors must be started by calling `actor.start()`, which will also start the actor system.\n * actor.start();\n *\n * // Actors can receive events\n * actor.send({ type: 'someEvent' });\n *\n * // You can stop root actors by calling `actor.stop()`, which will also stop the actor system and all actors in that system.\n * actor.stop();\n * ```\n *\n * @param logic - The actor logic to create an actor from. For a state machine\n *   actor logic creator, see {@link createMachine}. Other actor logic creators\n *   include {@link fromCallback}, {@link fromEventObservable},\n *   {@link fromObservable}, {@link fromPromise}, and {@link fromTransition}.\n * @param options - Actor options\n */\nfunction createActor(logic, ...[options]) {\n  return new Actor(logic, options);\n}\n\n/**\n * Creates a new Interpreter instance for the given machine with the provided\n * options, if any.\n *\n * @deprecated Use `createActor` instead\n * @alias\n */\nconst interpret = createActor;\n\n/**\n * @deprecated Use `Actor` instead.\n * @alias\n */\n\nfunction resolveCancel(_, snapshot, actionArgs, actionParams, {\n  sendId\n}) {\n  const resolvedSendId = typeof sendId === 'function' ? sendId(actionArgs, actionParams) : sendId;\n  return [snapshot, {\n    sendId: resolvedSendId\n  }, undefined];\n}\nfunction executeCancel(actorScope, params) {\n  actorScope.defer(() => {\n    actorScope.system.scheduler.cancel(actorScope.self, params.sendId);\n  });\n}\n/**\n * Cancels a delayed `sendTo(...)` action that is waiting to be executed. The\n * canceled `sendTo(...)` action will not send its event or execute, unless the\n * `delay` has already elapsed before `cancel(...)` is called.\n *\n * @example\n *\n * ```ts\n * import { createMachine, sendTo, cancel } from 'xstate';\n *\n * const machine = createMachine({\n *   // ...\n *   on: {\n *     sendEvent: {\n *       actions: sendTo(\n *         'some-actor',\n *         { type: 'someEvent' },\n *         {\n *           id: 'some-id',\n *           delay: 1000\n *         }\n *       )\n *     },\n *     cancelEvent: {\n *       actions: cancel('some-id')\n *     }\n *   }\n * });\n * ```\n *\n * @param sendId The `id` of the `sendTo(...)` action to cancel.\n */\nfunction cancel(sendId) {\n  function cancel(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  cancel.type = 'xstate.cancel';\n  cancel.sendId = sendId;\n  cancel.resolve = resolveCancel;\n  cancel.execute = executeCancel;\n  return cancel;\n}\n\nfunction resolveSpawn(actorScope, snapshot, actionArgs, _actionParams, {\n  id,\n  systemId,\n  src,\n  input,\n  syncSnapshot\n}) {\n  const logic = typeof src === 'string' ? resolveReferencedActor(snapshot.machine, src) : src;\n  const resolvedId = typeof id === 'function' ? id(actionArgs) : id;\n  let actorRef;\n  let resolvedInput = undefined;\n  if (logic) {\n    resolvedInput = typeof input === 'function' ? input({\n      context: snapshot.context,\n      event: actionArgs.event,\n      self: actorScope.self\n    }) : input;\n    actorRef = createActor(logic, {\n      id: resolvedId,\n      src,\n      parent: actorScope.self,\n      syncSnapshot,\n      systemId,\n      input: resolvedInput\n    });\n  }\n  if (!actorRef) {\n    console.warn(\n    // eslint-disable-next-line @typescript-eslint/restrict-template-expressions,@typescript-eslint/no-base-to-string\n    `Actor type '${src}' not found in machine '${actorScope.id}'.`);\n  }\n  return [cloneMachineSnapshot(snapshot, {\n    children: {\n      ...snapshot.children,\n      [resolvedId]: actorRef\n    }\n  }), {\n    id,\n    systemId,\n    actorRef,\n    src,\n    input: resolvedInput\n  }, undefined];\n}\nfunction executeSpawn(actorScope, {\n  actorRef\n}) {\n  if (!actorRef) {\n    return;\n  }\n  actorScope.defer(() => {\n    if (actorRef._processingStatus === ProcessingStatus.Stopped) {\n      return;\n    }\n    actorRef.start();\n  });\n}\nfunction spawnChild(...[src, {\n  id,\n  systemId,\n  input,\n  syncSnapshot = false\n} = {}]) {\n  function spawnChild(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  spawnChild.type = 'xstate.spawnChild';\n  spawnChild.id = id;\n  spawnChild.systemId = systemId;\n  spawnChild.src = src;\n  spawnChild.input = input;\n  spawnChild.syncSnapshot = syncSnapshot;\n  spawnChild.resolve = resolveSpawn;\n  spawnChild.execute = executeSpawn;\n  return spawnChild;\n}\n\nfunction resolveStop(_, snapshot, args, actionParams, {\n  actorRef\n}) {\n  const actorRefOrString = typeof actorRef === 'function' ? actorRef(args, actionParams) : actorRef;\n  const resolvedActorRef = typeof actorRefOrString === 'string' ? snapshot.children[actorRefOrString] : actorRefOrString;\n  let children = snapshot.children;\n  if (resolvedActorRef) {\n    children = {\n      ...children\n    };\n    delete children[resolvedActorRef.id];\n  }\n  return [cloneMachineSnapshot(snapshot, {\n    children\n  }), resolvedActorRef, undefined];\n}\nfunction executeStop(actorScope, actorRef) {\n  if (!actorRef) {\n    return;\n  }\n\n  // we need to eagerly unregister it here so a new actor with the same systemId can be registered immediately\n  // since we defer actual stopping of the actor but we don't defer actor creations (and we can't do that)\n  // this could throw on `systemId` collision, for example, when dealing with reentering transitions\n  actorScope.system._unregister(actorRef);\n\n  // this allows us to prevent an actor from being started if it gets stopped within the same macrostep\n  // this can happen, for example, when the invoking state is being exited immediately by an always transition\n  if (actorRef._processingStatus !== ProcessingStatus.Running) {\n    actorScope.stopChild(actorRef);\n    return;\n  }\n  // stopping a child enqueues a stop event in the child actor's mailbox\n  // we need for all of the already enqueued events to be processed before we stop the child\n  // the parent itself might want to send some events to a child (for example from exit actions on the invoking state)\n  // and we don't want to ignore those events\n  actorScope.defer(() => {\n    actorScope.stopChild(actorRef);\n  });\n}\n/**\n * Stops a child actor.\n *\n * @param actorRef The actor to stop.\n */\nfunction stopChild(actorRef) {\n  function stop(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  stop.type = 'xstate.stopChild';\n  stop.actorRef = actorRef;\n  stop.resolve = resolveStop;\n  stop.execute = executeStop;\n  return stop;\n}\n\n/**\n * Stops a child actor.\n *\n * @deprecated Use `stopChild(...)` instead\n * @alias\n */\nconst stop = stopChild;\n\nfunction checkStateIn(snapshot, _, {\n  stateValue\n}) {\n  if (typeof stateValue === 'string' && isStateId(stateValue)) {\n    const target = snapshot.machine.getStateNodeById(stateValue);\n    return snapshot._nodes.some(sn => sn === target);\n  }\n  return snapshot.matches(stateValue);\n}\nfunction stateIn(stateValue) {\n  function stateIn() {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  stateIn.check = checkStateIn;\n  stateIn.stateValue = stateValue;\n  return stateIn;\n}\nfunction checkNot(snapshot, {\n  context,\n  event\n}, {\n  guards\n}) {\n  return !evaluateGuard(guards[0], context, event, snapshot);\n}\n\n/**\n * Higher-order guard that evaluates to `true` if the `guard` passed to it\n * evaluates to `false`.\n *\n * @category Guards\n * @example\n *\n * ```ts\n * import { setup, not } from 'xstate';\n *\n * const machine = setup({\n *   guards: {\n *     someNamedGuard: () => false\n *   }\n * }).createMachine({\n *   on: {\n *     someEvent: {\n *       guard: not('someNamedGuard'),\n *       actions: () => {\n *         // will be executed if guard in `not(...)`\n *         // evaluates to `false`\n *       }\n *     }\n *   }\n * });\n * ```\n *\n * @returns A guard\n */\nfunction not(guard) {\n  function not(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  not.check = checkNot;\n  not.guards = [guard];\n  return not;\n}\nfunction checkAnd(snapshot, {\n  context,\n  event\n}, {\n  guards\n}) {\n  return guards.every(guard => evaluateGuard(guard, context, event, snapshot));\n}\n\n/**\n * Higher-order guard that evaluates to `true` if all `guards` passed to it\n * evaluate to `true`.\n *\n * @category Guards\n * @example\n *\n * ```ts\n * import { setup, and } from 'xstate';\n *\n * const machine = setup({\n *   guards: {\n *     someNamedGuard: () => true\n *   }\n * }).createMachine({\n *   on: {\n *     someEvent: {\n *       guard: and([({ context }) => context.value > 0, 'someNamedGuard']),\n *       actions: () => {\n *         // will be executed if all guards in `and(...)`\n *         // evaluate to true\n *       }\n *     }\n *   }\n * });\n * ```\n *\n * @returns A guard action object\n */\nfunction and(guards) {\n  function and(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  and.check = checkAnd;\n  and.guards = guards;\n  return and;\n}\nfunction checkOr(snapshot, {\n  context,\n  event\n}, {\n  guards\n}) {\n  return guards.some(guard => evaluateGuard(guard, context, event, snapshot));\n}\n\n/**\n * Higher-order guard that evaluates to `true` if any of the `guards` passed to\n * it evaluate to `true`.\n *\n * @category Guards\n * @example\n *\n * ```ts\n * import { setup, or } from 'xstate';\n *\n * const machine = setup({\n *   guards: {\n *     someNamedGuard: () => true\n *   }\n * }).createMachine({\n *   on: {\n *     someEvent: {\n *       guard: or([({ context }) => context.value > 0, 'someNamedGuard']),\n *       actions: () => {\n *         // will be executed if any of the guards in `or(...)`\n *         // evaluate to true\n *       }\n *     }\n *   }\n * });\n * ```\n *\n * @returns A guard action object\n */\nfunction or(guards) {\n  function or(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  or.check = checkOr;\n  or.guards = guards;\n  return or;\n}\n\n// TODO: throw on cycles (depth check should be enough)\nfunction evaluateGuard(guard, context, event, snapshot) {\n  const {\n    machine\n  } = snapshot;\n  const isInline = typeof guard === 'function';\n  const resolved = isInline ? guard : machine.implementations.guards[typeof guard === 'string' ? guard : guard.type];\n  if (!isInline && !resolved) {\n    throw new Error(`Guard '${typeof guard === 'string' ? guard : guard.type}' is not implemented.'.`);\n  }\n  if (typeof resolved !== 'function') {\n    return evaluateGuard(resolved, context, event, snapshot);\n  }\n  const guardArgs = {\n    context,\n    event\n  };\n  const guardParams = isInline || typeof guard === 'string' ? undefined : 'params' in guard ? typeof guard.params === 'function' ? guard.params({\n    context,\n    event\n  }) : guard.params : undefined;\n  if (!('check' in resolved)) {\n    // the existing type of `.guards` assumes non-nullable `TExpressionGuard`\n    // inline guards expect `TExpressionGuard` to be set to `undefined`\n    // it's fine to cast this here, our logic makes sure that we call those 2 \"variants\" correctly\n    return resolved(guardArgs, guardParams);\n  }\n  const builtinGuard = resolved;\n  return builtinGuard.check(snapshot, guardArgs, resolved // this holds all params\n  );\n}\n\nconst isAtomicStateNode = stateNode => stateNode.type === 'atomic' || stateNode.type === 'final';\nfunction getChildren(stateNode) {\n  return Object.values(stateNode.states).filter(sn => sn.type !== 'history');\n}\nfunction getProperAncestors(stateNode, toStateNode) {\n  const ancestors = [];\n  if (toStateNode === stateNode) {\n    return ancestors;\n  }\n\n  // add all ancestors\n  let m = stateNode.parent;\n  while (m && m !== toStateNode) {\n    ancestors.push(m);\n    m = m.parent;\n  }\n  return ancestors;\n}\nfunction getAllStateNodes(stateNodes) {\n  const nodeSet = new Set(stateNodes);\n  const adjList = getAdjList(nodeSet);\n\n  // add descendants\n  for (const s of nodeSet) {\n    // if previously active, add existing child nodes\n    if (s.type === 'compound' && (!adjList.get(s) || !adjList.get(s).length)) {\n      getInitialStateNodesWithTheirAncestors(s).forEach(sn => nodeSet.add(sn));\n    } else {\n      if (s.type === 'parallel') {\n        for (const child of getChildren(s)) {\n          if (child.type === 'history') {\n            continue;\n          }\n          if (!nodeSet.has(child)) {\n            const initialStates = getInitialStateNodesWithTheirAncestors(child);\n            for (const initialStateNode of initialStates) {\n              nodeSet.add(initialStateNode);\n            }\n          }\n        }\n      }\n    }\n  }\n\n  // add all ancestors\n  for (const s of nodeSet) {\n    let m = s.parent;\n    while (m) {\n      nodeSet.add(m);\n      m = m.parent;\n    }\n  }\n  return nodeSet;\n}\nfunction getValueFromAdj(baseNode, adjList) {\n  const childStateNodes = adjList.get(baseNode);\n  if (!childStateNodes) {\n    return {}; // todo: fix?\n  }\n  if (baseNode.type === 'compound') {\n    const childStateNode = childStateNodes[0];\n    if (childStateNode) {\n      if (isAtomicStateNode(childStateNode)) {\n        return childStateNode.key;\n      }\n    } else {\n      return {};\n    }\n  }\n  const stateValue = {};\n  for (const childStateNode of childStateNodes) {\n    stateValue[childStateNode.key] = getValueFromAdj(childStateNode, adjList);\n  }\n  return stateValue;\n}\nfunction getAdjList(stateNodes) {\n  const adjList = new Map();\n  for (const s of stateNodes) {\n    if (!adjList.has(s)) {\n      adjList.set(s, []);\n    }\n    if (s.parent) {\n      if (!adjList.has(s.parent)) {\n        adjList.set(s.parent, []);\n      }\n      adjList.get(s.parent).push(s);\n    }\n  }\n  return adjList;\n}\nfunction getStateValue(rootNode, stateNodes) {\n  const config = getAllStateNodes(stateNodes);\n  return getValueFromAdj(rootNode, getAdjList(config));\n}\nfunction isInFinalState(stateNodeSet, stateNode) {\n  if (stateNode.type === 'compound') {\n    return getChildren(stateNode).some(s => s.type === 'final' && stateNodeSet.has(s));\n  }\n  if (stateNode.type === 'parallel') {\n    return getChildren(stateNode).every(sn => isInFinalState(stateNodeSet, sn));\n  }\n  return stateNode.type === 'final';\n}\nconst isStateId = str => str[0] === STATE_IDENTIFIER;\nfunction getCandidates(stateNode, receivedEventType) {\n  const candidates = stateNode.transitions.get(receivedEventType) || [...stateNode.transitions.keys()].filter(eventDescriptor => {\n    // check if transition is a wildcard transition,\n    // which matches any non-transient events\n    if (eventDescriptor === WILDCARD) {\n      return true;\n    }\n    if (!eventDescriptor.endsWith('.*')) {\n      return false;\n    }\n    if (/.*\\*.+/.test(eventDescriptor)) {\n      console.warn(`Wildcards can only be the last token of an event descriptor (e.g., \"event.*\") or the entire event descriptor (\"*\"). Check the \"${eventDescriptor}\" event.`);\n    }\n    const partialEventTokens = eventDescriptor.split('.');\n    const eventTokens = receivedEventType.split('.');\n    for (let tokenIndex = 0; tokenIndex < partialEventTokens.length; tokenIndex++) {\n      const partialEventToken = partialEventTokens[tokenIndex];\n      const eventToken = eventTokens[tokenIndex];\n      if (partialEventToken === '*') {\n        const isLastToken = tokenIndex === partialEventTokens.length - 1;\n        if (!isLastToken) {\n          console.warn(`Infix wildcards in transition events are not allowed. Check the \"${eventDescriptor}\" transition.`);\n        }\n        return isLastToken;\n      }\n      if (partialEventToken !== eventToken) {\n        return false;\n      }\n    }\n    return true;\n  }).sort((a, b) => b.length - a.length).flatMap(key => stateNode.transitions.get(key));\n  return candidates;\n}\n\n/** All delayed transitions from the config. */\nfunction getDelayedTransitions(stateNode) {\n  const afterConfig = stateNode.config.after;\n  if (!afterConfig) {\n    return [];\n  }\n  const mutateEntryExit = delay => {\n    const afterEvent = createAfterEvent(delay, stateNode.id);\n    const eventType = afterEvent.type;\n    stateNode.entry.push(raise(afterEvent, {\n      id: eventType,\n      delay\n    }));\n    stateNode.exit.push(cancel(eventType));\n    return eventType;\n  };\n  const delayedTransitions = Object.keys(afterConfig).flatMap(delay => {\n    const configTransition = afterConfig[delay];\n    const resolvedTransition = typeof configTransition === 'string' ? {\n      target: configTransition\n    } : configTransition;\n    const resolvedDelay = Number.isNaN(+delay) ? delay : +delay;\n    const eventType = mutateEntryExit(resolvedDelay);\n    return toArray(resolvedTransition).map(transition => ({\n      ...transition,\n      event: eventType,\n      delay: resolvedDelay\n    }));\n  });\n  return delayedTransitions.map(delayedTransition => {\n    const {\n      delay\n    } = delayedTransition;\n    return {\n      ...formatTransition(stateNode, delayedTransition.event, delayedTransition),\n      delay\n    };\n  });\n}\nfunction formatTransition(stateNode, descriptor, transitionConfig) {\n  const normalizedTarget = normalizeTarget(transitionConfig.target);\n  const reenter = transitionConfig.reenter ?? false;\n  const target = resolveTarget(stateNode, normalizedTarget);\n\n  // TODO: should this be part of a lint rule instead?\n  if (transitionConfig.cond) {\n    throw new Error(`State \"${stateNode.id}\" has declared \\`cond\\` for one of its transitions. This property has been renamed to \\`guard\\`. Please update your code.`);\n  }\n  const transition = {\n    ...transitionConfig,\n    actions: toArray(transitionConfig.actions),\n    guard: transitionConfig.guard,\n    target,\n    source: stateNode,\n    reenter,\n    eventType: descriptor,\n    toJSON: () => ({\n      ...transition,\n      source: `#${stateNode.id}`,\n      target: target ? target.map(t => `#${t.id}`) : undefined\n    })\n  };\n  return transition;\n}\nfunction formatTransitions(stateNode) {\n  const transitions = new Map();\n  if (stateNode.config.on) {\n    for (const descriptor of Object.keys(stateNode.config.on)) {\n      if (descriptor === NULL_EVENT) {\n        throw new Error('Null events (\"\") cannot be specified as a transition key. Use `always: { ... }` instead.');\n      }\n      const transitionsConfig = stateNode.config.on[descriptor];\n      transitions.set(descriptor, toTransitionConfigArray(transitionsConfig).map(t => formatTransition(stateNode, descriptor, t)));\n    }\n  }\n  if (stateNode.config.onDone) {\n    const descriptor = `xstate.done.state.${stateNode.id}`;\n    transitions.set(descriptor, toTransitionConfigArray(stateNode.config.onDone).map(t => formatTransition(stateNode, descriptor, t)));\n  }\n  for (const invokeDef of stateNode.invoke) {\n    if (invokeDef.onDone) {\n      const descriptor = `xstate.done.actor.${invokeDef.id}`;\n      transitions.set(descriptor, toTransitionConfigArray(invokeDef.onDone).map(t => formatTransition(stateNode, descriptor, t)));\n    }\n    if (invokeDef.onError) {\n      const descriptor = `xstate.error.actor.${invokeDef.id}`;\n      transitions.set(descriptor, toTransitionConfigArray(invokeDef.onError).map(t => formatTransition(stateNode, descriptor, t)));\n    }\n    if (invokeDef.onSnapshot) {\n      const descriptor = `xstate.snapshot.${invokeDef.id}`;\n      transitions.set(descriptor, toTransitionConfigArray(invokeDef.onSnapshot).map(t => formatTransition(stateNode, descriptor, t)));\n    }\n  }\n  for (const delayedTransition of stateNode.after) {\n    let existing = transitions.get(delayedTransition.eventType);\n    if (!existing) {\n      existing = [];\n      transitions.set(delayedTransition.eventType, existing);\n    }\n    existing.push(delayedTransition);\n  }\n  return transitions;\n}\nfunction formatInitialTransition(stateNode, _target) {\n  const resolvedTarget = typeof _target === 'string' ? stateNode.states[_target] : _target ? stateNode.states[_target.target] : undefined;\n  if (!resolvedTarget && _target) {\n    throw new Error(\n    // eslint-disable-next-line @typescript-eslint/restrict-template-expressions, @typescript-eslint/no-base-to-string\n    `Initial state node \"${_target}\" not found on parent state node #${stateNode.id}`);\n  }\n  const transition = {\n    source: stateNode,\n    actions: !_target || typeof _target === 'string' ? [] : toArray(_target.actions),\n    eventType: null,\n    reenter: false,\n    target: resolvedTarget ? [resolvedTarget] : [],\n    toJSON: () => ({\n      ...transition,\n      source: `#${stateNode.id}`,\n      target: resolvedTarget ? [`#${resolvedTarget.id}`] : []\n    })\n  };\n  return transition;\n}\nfunction resolveTarget(stateNode, targets) {\n  if (targets === undefined) {\n    // an undefined target signals that the state node should not transition from that state when receiving that event\n    return undefined;\n  }\n  return targets.map(target => {\n    if (typeof target !== 'string') {\n      return target;\n    }\n    if (isStateId(target)) {\n      return stateNode.machine.getStateNodeById(target);\n    }\n    const isInternalTarget = target[0] === STATE_DELIMITER;\n    // If internal target is defined on machine,\n    // do not include machine key on target\n    if (isInternalTarget && !stateNode.parent) {\n      return getStateNodeByPath(stateNode, target.slice(1));\n    }\n    const resolvedTarget = isInternalTarget ? stateNode.key + target : target;\n    if (stateNode.parent) {\n      try {\n        const targetStateNode = getStateNodeByPath(stateNode.parent, resolvedTarget);\n        return targetStateNode;\n      } catch (err) {\n        throw new Error(`Invalid transition definition for state node '${stateNode.id}':\\n${err.message}`);\n      }\n    } else {\n      throw new Error(`Invalid target: \"${target}\" is not a valid target from the root node. Did you mean \".${target}\"?`);\n    }\n  });\n}\nfunction resolveHistoryDefaultTransition(stateNode) {\n  const normalizedTarget = normalizeTarget(stateNode.config.target);\n  if (!normalizedTarget) {\n    return stateNode.parent.initial;\n  }\n  return {\n    target: normalizedTarget.map(t => typeof t === 'string' ? getStateNodeByPath(stateNode.parent, t) : t)\n  };\n}\nfunction isHistoryNode(stateNode) {\n  return stateNode.type === 'history';\n}\nfunction getInitialStateNodesWithTheirAncestors(stateNode) {\n  const states = getInitialStateNodes(stateNode);\n  for (const initialState of states) {\n    for (const ancestor of getProperAncestors(initialState, stateNode)) {\n      states.add(ancestor);\n    }\n  }\n  return states;\n}\nfunction getInitialStateNodes(stateNode) {\n  const set = new Set();\n  function iter(descStateNode) {\n    if (set.has(descStateNode)) {\n      return;\n    }\n    set.add(descStateNode);\n    if (descStateNode.type === 'compound') {\n      iter(descStateNode.initial.target[0]);\n    } else if (descStateNode.type === 'parallel') {\n      for (const child of getChildren(descStateNode)) {\n        iter(child);\n      }\n    }\n  }\n  iter(stateNode);\n  return set;\n}\n/** Returns the child state node from its relative `stateKey`, or throws. */\nfunction getStateNode(stateNode, stateKey) {\n  if (isStateId(stateKey)) {\n    return stateNode.machine.getStateNodeById(stateKey);\n  }\n  if (!stateNode.states) {\n    throw new Error(`Unable to retrieve child state '${stateKey}' from '${stateNode.id}'; no child states exist.`);\n  }\n  const result = stateNode.states[stateKey];\n  if (!result) {\n    throw new Error(`Child state '${stateKey}' does not exist on '${stateNode.id}'`);\n  }\n  return result;\n}\n\n/**\n * Returns the relative state node from the given `statePath`, or throws.\n *\n * @param statePath The string or string array relative path to the state node.\n */\nfunction getStateNodeByPath(stateNode, statePath) {\n  if (typeof statePath === 'string' && isStateId(statePath)) {\n    try {\n      return stateNode.machine.getStateNodeById(statePath);\n    } catch {\n      // try individual paths\n      // throw e;\n    }\n  }\n  const arrayStatePath = toStatePath(statePath).slice();\n  let currentStateNode = stateNode;\n  while (arrayStatePath.length) {\n    const key = arrayStatePath.shift();\n    if (!key.length) {\n      break;\n    }\n    currentStateNode = getStateNode(currentStateNode, key);\n  }\n  return currentStateNode;\n}\n\n/**\n * Returns the state nodes represented by the current state value.\n *\n * @param stateValue The state value or State instance\n */\nfunction getStateNodes(stateNode, stateValue) {\n  if (typeof stateValue === 'string') {\n    const childStateNode = stateNode.states[stateValue];\n    if (!childStateNode) {\n      throw new Error(`State '${stateValue}' does not exist on '${stateNode.id}'`);\n    }\n    return [stateNode, childStateNode];\n  }\n  const childStateKeys = Object.keys(stateValue);\n  const childStateNodes = childStateKeys.map(subStateKey => getStateNode(stateNode, subStateKey)).filter(Boolean);\n  return [stateNode.machine.root, stateNode].concat(childStateNodes, childStateKeys.reduce((allSubStateNodes, subStateKey) => {\n    const subStateNode = getStateNode(stateNode, subStateKey);\n    if (!subStateNode) {\n      return allSubStateNodes;\n    }\n    const subStateNodes = getStateNodes(subStateNode, stateValue[subStateKey]);\n    return allSubStateNodes.concat(subStateNodes);\n  }, []));\n}\nfunction transitionAtomicNode(stateNode, stateValue, snapshot, event) {\n  const childStateNode = getStateNode(stateNode, stateValue);\n  const next = childStateNode.next(snapshot, event);\n  if (!next || !next.length) {\n    return stateNode.next(snapshot, event);\n  }\n  return next;\n}\nfunction transitionCompoundNode(stateNode, stateValue, snapshot, event) {\n  const subStateKeys = Object.keys(stateValue);\n  const childStateNode = getStateNode(stateNode, subStateKeys[0]);\n  const next = transitionNode(childStateNode, stateValue[subStateKeys[0]], snapshot, event);\n  if (!next || !next.length) {\n    return stateNode.next(snapshot, event);\n  }\n  return next;\n}\nfunction transitionParallelNode(stateNode, stateValue, snapshot, event) {\n  const allInnerTransitions = [];\n  for (const subStateKey of Object.keys(stateValue)) {\n    const subStateValue = stateValue[subStateKey];\n    if (!subStateValue) {\n      continue;\n    }\n    const subStateNode = getStateNode(stateNode, subStateKey);\n    const innerTransitions = transitionNode(subStateNode, subStateValue, snapshot, event);\n    if (innerTransitions) {\n      allInnerTransitions.push(...innerTransitions);\n    }\n  }\n  if (!allInnerTransitions.length) {\n    return stateNode.next(snapshot, event);\n  }\n  return allInnerTransitions;\n}\nfunction transitionNode(stateNode, stateValue, snapshot, event) {\n  // leaf node\n  if (typeof stateValue === 'string') {\n    return transitionAtomicNode(stateNode, stateValue, snapshot, event);\n  }\n\n  // compound node\n  if (Object.keys(stateValue).length === 1) {\n    return transitionCompoundNode(stateNode, stateValue, snapshot, event);\n  }\n\n  // parallel node\n  return transitionParallelNode(stateNode, stateValue, snapshot, event);\n}\nfunction getHistoryNodes(stateNode) {\n  return Object.keys(stateNode.states).map(key => stateNode.states[key]).filter(sn => sn.type === 'history');\n}\nfunction isDescendant(childStateNode, parentStateNode) {\n  let marker = childStateNode;\n  while (marker.parent && marker.parent !== parentStateNode) {\n    marker = marker.parent;\n  }\n  return marker.parent === parentStateNode;\n}\nfunction hasIntersection(s1, s2) {\n  const set1 = new Set(s1);\n  const set2 = new Set(s2);\n  for (const item of set1) {\n    if (set2.has(item)) {\n      return true;\n    }\n  }\n  for (const item of set2) {\n    if (set1.has(item)) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction removeConflictingTransitions(enabledTransitions, stateNodeSet, historyValue) {\n  const filteredTransitions = new Set();\n  for (const t1 of enabledTransitions) {\n    let t1Preempted = false;\n    const transitionsToRemove = new Set();\n    for (const t2 of filteredTransitions) {\n      if (hasIntersection(computeExitSet([t1], stateNodeSet, historyValue), computeExitSet([t2], stateNodeSet, historyValue))) {\n        if (isDescendant(t1.source, t2.source)) {\n          transitionsToRemove.add(t2);\n        } else {\n          t1Preempted = true;\n          break;\n        }\n      }\n    }\n    if (!t1Preempted) {\n      for (const t3 of transitionsToRemove) {\n        filteredTransitions.delete(t3);\n      }\n      filteredTransitions.add(t1);\n    }\n  }\n  return Array.from(filteredTransitions);\n}\nfunction findLeastCommonAncestor(stateNodes) {\n  const [head, ...tail] = stateNodes;\n  for (const ancestor of getProperAncestors(head, undefined)) {\n    if (tail.every(sn => isDescendant(sn, ancestor))) {\n      return ancestor;\n    }\n  }\n}\nfunction getEffectiveTargetStates(transition, historyValue) {\n  if (!transition.target) {\n    return [];\n  }\n  const targets = new Set();\n  for (const targetNode of transition.target) {\n    if (isHistoryNode(targetNode)) {\n      if (historyValue[targetNode.id]) {\n        for (const node of historyValue[targetNode.id]) {\n          targets.add(node);\n        }\n      } else {\n        for (const node of getEffectiveTargetStates(resolveHistoryDefaultTransition(targetNode), historyValue)) {\n          targets.add(node);\n        }\n      }\n    } else {\n      targets.add(targetNode);\n    }\n  }\n  return [...targets];\n}\nfunction getTransitionDomain(transition, historyValue) {\n  const targetStates = getEffectiveTargetStates(transition, historyValue);\n  if (!targetStates) {\n    return;\n  }\n  if (!transition.reenter && targetStates.every(target => target === transition.source || isDescendant(target, transition.source))) {\n    return transition.source;\n  }\n  const lca = findLeastCommonAncestor(targetStates.concat(transition.source));\n  if (lca) {\n    return lca;\n  }\n\n  // at this point we know that it's a root transition since LCA couldn't be found\n  if (transition.reenter) {\n    return;\n  }\n  return transition.source.machine.root;\n}\nfunction computeExitSet(transitions, stateNodeSet, historyValue) {\n  const statesToExit = new Set();\n  for (const t of transitions) {\n    if (t.target?.length) {\n      const domain = getTransitionDomain(t, historyValue);\n      if (t.reenter && t.source === domain) {\n        statesToExit.add(domain);\n      }\n      for (const stateNode of stateNodeSet) {\n        if (isDescendant(stateNode, domain)) {\n          statesToExit.add(stateNode);\n        }\n      }\n    }\n  }\n  return [...statesToExit];\n}\nfunction areStateNodeCollectionsEqual(prevStateNodes, nextStateNodeSet) {\n  if (prevStateNodes.length !== nextStateNodeSet.size) {\n    return false;\n  }\n  for (const node of prevStateNodes) {\n    if (!nextStateNodeSet.has(node)) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/** https://www.w3.org/TR/scxml/#microstepProcedure */\nfunction microstep(transitions, currentSnapshot, actorScope, event, isInitial, internalQueue) {\n  if (!transitions.length) {\n    return currentSnapshot;\n  }\n  const mutStateNodeSet = new Set(currentSnapshot._nodes);\n  let historyValue = currentSnapshot.historyValue;\n  const filteredTransitions = removeConflictingTransitions(transitions, mutStateNodeSet, historyValue);\n  let nextState = currentSnapshot;\n\n  // Exit states\n  if (!isInitial) {\n    [nextState, historyValue] = exitStates(nextState, event, actorScope, filteredTransitions, mutStateNodeSet, historyValue, internalQueue, actorScope.actionExecutor);\n  }\n\n  // Execute transition content\n  nextState = resolveActionsAndContext(nextState, event, actorScope, filteredTransitions.flatMap(t => t.actions), internalQueue, undefined);\n\n  // Enter states\n  nextState = enterStates(nextState, event, actorScope, filteredTransitions, mutStateNodeSet, internalQueue, historyValue, isInitial);\n  const nextStateNodes = [...mutStateNodeSet];\n  if (nextState.status === 'done') {\n    nextState = resolveActionsAndContext(nextState, event, actorScope, nextStateNodes.sort((a, b) => b.order - a.order).flatMap(state => state.exit), internalQueue, undefined);\n  }\n\n  // eslint-disable-next-line no-useless-catch\n  try {\n    if (historyValue === currentSnapshot.historyValue && areStateNodeCollectionsEqual(currentSnapshot._nodes, mutStateNodeSet)) {\n      return nextState;\n    }\n    return cloneMachineSnapshot(nextState, {\n      _nodes: nextStateNodes,\n      historyValue\n    });\n  } catch (e) {\n    // TODO: Refactor this once proper error handling is implemented.\n    // See https://github.com/statelyai/rfcs/pull/4\n    throw e;\n  }\n}\nfunction getMachineOutput(snapshot, event, actorScope, rootNode, rootCompletionNode) {\n  if (rootNode.output === undefined) {\n    return;\n  }\n  const doneStateEvent = createDoneStateEvent(rootCompletionNode.id, rootCompletionNode.output !== undefined && rootCompletionNode.parent ? resolveOutput(rootCompletionNode.output, snapshot.context, event, actorScope.self) : undefined);\n  return resolveOutput(rootNode.output, snapshot.context, doneStateEvent, actorScope.self);\n}\nfunction enterStates(currentSnapshot, event, actorScope, filteredTransitions, mutStateNodeSet, internalQueue, historyValue, isInitial) {\n  let nextSnapshot = currentSnapshot;\n  const statesToEnter = new Set();\n  // those are states that were directly targeted or indirectly targeted by the explicit target\n  // in other words, those are states for which initial actions should be executed\n  // when we target `#deep_child` initial actions of its ancestors shouldn't be executed\n  const statesForDefaultEntry = new Set();\n  computeEntrySet(filteredTransitions, historyValue, statesForDefaultEntry, statesToEnter);\n\n  // In the initial state, the root state node is \"entered\".\n  if (isInitial) {\n    statesForDefaultEntry.add(currentSnapshot.machine.root);\n  }\n  const completedNodes = new Set();\n  for (const stateNodeToEnter of [...statesToEnter].sort((a, b) => a.order - b.order)) {\n    mutStateNodeSet.add(stateNodeToEnter);\n    const actions = [];\n\n    // Add entry actions\n    actions.push(...stateNodeToEnter.entry);\n    for (const invokeDef of stateNodeToEnter.invoke) {\n      actions.push(spawnChild(invokeDef.src, {\n        ...invokeDef,\n        syncSnapshot: !!invokeDef.onSnapshot\n      }));\n    }\n    if (statesForDefaultEntry.has(stateNodeToEnter)) {\n      const initialActions = stateNodeToEnter.initial.actions;\n      actions.push(...initialActions);\n    }\n    nextSnapshot = resolveActionsAndContext(nextSnapshot, event, actorScope, actions, internalQueue, stateNodeToEnter.invoke.map(invokeDef => invokeDef.id));\n    if (stateNodeToEnter.type === 'final') {\n      const parent = stateNodeToEnter.parent;\n      let ancestorMarker = parent?.type === 'parallel' ? parent : parent?.parent;\n      let rootCompletionNode = ancestorMarker || stateNodeToEnter;\n      if (parent?.type === 'compound') {\n        internalQueue.push(createDoneStateEvent(parent.id, stateNodeToEnter.output !== undefined ? resolveOutput(stateNodeToEnter.output, nextSnapshot.context, event, actorScope.self) : undefined));\n      }\n      while (ancestorMarker?.type === 'parallel' && !completedNodes.has(ancestorMarker) && isInFinalState(mutStateNodeSet, ancestorMarker)) {\n        completedNodes.add(ancestorMarker);\n        internalQueue.push(createDoneStateEvent(ancestorMarker.id));\n        rootCompletionNode = ancestorMarker;\n        ancestorMarker = ancestorMarker.parent;\n      }\n      if (ancestorMarker) {\n        continue;\n      }\n      nextSnapshot = cloneMachineSnapshot(nextSnapshot, {\n        status: 'done',\n        output: getMachineOutput(nextSnapshot, event, actorScope, nextSnapshot.machine.root, rootCompletionNode)\n      });\n    }\n  }\n  return nextSnapshot;\n}\nfunction computeEntrySet(transitions, historyValue, statesForDefaultEntry, statesToEnter) {\n  for (const t of transitions) {\n    const domain = getTransitionDomain(t, historyValue);\n    for (const s of t.target || []) {\n      if (!isHistoryNode(s) && (\n      // if the target is different than the source then it will *definitely* be entered\n      t.source !== s ||\n      // we know that the domain can't lie within the source\n      // if it's different than the source then it's outside of it and it means that the target has to be entered as well\n      t.source !== domain ||\n      // reentering transitions always enter the target, even if it's the source itself\n      t.reenter)) {\n        statesToEnter.add(s);\n        statesForDefaultEntry.add(s);\n      }\n      addDescendantStatesToEnter(s, historyValue, statesForDefaultEntry, statesToEnter);\n    }\n    const targetStates = getEffectiveTargetStates(t, historyValue);\n    for (const s of targetStates) {\n      const ancestors = getProperAncestors(s, domain);\n      if (domain?.type === 'parallel') {\n        ancestors.push(domain);\n      }\n      addAncestorStatesToEnter(statesToEnter, historyValue, statesForDefaultEntry, ancestors, !t.source.parent && t.reenter ? undefined : domain);\n    }\n  }\n}\nfunction addDescendantStatesToEnter(stateNode, historyValue, statesForDefaultEntry, statesToEnter) {\n  if (isHistoryNode(stateNode)) {\n    if (historyValue[stateNode.id]) {\n      const historyStateNodes = historyValue[stateNode.id];\n      for (const s of historyStateNodes) {\n        statesToEnter.add(s);\n        addDescendantStatesToEnter(s, historyValue, statesForDefaultEntry, statesToEnter);\n      }\n      for (const s of historyStateNodes) {\n        addProperAncestorStatesToEnter(s, stateNode.parent, statesToEnter, historyValue, statesForDefaultEntry);\n      }\n    } else {\n      const historyDefaultTransition = resolveHistoryDefaultTransition(stateNode);\n      for (const s of historyDefaultTransition.target) {\n        statesToEnter.add(s);\n        if (historyDefaultTransition === stateNode.parent?.initial) {\n          statesForDefaultEntry.add(stateNode.parent);\n        }\n        addDescendantStatesToEnter(s, historyValue, statesForDefaultEntry, statesToEnter);\n      }\n      for (const s of historyDefaultTransition.target) {\n        addProperAncestorStatesToEnter(s, stateNode.parent, statesToEnter, historyValue, statesForDefaultEntry);\n      }\n    }\n  } else {\n    if (stateNode.type === 'compound') {\n      const [initialState] = stateNode.initial.target;\n      if (!isHistoryNode(initialState)) {\n        statesToEnter.add(initialState);\n        statesForDefaultEntry.add(initialState);\n      }\n      addDescendantStatesToEnter(initialState, historyValue, statesForDefaultEntry, statesToEnter);\n      addProperAncestorStatesToEnter(initialState, stateNode, statesToEnter, historyValue, statesForDefaultEntry);\n    } else {\n      if (stateNode.type === 'parallel') {\n        for (const child of getChildren(stateNode).filter(sn => !isHistoryNode(sn))) {\n          if (![...statesToEnter].some(s => isDescendant(s, child))) {\n            if (!isHistoryNode(child)) {\n              statesToEnter.add(child);\n              statesForDefaultEntry.add(child);\n            }\n            addDescendantStatesToEnter(child, historyValue, statesForDefaultEntry, statesToEnter);\n          }\n        }\n      }\n    }\n  }\n}\nfunction addAncestorStatesToEnter(statesToEnter, historyValue, statesForDefaultEntry, ancestors, reentrancyDomain) {\n  for (const anc of ancestors) {\n    if (!reentrancyDomain || isDescendant(anc, reentrancyDomain)) {\n      statesToEnter.add(anc);\n    }\n    if (anc.type === 'parallel') {\n      for (const child of getChildren(anc).filter(sn => !isHistoryNode(sn))) {\n        if (![...statesToEnter].some(s => isDescendant(s, child))) {\n          statesToEnter.add(child);\n          addDescendantStatesToEnter(child, historyValue, statesForDefaultEntry, statesToEnter);\n        }\n      }\n    }\n  }\n}\nfunction addProperAncestorStatesToEnter(stateNode, toStateNode, statesToEnter, historyValue, statesForDefaultEntry) {\n  addAncestorStatesToEnter(statesToEnter, historyValue, statesForDefaultEntry, getProperAncestors(stateNode, toStateNode));\n}\nfunction exitStates(currentSnapshot, event, actorScope, transitions, mutStateNodeSet, historyValue, internalQueue, _actionExecutor) {\n  let nextSnapshot = currentSnapshot;\n  const statesToExit = computeExitSet(transitions, mutStateNodeSet, historyValue);\n  statesToExit.sort((a, b) => b.order - a.order);\n  let changedHistory;\n\n  // From SCXML algorithm: https://www.w3.org/TR/scxml/#exitStates\n  for (const exitStateNode of statesToExit) {\n    for (const historyNode of getHistoryNodes(exitStateNode)) {\n      let predicate;\n      if (historyNode.history === 'deep') {\n        predicate = sn => isAtomicStateNode(sn) && isDescendant(sn, exitStateNode);\n      } else {\n        predicate = sn => {\n          return sn.parent === exitStateNode;\n        };\n      }\n      changedHistory ??= {\n        ...historyValue\n      };\n      changedHistory[historyNode.id] = Array.from(mutStateNodeSet).filter(predicate);\n    }\n  }\n  for (const s of statesToExit) {\n    nextSnapshot = resolveActionsAndContext(nextSnapshot, event, actorScope, [...s.exit, ...s.invoke.map(def => stopChild(def.id))], internalQueue, undefined);\n    mutStateNodeSet.delete(s);\n  }\n  return [nextSnapshot, changedHistory || historyValue];\n}\nfunction getAction(machine, actionType) {\n  return machine.implementations.actions[actionType];\n}\nfunction resolveAndExecuteActionsWithContext(currentSnapshot, event, actorScope, actions, extra, retries) {\n  const {\n    machine\n  } = currentSnapshot;\n  let intermediateSnapshot = currentSnapshot;\n  for (const action of actions) {\n    const isInline = typeof action === 'function';\n    const resolvedAction = isInline ? action :\n    // the existing type of `.actions` assumes non-nullable `TExpressionAction`\n    // it's fine to cast this here to get a common type and lack of errors in the rest of the code\n    // our logic below makes sure that we call those 2 \"variants\" correctly\n\n    getAction(machine, typeof action === 'string' ? action : action.type);\n    const actionArgs = {\n      context: intermediateSnapshot.context,\n      event,\n      self: actorScope.self,\n      system: actorScope.system\n    };\n    const actionParams = isInline || typeof action === 'string' ? undefined : 'params' in action ? typeof action.params === 'function' ? action.params({\n      context: intermediateSnapshot.context,\n      event\n    }) : action.params : undefined;\n    if (!resolvedAction || !('resolve' in resolvedAction)) {\n      actorScope.actionExecutor({\n        type: typeof action === 'string' ? action : typeof action === 'object' ? action.type : action.name || '(anonymous)',\n        info: actionArgs,\n        params: actionParams,\n        exec: resolvedAction\n      });\n      continue;\n    }\n    const builtinAction = resolvedAction;\n    const [nextState, params, actions] = builtinAction.resolve(actorScope, intermediateSnapshot, actionArgs, actionParams, resolvedAction,\n    // this holds all params\n    extra);\n    intermediateSnapshot = nextState;\n    if ('retryResolve' in builtinAction) {\n      retries?.push([builtinAction, params]);\n    }\n    if ('execute' in builtinAction) {\n      actorScope.actionExecutor({\n        type: builtinAction.type,\n        info: actionArgs,\n        params,\n        exec: builtinAction.execute.bind(null, actorScope, params)\n      });\n    }\n    if (actions) {\n      intermediateSnapshot = resolveAndExecuteActionsWithContext(intermediateSnapshot, event, actorScope, actions, extra, retries);\n    }\n  }\n  return intermediateSnapshot;\n}\nfunction resolveActionsAndContext(currentSnapshot, event, actorScope, actions, internalQueue, deferredActorIds) {\n  const retries = deferredActorIds ? [] : undefined;\n  const nextState = resolveAndExecuteActionsWithContext(currentSnapshot, event, actorScope, actions, {\n    internalQueue,\n    deferredActorIds\n  }, retries);\n  retries?.forEach(([builtinAction, params]) => {\n    builtinAction.retryResolve(actorScope, nextState, params);\n  });\n  return nextState;\n}\nfunction macrostep(snapshot, event, actorScope, internalQueue) {\n  if (event.type === WILDCARD) {\n    throw new Error(`An event cannot have the wildcard type ('${WILDCARD}')`);\n  }\n  let nextSnapshot = snapshot;\n  const microstates = [];\n  function addMicrostate(microstate, event, transitions) {\n    actorScope.system._sendInspectionEvent({\n      type: '@xstate.microstep',\n      actorRef: actorScope.self,\n      event,\n      snapshot: microstate,\n      _transitions: transitions\n    });\n    microstates.push(microstate);\n  }\n\n  // Handle stop event\n  if (event.type === XSTATE_STOP) {\n    nextSnapshot = cloneMachineSnapshot(stopChildren(nextSnapshot, event, actorScope), {\n      status: 'stopped'\n    });\n    addMicrostate(nextSnapshot, event, []);\n    return {\n      snapshot: nextSnapshot,\n      microstates\n    };\n  }\n  let nextEvent = event;\n\n  // Assume the state is at rest (no raised events)\n  // Determine the next state based on the next microstep\n  if (nextEvent.type !== XSTATE_INIT) {\n    const currentEvent = nextEvent;\n    const isErr = isErrorActorEvent(currentEvent);\n    const transitions = selectTransitions(currentEvent, nextSnapshot);\n    if (isErr && !transitions.length) {\n      // TODO: we should likely only allow transitions selected by very explicit descriptors\n      // `*` shouldn't be matched, likely `xstate.error.*` shouldn't be either\n      // similarly `xstate.error.actor.*` and `xstate.error.actor.todo.*` have to be considered too\n      nextSnapshot = cloneMachineSnapshot(snapshot, {\n        status: 'error',\n        error: currentEvent.error\n      });\n      addMicrostate(nextSnapshot, currentEvent, []);\n      return {\n        snapshot: nextSnapshot,\n        microstates\n      };\n    }\n    nextSnapshot = microstep(transitions, snapshot, actorScope, nextEvent, false,\n    // isInitial\n    internalQueue);\n    addMicrostate(nextSnapshot, currentEvent, transitions);\n  }\n  let shouldSelectEventlessTransitions = true;\n  while (nextSnapshot.status === 'active') {\n    let enabledTransitions = shouldSelectEventlessTransitions ? selectEventlessTransitions(nextSnapshot, nextEvent) : [];\n\n    // eventless transitions should always be selected after selecting *regular* transitions\n    // by assigning `undefined` to `previousState` we ensure that `shouldSelectEventlessTransitions` gets always computed to true in such a case\n    const previousState = enabledTransitions.length ? nextSnapshot : undefined;\n    if (!enabledTransitions.length) {\n      if (!internalQueue.length) {\n        break;\n      }\n      nextEvent = internalQueue.shift();\n      enabledTransitions = selectTransitions(nextEvent, nextSnapshot);\n    }\n    nextSnapshot = microstep(enabledTransitions, nextSnapshot, actorScope, nextEvent, false, internalQueue);\n    shouldSelectEventlessTransitions = nextSnapshot !== previousState;\n    addMicrostate(nextSnapshot, nextEvent, enabledTransitions);\n  }\n  if (nextSnapshot.status !== 'active') {\n    stopChildren(nextSnapshot, nextEvent, actorScope);\n  }\n  return {\n    snapshot: nextSnapshot,\n    microstates\n  };\n}\nfunction stopChildren(nextState, event, actorScope) {\n  return resolveActionsAndContext(nextState, event, actorScope, Object.values(nextState.children).map(child => stopChild(child)), [], undefined);\n}\nfunction selectTransitions(event, nextState) {\n  return nextState.machine.getTransitionData(nextState, event);\n}\nfunction selectEventlessTransitions(nextState, event) {\n  const enabledTransitionSet = new Set();\n  const atomicStates = nextState._nodes.filter(isAtomicStateNode);\n  for (const stateNode of atomicStates) {\n    loop: for (const s of [stateNode].concat(getProperAncestors(stateNode, undefined))) {\n      if (!s.always) {\n        continue;\n      }\n      for (const transition of s.always) {\n        if (transition.guard === undefined || evaluateGuard(transition.guard, nextState.context, event, nextState)) {\n          enabledTransitionSet.add(transition);\n          break loop;\n        }\n      }\n    }\n  }\n  return removeConflictingTransitions(Array.from(enabledTransitionSet), new Set(nextState._nodes), nextState.historyValue);\n}\n\n/**\n * Resolves a partial state value with its full representation in the state\n * node's machine.\n *\n * @param stateValue The partial state value to resolve.\n */\nfunction resolveStateValue(rootNode, stateValue) {\n  const allStateNodes = getAllStateNodes(getStateNodes(rootNode, stateValue));\n  return getStateValue(rootNode, [...allStateNodes]);\n}\n\nfunction isMachineSnapshot(value) {\n  return !!value && typeof value === 'object' && 'machine' in value && 'value' in value;\n}\nconst machineSnapshotMatches = function matches(testValue) {\n  return matchesState(testValue, this.value);\n};\nconst machineSnapshotHasTag = function hasTag(tag) {\n  return this.tags.has(tag);\n};\nconst machineSnapshotCan = function can(event) {\n  if (!this.machine) {\n    console.warn(`state.can(...) used outside of a machine-created State object; this will always return false.`);\n  }\n  const transitionData = this.machine.getTransitionData(this, event);\n  return !!transitionData?.length &&\n  // Check that at least one transition is not forbidden\n  transitionData.some(t => t.target !== undefined || t.actions.length);\n};\nconst machineSnapshotToJSON = function toJSON() {\n  const {\n    _nodes: nodes,\n    tags,\n    machine,\n    getMeta,\n    toJSON,\n    can,\n    hasTag,\n    matches,\n    ...jsonValues\n  } = this;\n  return {\n    ...jsonValues,\n    tags: Array.from(tags)\n  };\n};\nconst machineSnapshotGetMeta = function getMeta() {\n  return this._nodes.reduce((acc, stateNode) => {\n    if (stateNode.meta !== undefined) {\n      acc[stateNode.id] = stateNode.meta;\n    }\n    return acc;\n  }, {});\n};\nfunction createMachineSnapshot(config, machine) {\n  return {\n    status: config.status,\n    output: config.output,\n    error: config.error,\n    machine,\n    context: config.context,\n    _nodes: config._nodes,\n    value: getStateValue(machine.root, config._nodes),\n    tags: new Set(config._nodes.flatMap(sn => sn.tags)),\n    children: config.children,\n    historyValue: config.historyValue || {},\n    matches: machineSnapshotMatches,\n    hasTag: machineSnapshotHasTag,\n    can: machineSnapshotCan,\n    getMeta: machineSnapshotGetMeta,\n    toJSON: machineSnapshotToJSON\n  };\n}\nfunction cloneMachineSnapshot(snapshot, config = {}) {\n  return createMachineSnapshot({\n    ...snapshot,\n    ...config\n  }, snapshot.machine);\n}\nfunction serializeHistoryValue(historyValue) {\n  if (typeof historyValue !== 'object' || historyValue === null) {\n    return {};\n  }\n  const result = {};\n  for (const key in historyValue) {\n    const value = historyValue[key];\n    if (Array.isArray(value)) {\n      result[key] = value.map(item => ({\n        id: item.id\n      }));\n    }\n  }\n  return result;\n}\nfunction getPersistedSnapshot(snapshot, options) {\n  const {\n    _nodes: nodes,\n    tags,\n    machine,\n    children,\n    context,\n    can,\n    hasTag,\n    matches,\n    getMeta,\n    toJSON,\n    ...jsonValues\n  } = snapshot;\n  const childrenJson = {};\n  for (const id in children) {\n    const child = children[id];\n    if (typeof child.src !== 'string' && (!options || !('__unsafeAllowInlineActors' in options))) {\n      throw new Error('An inline child actor cannot be persisted.');\n    }\n    childrenJson[id] = {\n      snapshot: child.getPersistedSnapshot(options),\n      src: child.src,\n      systemId: child._systemId,\n      syncSnapshot: child._syncSnapshot\n    };\n  }\n  const persisted = {\n    ...jsonValues,\n    context: persistContext(context),\n    children: childrenJson,\n    historyValue: serializeHistoryValue(jsonValues.historyValue)\n  };\n  return persisted;\n}\nfunction persistContext(contextPart) {\n  let copy;\n  for (const key in contextPart) {\n    const value = contextPart[key];\n    if (value && typeof value === 'object') {\n      if ('sessionId' in value && 'send' in value && 'ref' in value) {\n        copy ??= Array.isArray(contextPart) ? contextPart.slice() : {\n          ...contextPart\n        };\n        copy[key] = {\n          xstate$$type: $$ACTOR_TYPE,\n          id: value.id\n        };\n      } else {\n        const result = persistContext(value);\n        if (result !== value) {\n          copy ??= Array.isArray(contextPart) ? contextPart.slice() : {\n            ...contextPart\n          };\n          copy[key] = result;\n        }\n      }\n    }\n  }\n  return copy ?? contextPart;\n}\n\nfunction resolveRaise(_, snapshot, args, actionParams, {\n  event: eventOrExpr,\n  id,\n  delay\n}, {\n  internalQueue\n}) {\n  const delaysMap = snapshot.machine.implementations.delays;\n  if (typeof eventOrExpr === 'string') {\n    throw new Error(\n    // eslint-disable-next-line @typescript-eslint/restrict-template-expressions\n    `Only event objects may be used with raise; use raise({ type: \"${eventOrExpr}\" }) instead`);\n  }\n  const resolvedEvent = typeof eventOrExpr === 'function' ? eventOrExpr(args, actionParams) : eventOrExpr;\n  let resolvedDelay;\n  if (typeof delay === 'string') {\n    const configDelay = delaysMap && delaysMap[delay];\n    resolvedDelay = typeof configDelay === 'function' ? configDelay(args, actionParams) : configDelay;\n  } else {\n    resolvedDelay = typeof delay === 'function' ? delay(args, actionParams) : delay;\n  }\n  if (typeof resolvedDelay !== 'number') {\n    internalQueue.push(resolvedEvent);\n  }\n  return [snapshot, {\n    event: resolvedEvent,\n    id,\n    delay: resolvedDelay\n  }, undefined];\n}\nfunction executeRaise(actorScope, params) {\n  const {\n    event,\n    delay,\n    id\n  } = params;\n  if (typeof delay === 'number') {\n    actorScope.defer(() => {\n      const self = actorScope.self;\n      actorScope.system.scheduler.schedule(self, self, event, delay, id);\n    });\n    return;\n  }\n}\n/**\n * Raises an event. This places the event in the internal event queue, so that\n * the event is immediately consumed by the machine in the current step.\n *\n * @param eventType The event to raise.\n */\nfunction raise(eventOrExpr, options) {\n  if (executingCustomAction) {\n    console.warn('Custom actions should not call `raise()` directly, as it is not imperative. See https://stately.ai/docs/actions#built-in-actions for more details.');\n  }\n  function raise(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  raise.type = 'xstate.raise';\n  raise.event = eventOrExpr;\n  raise.id = options?.id;\n  raise.delay = options?.delay;\n  raise.resolve = resolveRaise;\n  raise.execute = executeRaise;\n  return raise;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xstate/dist/raise-78b8dcb8.development.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xstate/dist/xstate.development.esm.js":
/*!************************************************************!*\
  !*** ./node_modules/xstate/dist/xstate.development.esm.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Actor: () => (/* reexport safe */ _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.A),\n/* harmony export */   SimulatedClock: () => (/* binding */ SimulatedClock),\n/* harmony export */   SpecialTargets: () => (/* reexport safe */ _log_ef959da6_development_esm_js__WEBPACK_IMPORTED_MODULE_3__.S),\n/* harmony export */   StateMachine: () => (/* reexport safe */ _StateMachine_b4e94439_development_esm_js__WEBPACK_IMPORTED_MODULE_2__.S),\n/* harmony export */   StateNode: () => (/* reexport safe */ _StateMachine_b4e94439_development_esm_js__WEBPACK_IMPORTED_MODULE_2__.a),\n/* harmony export */   __unsafe_getAllOwnEventDescriptors: () => (/* reexport safe */ _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.d),\n/* harmony export */   and: () => (/* reexport safe */ _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.a),\n/* harmony export */   assertEvent: () => (/* binding */ assertEvent),\n/* harmony export */   assign: () => (/* reexport safe */ _assign_6313ccb3_development_esm_js__WEBPACK_IMPORTED_MODULE_4__.a),\n/* harmony export */   cancel: () => (/* reexport safe */ _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.f),\n/* harmony export */   createActor: () => (/* reexport safe */ _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.c),\n/* harmony export */   createEmptyActor: () => (/* reexport safe */ _actors_dist_xstate_actors_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.createEmptyActor),\n/* harmony export */   createMachine: () => (/* binding */ createMachine),\n/* harmony export */   emit: () => (/* reexport safe */ _log_ef959da6_development_esm_js__WEBPACK_IMPORTED_MODULE_3__.e),\n/* harmony export */   enqueueActions: () => (/* reexport safe */ _log_ef959da6_development_esm_js__WEBPACK_IMPORTED_MODULE_3__.a),\n/* harmony export */   forwardTo: () => (/* reexport safe */ _log_ef959da6_development_esm_js__WEBPACK_IMPORTED_MODULE_3__.f),\n/* harmony export */   fromCallback: () => (/* reexport safe */ _actors_dist_xstate_actors_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.fromCallback),\n/* harmony export */   fromEventObservable: () => (/* reexport safe */ _actors_dist_xstate_actors_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.fromEventObservable),\n/* harmony export */   fromObservable: () => (/* reexport safe */ _actors_dist_xstate_actors_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.fromObservable),\n/* harmony export */   fromPromise: () => (/* reexport safe */ _actors_dist_xstate_actors_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.fromPromise),\n/* harmony export */   fromTransition: () => (/* reexport safe */ _actors_dist_xstate_actors_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.fromTransition),\n/* harmony export */   getInitialSnapshot: () => (/* binding */ getInitialSnapshot),\n/* harmony export */   getNextSnapshot: () => (/* binding */ getNextSnapshot),\n/* harmony export */   getStateNodes: () => (/* reexport safe */ _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.g),\n/* harmony export */   initialTransition: () => (/* binding */ initialTransition),\n/* harmony export */   interpret: () => (/* reexport safe */ _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.i),\n/* harmony export */   isMachineSnapshot: () => (/* reexport safe */ _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.b),\n/* harmony export */   log: () => (/* reexport safe */ _log_ef959da6_development_esm_js__WEBPACK_IMPORTED_MODULE_3__.l),\n/* harmony export */   matchesState: () => (/* reexport safe */ _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.m),\n/* harmony export */   not: () => (/* reexport safe */ _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.n),\n/* harmony export */   or: () => (/* reexport safe */ _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.o),\n/* harmony export */   pathToStateValue: () => (/* reexport safe */ _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.p),\n/* harmony export */   raise: () => (/* reexport safe */ _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.r),\n/* harmony export */   sendParent: () => (/* reexport safe */ _log_ef959da6_development_esm_js__WEBPACK_IMPORTED_MODULE_3__.s),\n/* harmony export */   sendTo: () => (/* reexport safe */ _log_ef959da6_development_esm_js__WEBPACK_IMPORTED_MODULE_3__.b),\n/* harmony export */   setup: () => (/* binding */ setup),\n/* harmony export */   spawnChild: () => (/* reexport safe */ _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.h),\n/* harmony export */   stateIn: () => (/* reexport safe */ _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.s),\n/* harmony export */   stop: () => (/* reexport safe */ _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.j),\n/* harmony export */   stopChild: () => (/* reexport safe */ _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.k),\n/* harmony export */   toObserver: () => (/* reexport safe */ _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.e),\n/* harmony export */   toPromise: () => (/* binding */ toPromise),\n/* harmony export */   transition: () => (/* binding */ transition),\n/* harmony export */   waitFor: () => (/* binding */ waitFor)\n/* harmony export */ });\n/* harmony import */ var _actors_dist_xstate_actors_development_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actors/dist/xstate-actors.development.esm.js */ \"(ssr)/./node_modules/xstate/actors/dist/xstate-actors.development.esm.js\");\n/* harmony import */ var _raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./raise-78b8dcb8.development.esm.js */ \"(ssr)/./node_modules/xstate/dist/raise-78b8dcb8.development.esm.js\");\n/* harmony import */ var _StateMachine_b4e94439_development_esm_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./StateMachine-b4e94439.development.esm.js */ \"(ssr)/./node_modules/xstate/dist/StateMachine-b4e94439.development.esm.js\");\n/* harmony import */ var _log_ef959da6_development_esm_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./log-ef959da6.development.esm.js */ \"(ssr)/./node_modules/xstate/dist/log-ef959da6.development.esm.js\");\n/* harmony import */ var _assign_6313ccb3_development_esm_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./assign-6313ccb3.development.esm.js */ \"(ssr)/./node_modules/xstate/dist/assign-6313ccb3.development.esm.js\");\n/* harmony import */ var _dev_dist_xstate_dev_development_esm_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../dev/dist/xstate-dev.development.esm.js */ \"(ssr)/./node_modules/xstate/dev/dist/xstate-dev.development.esm.js\");\n\n\n\n\n\n\n\n\n\n/**\n * Asserts that the given event object is of the specified type or types. Throws\n * an error if the event object is not of the specified types.\n *\n * @example\n *\n * ```ts\n * // ...\n * entry: ({ event }) => {\n *   assertEvent(event, 'doNothing');\n *   // event is { type: 'doNothing' }\n * },\n * // ...\n * exit: ({ event }) => {\n *   assertEvent(event, 'greet');\n *   // event is { type: 'greet'; message: string }\n *\n *   assertEvent(event, ['greet', 'notify']);\n *   // event is { type: 'greet'; message: string }\n *   // or { type: 'notify'; message: string; level: 'info' | 'error' }\n * },\n * ```\n */\nfunction assertEvent(event, type) {\n  const types = (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.t)(type);\n  if (!types.includes(event.type)) {\n    const typesText = types.length === 1 ? `type \"${types[0]}\"` : `one of types \"${types.join('\", \"')}\"`;\n    throw new Error(`Expected event ${JSON.stringify(event)} to have ${typesText}`);\n  }\n}\n\n/**\n * Creates a state machine (statechart) with the given configuration.\n *\n * The state machine represents the pure logic of a state machine actor.\n *\n * @example\n *\n * ```ts\n * import { createMachine } from 'xstate';\n *\n * const lightMachine = createMachine({\n *   id: 'light',\n *   initial: 'green',\n *   states: {\n *     green: {\n *       on: {\n *         TIMER: { target: 'yellow' }\n *       }\n *     },\n *     yellow: {\n *       on: {\n *         TIMER: { target: 'red' }\n *       }\n *     },\n *     red: {\n *       on: {\n *         TIMER: { target: 'green' }\n *       }\n *     }\n *   }\n * });\n *\n * const lightActor = createActor(lightMachine);\n * lightActor.start();\n *\n * lightActor.send({ type: 'TIMER' });\n * ```\n *\n * @param config The state machine configuration.\n * @param options DEPRECATED: use `setup({ ... })` or `machine.provide({ ... })`\n *   to provide machine implementations instead.\n */\nfunction createMachine(config, implementations) {\n  return new _StateMachine_b4e94439_development_esm_js__WEBPACK_IMPORTED_MODULE_2__.S(config, implementations);\n}\n\n/** @internal */\nfunction createInertActorScope(actorLogic) {\n  const self = (0,_raise_78b8dcb8_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.c)(actorLogic);\n  const inertActorScope = {\n    self,\n    defer: () => {},\n    id: '',\n    logger: () => {},\n    sessionId: '',\n    stopChild: () => {},\n    system: self.system,\n    emit: () => {},\n    actionExecutor: () => {}\n  };\n  return inertActorScope;\n}\n\n/** @deprecated Use `initialTransition(…)` instead. */\nfunction getInitialSnapshot(actorLogic, ...[input]) {\n  const actorScope = createInertActorScope(actorLogic);\n  return actorLogic.getInitialSnapshot(actorScope, input);\n}\n\n/**\n * Determines the next snapshot for the given `actorLogic` based on the given\n * `snapshot` and `event`.\n *\n * If the `snapshot` is `undefined`, the initial snapshot of the `actorLogic` is\n * used.\n *\n * @deprecated Use `transition(…)` instead.\n * @example\n *\n * ```ts\n * import { getNextSnapshot } from 'xstate';\n * import { trafficLightMachine } from './trafficLightMachine.ts';\n *\n * const nextSnapshot = getNextSnapshot(\n *   trafficLightMachine, // actor logic\n *   undefined, // snapshot (or initial state if undefined)\n *   { type: 'TIMER' }\n * ); // event object\n *\n * console.log(nextSnapshot.value);\n * // => 'yellow'\n *\n * const nextSnapshot2 = getNextSnapshot(\n *   trafficLightMachine, // actor logic\n *   nextSnapshot, // snapshot\n *   { type: 'TIMER' }\n * ); // event object\n *\n * console.log(nextSnapshot2.value);\n * // =>'red'\n * ```\n */\nfunction getNextSnapshot(actorLogic, snapshot, event) {\n  const inertActorScope = createInertActorScope(actorLogic);\n  inertActorScope.self._snapshot = snapshot;\n  return actorLogic.transition(snapshot, event, inertActorScope);\n}\n\n// at the moment we allow extra actors - ones that are not specified by `children`\n// this could be reconsidered in the future\n\nfunction setup({\n  schemas,\n  actors,\n  actions,\n  guards,\n  delays\n}) {\n  return {\n    createMachine: config => createMachine({\n      ...config,\n      schemas\n    }, {\n      actors,\n      actions,\n      guards,\n      delays\n    })\n  };\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-declaration-merging\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-declaration-merging\nclass SimulatedClock {\n  constructor() {\n    this.timeouts = new Map();\n    this._now = 0;\n    this._id = 0;\n    this._flushing = false;\n    this._flushingInvalidated = false;\n  }\n  now() {\n    return this._now;\n  }\n  getId() {\n    return this._id++;\n  }\n  setTimeout(fn, timeout) {\n    this._flushingInvalidated = this._flushing;\n    const id = this.getId();\n    this.timeouts.set(id, {\n      start: this.now(),\n      timeout,\n      fn\n    });\n    return id;\n  }\n  clearTimeout(id) {\n    this._flushingInvalidated = this._flushing;\n    this.timeouts.delete(id);\n  }\n  set(time) {\n    if (this._now > time) {\n      throw new Error('Unable to travel back in time');\n    }\n    this._now = time;\n    this.flushTimeouts();\n  }\n  flushTimeouts() {\n    if (this._flushing) {\n      this._flushingInvalidated = true;\n      return;\n    }\n    this._flushing = true;\n    const sorted = [...this.timeouts].sort(([_idA, timeoutA], [_idB, timeoutB]) => {\n      const endA = timeoutA.start + timeoutA.timeout;\n      const endB = timeoutB.start + timeoutB.timeout;\n      return endB > endA ? -1 : 1;\n    });\n    for (const [id, timeout] of sorted) {\n      if (this._flushingInvalidated) {\n        this._flushingInvalidated = false;\n        this._flushing = false;\n        this.flushTimeouts();\n        return;\n      }\n      if (this.now() - timeout.start >= timeout.timeout) {\n        this.timeouts.delete(id);\n        timeout.fn.call(null);\n      }\n    }\n    this._flushing = false;\n  }\n  increment(ms) {\n    this._now += ms;\n    this.flushTimeouts();\n  }\n}\n\n/**\n * Returns a promise that resolves to the `output` of the actor when it is done.\n *\n * @example\n *\n * ```ts\n * const machine = createMachine({\n *   // ...\n *   output: {\n *     count: 42\n *   }\n * });\n *\n * const actor = createActor(machine);\n *\n * actor.start();\n *\n * const output = await toPromise(actor);\n *\n * console.log(output);\n * // logs { count: 42 }\n * ```\n */\nfunction toPromise(actor) {\n  return new Promise((resolve, reject) => {\n    actor.subscribe({\n      complete: () => {\n        resolve(actor.getSnapshot().output);\n      },\n      error: reject\n    });\n  });\n}\n\n/**\n * Given actor `logic`, a `snapshot`, and an `event`, returns a tuple of the\n * `nextSnapshot` and `actions` to execute.\n *\n * This is a pure function that does not execute `actions`.\n */\nfunction transition(logic, snapshot, event) {\n  const executableActions = [];\n  const actorScope = createInertActorScope(logic);\n  actorScope.actionExecutor = action => {\n    executableActions.push(action);\n  };\n  const nextSnapshot = logic.transition(snapshot, event, actorScope);\n  return [nextSnapshot, executableActions];\n}\n\n/**\n * Given actor `logic` and optional `input`, returns a tuple of the\n * `nextSnapshot` and `actions` to execute from the initial transition (no\n * previous state).\n *\n * This is a pure function that does not execute `actions`.\n */\nfunction initialTransition(logic, ...[input]) {\n  const executableActions = [];\n  const actorScope = createInertActorScope(logic);\n  actorScope.actionExecutor = action => {\n    executableActions.push(action);\n  };\n  const nextSnapshot = logic.getInitialSnapshot(actorScope, input);\n  return [nextSnapshot, executableActions];\n}\n\nconst defaultWaitForOptions = {\n  timeout: Infinity // much more than 10 seconds\n};\n\n/**\n * Subscribes to an actor ref and waits for its emitted value to satisfy a\n * predicate, and then resolves with that value. Will throw if the desired state\n * is not reached after an optional timeout. (defaults to Infinity).\n *\n * @example\n *\n * ```js\n * const state = await waitFor(someService, (state) => {\n *   return state.hasTag('loaded');\n * });\n *\n * state.hasTag('loaded'); // true\n * ```\n *\n * @param actorRef The actor ref to subscribe to\n * @param predicate Determines if a value matches the condition to wait for\n * @param options\n * @returns A promise that eventually resolves to the emitted value that matches\n *   the condition\n */\nfunction waitFor(actorRef, predicate, options) {\n  const resolvedOptions = {\n    ...defaultWaitForOptions,\n    ...options\n  };\n  return new Promise((res, rej) => {\n    const {\n      signal\n    } = resolvedOptions;\n    if (signal?.aborted) {\n      // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\n      rej(signal.reason);\n      return;\n    }\n    let done = false;\n    if (resolvedOptions.timeout < 0) {\n      console.error('`timeout` passed to `waitFor` is negative and it will reject its internal promise immediately.');\n    }\n    const handle = resolvedOptions.timeout === Infinity ? undefined : setTimeout(() => {\n      dispose();\n      rej(new Error(`Timeout of ${resolvedOptions.timeout} ms exceeded`));\n    }, resolvedOptions.timeout);\n    const dispose = () => {\n      clearTimeout(handle);\n      done = true;\n      sub?.unsubscribe();\n      if (abortListener) {\n        signal.removeEventListener('abort', abortListener);\n      }\n    };\n    function checkEmitted(emitted) {\n      if (predicate(emitted)) {\n        dispose();\n        res(emitted);\n      }\n    }\n\n    /**\n     * If the `signal` option is provided, this will be the listener for its\n     * `abort` event\n     */\n    let abortListener;\n    // eslint-disable-next-line prefer-const\n    let sub; // avoid TDZ when disposing synchronously\n\n    // See if the current snapshot already matches the predicate\n    checkEmitted(actorRef.getSnapshot());\n    if (done) {\n      return;\n    }\n\n    // only define the `abortListener` if the `signal` option is provided\n    if (signal) {\n      abortListener = () => {\n        dispose();\n        // XState does not \"own\" the signal, so we should reject with its reason (if any)\n        // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\n        rej(signal.reason);\n      };\n      signal.addEventListener('abort', abortListener);\n    }\n    sub = actorRef.subscribe({\n      next: checkEmitted,\n      error: err => {\n        dispose();\n        // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\n        rej(err);\n      },\n      complete: () => {\n        dispose();\n        rej(new Error(`Actor terminated without satisfying predicate`));\n      }\n    });\n    if (done) {\n      sub.unsubscribe();\n    }\n  });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xstate/dist/xstate.development.esm.js\n");

/***/ })

};
;